# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
venv/
env/
ENV/
.env
.venv
env.bak/
venv.bak/

# IDE - VSCode
.vscode/
*.code-workspace
.history

# IDE - PyCharm
.idea/
*.iml
*.iws
.idea_modules/

# IDE - Jupyter Notebook
.ipynb_checkpoints
*/.ipynb_checkpoints/*
profile_default/
ipython_config.py

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Logs
*.log
logs/
log/

# Database
*.db
*.sqlite3
*.sqlite

# Environment variables
.env
.env.local
.env.*.local

# Operating System
.DS_Store
Thumbs.db
*.swp
*.swo
*~

# Project specific
data/
output/
results/
temp/
tmp/