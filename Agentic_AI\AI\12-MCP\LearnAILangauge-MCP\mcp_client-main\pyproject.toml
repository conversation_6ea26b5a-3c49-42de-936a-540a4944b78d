[project]
name = "mcp-client"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "google-genai>=1.5.0",
    "google-generativeai>=0.8.4",
    "langchain>=0.3.21",
    "langchain-google-genai>=2.0.10",
    "langchain-mcp-adapters>=0.0.5",
    "langgraph>=0.3.18",
    "mcp>=1.4.1",
    "nest-asyncio>=1.6.0",
    "pillow>=11.1.0",
    "python-dotenv>=1.0.1",
    "streamlit>=1.44.1",
]
[tool.setuptools]
py-modules = ["client_sse"]
