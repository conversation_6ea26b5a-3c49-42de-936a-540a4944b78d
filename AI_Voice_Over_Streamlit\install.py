#!/usr/bin/env python3
"""
Installation script for Advanced AI Voice-Over Streamlit Application
Handles step-by-step installation with error handling
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"📦 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed:")
        print(f"   Error: {e.stderr}")
        return False

def install_core_packages():
    """Install core packages first"""
    core_packages = [
        "streamlit>=1.28.0",
        "numpy>=1.21.0",
        "pandas>=1.5.0",
        "requests>=2.28.0",
        "python-dateutil>=2.8.0",
        "pillow>=9.0.0",
        "tqdm>=4.64.0",
        "python-dotenv>=0.19.0"
    ]
    
    print("🔧 Installing core packages...")
    for package in core_packages:
        if not run_command(f"pip install {package}", f"Installing {package.split('>=')[0]}"):
            return False
    return True

def install_azure_packages():
    """Install Azure packages"""
    azure_packages = [
        "azure-cognitiveservices-speech==1.34.0",
        "azure-storage-blob==12.19.0",
        "azure-identity==1.15.0",
        "azure-mgmt-storage==21.1.0"
    ]
    
    print("☁️ Installing Azure packages...")
    for package in azure_packages:
        if not run_command(f"pip install {package}", f"Installing {package.split('==')[0]}"):
            print(f"⚠️ Failed to install {package}, continuing...")
    return True

def install_audio_packages():
    """Install audio processing packages"""
    print("🎵 Installing audio processing packages...")
    
    # Try to install audio packages one by one
    audio_packages = [
        ("moviepy>=1.0.3", "MoviePy for video processing"),
        ("pydub>=0.25.1", "Pydub for audio manipulation"),
        ("scipy>=1.9.0", "SciPy for signal processing"),
        ("plotly>=5.15.0", "Plotly for visualization"),
        ("matplotlib>=3.7.0", "Matplotlib for plotting")
    ]
    
    for package, description in audio_packages:
        run_command(f"pip install {package}", description)
    
    # Try advanced audio packages (optional)
    advanced_packages = [
        ("librosa>=0.9.0", "Librosa for audio analysis"),
        ("soundfile>=0.12.0", "SoundFile for audio I/O"),
        ("noisereduce>=2.0.0", "NoiseReduce for audio enhancement")
    ]
    
    print("🔬 Installing advanced audio packages (optional)...")
    for package, description in advanced_packages:
        if not run_command(f"pip install {package}", description):
            print(f"⚠️ {description} failed - some features may be limited")
    
    return True

def check_ffmpeg():
    """Check if FFmpeg is available"""
    print("🎬 Checking FFmpeg installation...")
    try:
        result = subprocess.run(["ffmpeg", "-version"], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ FFmpeg is installed and available")
            return True
    except FileNotFoundError:
        pass
    
    print("⚠️ FFmpeg not found!")
    print("   FFmpeg is required for video processing.")
    print("   Please install FFmpeg:")
    print("   - Windows: Download from https://ffmpeg.org/download.html")
    print("   - macOS: brew install ffmpeg")
    print("   - Linux: sudo apt install ffmpeg")
    return False

def create_directories():
    """Create necessary directories"""
    print("📁 Creating directories...")
    directories = ["temp", "output", "projects", ".cache"]
    
    for directory in directories:
        dir_path = Path(directory)
        dir_path.mkdir(exist_ok=True)
        print(f"   ✓ {directory}/")
    
    print("✅ Directories created")
    return True

def main():
    """Main installation function"""
    print("🎬 Advanced AI Voice-Over System - Installation")
    print("=" * 60)
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        print(f"   Current version: {sys.version}")
        sys.exit(1)
    
    print(f"✅ Python {sys.version.split()[0]} detected")
    
    # Install packages step by step
    success = True
    
    # Core packages
    if not install_core_packages():
        print("❌ Failed to install core packages")
        success = False
    
    # Azure packages
    if not install_azure_packages():
        print("⚠️ Some Azure packages failed to install")
    
    # Audio packages
    if not install_audio_packages():
        print("⚠️ Some audio packages failed to install")
    
    # Check FFmpeg
    ffmpeg_available = check_ffmpeg()
    
    # Create directories
    create_directories()
    
    print("\n" + "=" * 60)
    
    if success:
        print("🎉 Installation completed!")
        print("\n📋 Next steps:")
        print("1. Ensure your Azure credentials are configured in the parent AI_Voice_Over_Azure system")
        print("2. Run the application with: python run_app.py")
        print("3. Or directly with: streamlit run app.py")
        
        if not ffmpeg_available:
            print("\n⚠️ Note: Install FFmpeg for full functionality")
    else:
        print("⚠️ Installation completed with some issues")
        print("   You may need to install missing packages manually")
    
    print("\n🔗 For help, see README.md")

if __name__ == "__main__":
    main()
