#!/usr/bin/env python3
"""
Enhanced Audio Processor with Timestamp Tracking
Extends the existing audio processing capabilities with precise timeline management
"""

import os
import sys
import logging
import numpy as np
import librosa
import soundfile as sf
from typing import List, Dict, Tuple, Optional
from pathlib import Path
from moviepy.editor import VideoFileClip, AudioFileClip
import json
from datetime import timedelta

# Add parent directory to import existing Azure functionality
parent_dir = Path(__file__).parent.parent.parent
sys.path.append(str(parent_dir / "AI_Voice_Over_Azure"))

try:
    from azure_voice_converter import AzureVoiceConverter
    from config import *
except ImportError as e:
    logging.error(f"Could not import Azure modules: {e}")

class AudioSegment:
    """Represents an audio segment with timestamp information"""
    
    def __init__(self, start_time: float, end_time: float, audio_data: np.ndarray, 
                 sample_rate: int, text: str = "", confidence: float = 0.0):
        self.start_time = start_time
        self.end_time = end_time
        self.duration = end_time - start_time
        self.audio_data = audio_data
        self.sample_rate = sample_rate
        self.text = text
        self.confidence = confidence
        self.is_modified = False
        
    def to_dict(self) -> Dict:
        """Convert segment to dictionary for serialization"""
        return {
            'start_time': self.start_time,
            'end_time': self.end_time,
            'duration': self.duration,
            'text': self.text,
            'confidence': self.confidence,
            'is_modified': self.is_modified,
            'sample_rate': self.sample_rate
        }
    
    def get_time_formatted(self) -> Tuple[str, str]:
        """Get formatted time strings"""
        start = str(timedelta(seconds=self.start_time))
        end = str(timedelta(seconds=self.end_time))
        return start, end

class EnhancedAudioProcessor:
    """Enhanced audio processor with timestamp tracking and segmentation"""
    
    def __init__(self, azure_converter: AzureVoiceConverter = None):
        self.logger = logging.getLogger(__name__)
        self.azure_converter = azure_converter or AzureVoiceConverter()
        self.segments: List[AudioSegment] = []
        self.original_audio_path = None
        self.sample_rate = 16000  # Standard for speech processing
        
    def extract_audio_with_timestamps(self, video_path: str, output_dir: str) -> Dict:
        """
        Extract audio from video with detailed timestamp information
        
        Args:
            video_path: Path to input video file
            output_dir: Directory to save extracted audio and metadata
            
        Returns:
            Dictionary containing audio file path and segment information
        """
        try:
            self.logger.info(f"Extracting audio with timestamps from: {video_path}")
            
            # Create output directory
            os.makedirs(output_dir, exist_ok=True)
            
            # Extract audio using moviepy
            video = VideoFileClip(video_path)
            if video.audio is None:
                raise ValueError("Video file has no audio track")
            
            # Save full audio file
            audio_filename = "extracted_audio.wav"
            audio_path = os.path.join(output_dir, audio_filename)
            
            video.audio.write_audiofile(
                audio_path,
                verbose=False,
                logger=None,
                codec='pcm_s16le',
                ffmpeg_params=['-ar', str(self.sample_rate), '-ac', '1']
            )
            
            self.original_audio_path = audio_path
            
            # Load audio for analysis
            audio_data, sr = librosa.load(audio_path, sr=self.sample_rate)
            
            # Detect speech segments using energy-based segmentation
            segments_info = self.detect_speech_segments(audio_data, sr)
            
            # Create audio segments
            self.segments = []
            for i, (start_time, end_time) in enumerate(segments_info):
                start_sample = int(start_time * sr)
                end_sample = int(end_time * sr)
                segment_audio = audio_data[start_sample:end_sample]
                
                segment = AudioSegment(
                    start_time=start_time,
                    end_time=end_time,
                    audio_data=segment_audio,
                    sample_rate=sr,
                    text=f"Segment {i+1}",  # Will be filled by transcription
                    confidence=0.0
                )
                self.segments.append(segment)
            
            # Save segment metadata
            metadata = {
                'total_duration': float(video.duration),
                'sample_rate': self.sample_rate,
                'num_segments': len(self.segments),
                'segments': [seg.to_dict() for seg in self.segments]
            }
            
            metadata_path = os.path.join(output_dir, "audio_metadata.json")
            with open(metadata_path, 'w') as f:
                json.dump(metadata, f, indent=2)
            
            video.close()
            
            self.logger.info(f"Audio extracted successfully: {len(self.segments)} segments found")
            
            return {
                'audio_path': audio_path,
                'metadata_path': metadata_path,
                'segments': self.segments,
                'total_duration': video.duration
            }
            
        except Exception as e:
            self.logger.error(f"Error extracting audio with timestamps: {str(e)}")
            raise
    
    def detect_speech_segments(self, audio_data: np.ndarray, sample_rate: int, 
                             min_segment_length: float = 1.0, 
                             silence_threshold: float = 0.01) -> List[Tuple[float, float]]:
        """
        Detect speech segments in audio using energy-based analysis
        
        Args:
            audio_data: Audio signal as numpy array
            sample_rate: Sample rate of audio
            min_segment_length: Minimum segment length in seconds
            silence_threshold: Threshold for silence detection
            
        Returns:
            List of (start_time, end_time) tuples for each segment
        """
        try:
            # Calculate frame-wise energy
            frame_length = int(0.025 * sample_rate)  # 25ms frames
            hop_length = int(0.010 * sample_rate)    # 10ms hop
            
            # Compute RMS energy
            rms = librosa.feature.rms(
                y=audio_data, 
                frame_length=frame_length, 
                hop_length=hop_length
            )[0]
            
            # Convert to time axis
            times = librosa.frames_to_time(
                np.arange(len(rms)), 
                sr=sample_rate, 
                hop_length=hop_length
            )
            
            # Detect speech/silence
            is_speech = rms > silence_threshold
            
            # Find segment boundaries
            segments = []
            in_speech = False
            segment_start = 0
            
            for i, speech in enumerate(is_speech):
                if speech and not in_speech:
                    # Start of speech segment
                    segment_start = times[i]
                    in_speech = True
                elif not speech and in_speech:
                    # End of speech segment
                    segment_end = times[i]
                    if segment_end - segment_start >= min_segment_length:
                        segments.append((segment_start, segment_end))
                    in_speech = False
            
            # Handle case where audio ends during speech
            if in_speech:
                segments.append((segment_start, times[-1]))
            
            # Merge nearby segments
            merged_segments = self.merge_nearby_segments(segments, gap_threshold=0.5)
            
            return merged_segments
            
        except Exception as e:
            self.logger.error(f"Error detecting speech segments: {str(e)}")
            # Fallback: create segments every 10 seconds
            duration = len(audio_data) / sample_rate
            return [(i, min(i + 10, duration)) for i in range(0, int(duration), 10)]
    
    def merge_nearby_segments(self, segments: List[Tuple[float, float]], 
                            gap_threshold: float = 0.5) -> List[Tuple[float, float]]:
        """Merge segments that are close together"""
        if not segments:
            return []
        
        merged = [segments[0]]
        
        for start, end in segments[1:]:
            last_end = merged[-1][1]
            if start - last_end <= gap_threshold:
                # Merge with previous segment
                merged[-1] = (merged[-1][0], end)
            else:
                merged.append((start, end))
        
        return merged
    
    def transcribe_segments_with_azure(self, output_dir: str) -> Dict:
        """
        Transcribe each audio segment using Azure Speech Services
        
        Args:
            output_dir: Directory containing audio files
            
        Returns:
            Dictionary with transcription results
        """
        try:
            self.logger.info("Transcribing audio segments with Azure Speech Services")
            
            if not self.original_audio_path:
                raise ValueError("No audio file available for transcription")
            
            # Transcribe full audio first
            full_transcript = self.azure_converter.transcribe_audio_azure(self.original_audio_path)
            
            if not full_transcript:
                raise ValueError("Failed to transcribe audio")
            
            # For now, distribute transcript across segments
            # In a more advanced implementation, we would use Azure's detailed transcription
            words = full_transcript.split()
            words_per_segment = max(1, len(words) // len(self.segments))
            
            transcription_results = {
                'full_transcript': full_transcript,
                'segments': []
            }
            
            for i, segment in enumerate(self.segments):
                start_word = i * words_per_segment
                end_word = min((i + 1) * words_per_segment, len(words))
                segment_text = ' '.join(words[start_word:end_word])
                
                segment.text = segment_text
                segment.confidence = 0.9  # Placeholder confidence
                
                transcription_results['segments'].append({
                    'segment_id': i,
                    'start_time': segment.start_time,
                    'end_time': segment.end_time,
                    'text': segment_text,
                    'confidence': segment.confidence
                })
            
            # Save transcription results
            transcript_path = os.path.join(output_dir, "transcription.json")
            with open(transcript_path, 'w') as f:
                json.dump(transcription_results, f, indent=2)
            
            self.logger.info("Transcription completed successfully")
            return transcription_results
            
        except Exception as e:
            self.logger.error(f"Error transcribing segments: {str(e)}")
            raise
    
    def get_segment_by_time(self, timestamp: float) -> Optional[AudioSegment]:
        """Get the audio segment that contains the given timestamp"""
        for segment in self.segments:
            if segment.start_time <= timestamp <= segment.end_time:
                return segment
        return None
    
    def update_segment_text(self, segment_index: int, new_text: str) -> bool:
        """Update the text for a specific segment"""
        try:
            if 0 <= segment_index < len(self.segments):
                self.segments[segment_index].text = new_text
                self.segments[segment_index].is_modified = True
                return True
            return False
        except Exception as e:
            self.logger.error(f"Error updating segment text: {str(e)}")
            return False
    
    def export_timeline_data(self, output_path: str) -> bool:
        """Export timeline data for use in editing interface"""
        try:
            timeline_data = {
                'segments': [seg.to_dict() for seg in self.segments],
                'total_duration': self.segments[-1].end_time if self.segments else 0,
                'sample_rate': self.sample_rate
            }
            
            with open(output_path, 'w') as f:
                json.dump(timeline_data, f, indent=2)
            
            return True
        except Exception as e:
            self.logger.error(f"Error exporting timeline data: {str(e)}")
            return False
