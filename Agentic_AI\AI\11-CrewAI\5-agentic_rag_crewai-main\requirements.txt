# Editable install with no version control (agentic_rag==0.1.0)
-e /Users/<USER>/Downloads/agentic_rag_crewai
aiohappyeyeballs==2.6.1
aiohttp==3.11.14
aiosignal==1.3.2
alembic==1.15.2
altair==5.5.0
annotated-types==0.7.0
anyio==4.9.0
appdirs==1.4.4
asgiref==3.8.1
asttokens==3.0.0
attrs==25.3.0
auth0-python==4.8.1
azure-common==1.1.28
azure-core==1.32.0
azure-search-documents==11.5.2
backoff==2.2.1
bcrypt==4.3.0
beautifulsoup4==4.13.3
blinker==1.9.0
build==1.2.2.post1
cachetools==5.5.2
certifi==2025.1.31
cffi==1.17.1
charset-normalizer==3.4.1
chroma-hnswlib==0.7.6
chromadb==0.5.23
click==8.1.8
cohere==5.14.0
coloredlogs==15.0.1
crewai==0.108.0
crewai-tools==0.38.1
cryptography==44.0.2
dataclasses-json==0.6.7
decorator==5.2.1
Deprecated==1.2.18
deprecation==2.1.0
distro==1.9.0
docker==7.1.0
docstring_parser==0.16
durationpy==0.9
embedchain==0.1.128
et_xmlfile==2.0.0
executing==2.2.0
fastapi==0.115.12
fastavro==1.10.0
fastembed==0.6.0
filelock==3.18.0
firecrawl-py==1.15.0
flatbuffers==25.2.10
frozenlist==1.5.0
fsspec==2025.3.0
gitdb==4.0.12
GitPython==3.1.44
google-auth==2.38.0
googleapis-common-protos==1.69.2
gptcache==0.1.44
greenlet==3.1.1
grpcio==1.71.0
grpcio-tools==1.71.0
h11==0.14.0
h2==4.2.0
hpack==4.1.0
httpcore==1.0.7
httptools==0.6.4
httpx==0.27.2
httpx-sse==0.4.0
huggingface-hub==0.29.3
humanfriendly==10.0
hyperframe==6.1.0
idna==3.10
importlib_metadata==8.6.1
importlib_resources==6.5.2
instructor==1.7.8
ipython==9.0.2
ipython_pygments_lexers==1.1.1
isodate==0.7.2
jedi==0.19.2
Jinja2==3.1.6
jiter==0.8.2
json5==0.10.0
json_repair==0.40.0
jsonpatch==1.33
jsonpickle==4.0.5
jsonpointer==3.0.0
jsonref==1.1.0
jsonschema==4.23.0
jsonschema-specifications==2024.10.1
kubernetes==32.0.1
lancedb==0.21.2
langchain==0.3.21
langchain-cohere==0.3.5
langchain-community==0.3.20
langchain-core==0.3.49
langchain-experimental==0.3.4
langchain-openai==0.2.14
langchain-text-splitters==0.3.7
langsmith==0.3.19
litellm==1.60.2
loguru==0.7.3
magika==0.6.1
Mako==1.3.9
markdown-it-py==3.0.0
markdownify==1.1.0
markitdown==0.1.1
MarkupSafe==3.0.2
marshmallow==3.26.1
matplotlib-inline==0.1.7
mdurl==0.1.2
mem0ai==0.1.81
mmh3==5.1.0
monotonic==1.6
mpmath==1.3.0
multidict==6.2.0
mypy-extensions==1.0.0
narwhals==1.32.0
nest-asyncio==1.6.0
networkx==3.4.2
nodeenv==1.9.1
numpy==2.2.4
oauthlib==3.2.2
onnxruntime==1.21.0
openai==1.69.0
openpyxl==3.1.5
opentelemetry-api==1.31.1
opentelemetry-exporter-otlp-proto-common==1.31.1
opentelemetry-exporter-otlp-proto-grpc==1.31.1
opentelemetry-exporter-otlp-proto-http==1.31.1
opentelemetry-instrumentation==0.52b1
opentelemetry-instrumentation-asgi==0.52b1
opentelemetry-instrumentation-fastapi==0.52b1
opentelemetry-proto==1.31.1
opentelemetry-sdk==1.31.1
opentelemetry-semantic-conventions==0.52b1
opentelemetry-util-http==0.52b1
orjson==3.10.16
overrides==7.7.0
packaging==24.2
pandas==2.2.3
parso==0.8.4
pdfminer.six==20250327
pdfplumber==0.11.6
pexpect==4.9.0
pillow==11.1.0
portalocker==2.10.1
posthog==3.23.0
prompt_toolkit==3.0.50
propcache==0.3.1
protobuf==5.29.4
psycopg2-binary==2.9.10
ptyprocess==0.7.0
pure_eval==0.2.3
py_rust_stemmers==0.1.5
pyarrow==19.0.1
pyasn1==0.6.1
pyasn1_modules==0.4.2
pycparser==2.22
pydantic==2.11.1
pydantic-settings==2.8.1
pydantic_core==2.33.0
pydeck==0.9.1
Pygments==2.19.1
PyJWT==2.10.1
pypdf==5.4.0
pypdfium2==4.30.1
PyPika==0.48.9
pyproject_hooks==1.2.0
pyright==1.1.398
pysbd==0.3.4
python-dateutil==2.9.0.post0
python-dotenv==1.1.0
pytube==15.0.0
pytz==2024.2
pyvis==0.3.2
PyYAML==6.0.2
qdrant-client==1.13.3
referencing==0.36.2
regex==2024.11.6
requests==2.32.3
requests-oauthlib==2.0.0
requests-toolbelt==1.0.0
rich==13.9.4
rpds-py==0.24.0
rsa==4.9
schema==0.7.7
shellingham==1.5.4
six==1.17.0
smmap==5.0.2
sniffio==1.3.1
soupsieve==2.6
SQLAlchemy==2.0.40
stack-data==0.6.3
starlette==0.46.1
streamlit==1.44.0
sympy==1.13.3
tabulate==0.9.0
tenacity==9.0.0
tiktoken==0.9.0
tokenizers==0.20.3
toml==0.10.2
tomli==2.2.1
tomli_w==1.2.0
tornado==6.4.2
tqdm==4.67.1
traitlets==5.14.3
typer==0.15.2
types-requests==2.32.0.20250328
typing-inspect==0.9.0
typing-inspection==0.4.0
typing_extensions==4.13.0
tzdata==2025.2
urllib3==2.3.0
uv==0.6.10
uvicorn==0.34.0
uvloop==0.21.0
watchfiles==1.0.4
wcwidth==0.2.13
websocket-client==1.8.0
websockets==15.0.1
wrapt==1.17.2
yarl==1.18.3
zipp==3.21.0
zstandard==0.23.0
