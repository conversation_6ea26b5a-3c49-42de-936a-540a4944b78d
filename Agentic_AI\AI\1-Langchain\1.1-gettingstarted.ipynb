{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import os\n", "from dotenv import load_dotenv\n", "load_dotenv()"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["os.environ[\"OPENAI_API_KEY\"]=os.getenv(\"OPENAI_API_KEY\")\n", "\n", "## Langsmith Tracking and tracing\n", "\n", "os.environ[\"LANGCHAIN_API_KEY\"]=os.getenv(\"LANGCHAIN_API_KEY\")\n", "os.environ[\"LANGCHAIN_TRACING_V2\"]=\"true\"\n", "os.environ[\"LANGCHAIN_PROJECT\"]=os.getenv(\"LANGCHAIN_PROJECT\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["client=<openai.resources.chat.completions.Completions object at 0x000002510F21F200> async_client=<openai.resources.chat.completions.AsyncCompletions object at 0x000002510F256D20> root_client=<openai.OpenAI object at 0x000002510EE08110> root_async_client=<openai.AsyncOpenAI object at 0x000002510F2550A0> model_name='o1-mini' temperature=1.0 model_kwargs={} openai_api_key=SecretStr('**********')\n"]}], "source": ["from langchain_openai import ChatOpenAI\n", "\n", "llm=ChatOpenAI(model=\"o1-mini\")\n", "print(llm)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["content=\"**Agentic AI** refers to artificial intelligence systems designed to operate as autonomous agents capable of making independent decisions, taking actions, and pursuing defined goals within an environment. The concept emphasizes the AI's ability to exhibit agency—the capacity to act intentionally and adaptively—much like a human or other autonomous entity would.\\n\\n### Key Characteristics of Agentic AI\\n\\n1. **Autonomy**: Agentic AI systems can operate without continuous human oversight, making decisions based on their programming, learning, and interactions with the environment.\\n\\n2. **Goal-Oriented Behavior**: These systems are designed with specific objectives and can plan and execute actions to achieve these goals.\\n\\n3. **Adaptability and Learning**: Agentic AI can learn from experiences, adapt to new situations, and improve performance over time through techniques like machine learning.\\n\\n4. **Perception and Interaction**: They can perceive their environment through sensors (e.g., cameras, microphones) and interact with it using actuators or interfaces, enabling dynamic responses to changing conditions.\\n\\n5. **Decision-Making**: Equipped with algorithms that allow for evaluating options, predicting outcomes, and making choices that align with their objectives.\\n\\n### Examples of Agentic AI\\n\\n- **Autonomous Vehicles**: Self-driving cars navigate roads, make real-time decisions, and adjust to traffic conditions without human intervention.\\n  \\n- **Robotic Assistants**: Robots in manufacturing or healthcare that perform tasks, respond to changes, and interact with humans effectively.\\n  \\n- **Virtual Personal Assistants**: AI like Siri or Alexa that can manage schedules, control smart devices, and provide information proactively based on user preferences and behaviors.\\n\\n- **Autonomous Drones**: Used in areas such as delivery, surveillance, or agriculture, these drones can navigate environments, avoid obstacles, and complete missions with minimal human guidance.\\n\\n### Distinction from Other AI Concepts\\n\\nWhile traditional AI systems might perform specific tasks based on predefined rules or respond to explicit commands, agentic AI embodies a higher level of independence and proactive behavior. Unlike narrow AI, which is limited to particular applications, agentic AI involves a broader scope of actions and decision-making processes aimed at achieving set objectives in varied and potentially unforeseen scenarios.\\n\\n### Applications of Agentic AI\\n\\n- **Healthcare**: Autonomous diagnostic systems that can analyze medical data, recommend treatments, and monitor patient progress with minimal human intervention.\\n  \\n- **Finance**: Automated trading systems that execute trades, manage portfolios, and respond to market changes autonomously.\\n  \\n- **Smart Homes and Cities**: Systems that manage energy use, security, transportation, and other infrastructure elements dynamically to optimize efficiency and quality of life.\\n\\n- **Customer Service**: Advanced chatbots and virtual agents that handle inquiries, resolve issues, and provide personalized support independently.\\n\\n### Ethical and Societal Considerations\\n\\nThe deployment of agentic AI raises several ethical and societal issues:\\n\\n- **Accountability**: Determining responsibility for the actions and decisions made by autonomous agents.\\n  \\n- **Bias and Fairness**: Ensuring that agentic AI systems do not perpetuate or exacerbate existing biases in their decision-making processes.\\n  \\n- **Transparency**: Making AI decision-making processes understandable to users and stakeholders.\\n  \\n- **Privacy**: Managing the data gathered and used by autonomous agents to protect individual privacy rights.\\n  \\n- **Safety and Security**: Ensuring that agentic AI systems operate reliably and are protected against malicious attacks or unintended behaviors.\\n\\n### Future Directions\\n\\nAs AI technology advances, agentic AI is expected to become increasingly sophisticated, with enhanced capabilities for reasoning, emotional intelligence, and ethical decision-making. Research is focused on creating more robust frameworks for ensuring that autonomous agents act in ways that are beneficial, transparent, and aligned with human values. Integration with other emerging technologies, such as the Internet of Things (IoT) and advanced robotics, will likely expand the applications and impact of agentic AI across various sectors.\\n\\n### Conclusion\\n\\nAgentic AI represents a significant evolution in artificial intelligence, emphasizing autonomy, intentionality, and goal-directed behavior. While it offers numerous benefits across diverse applications, it also necessitates careful consideration of ethical, legal, and societal implications to ensure that its development and deployment contribute positively to human welfare and societal advancement.\" additional_kwargs={'refusal': None} response_metadata={'token_usage': {'completion_tokens': 992, 'prompt_tokens': 13, 'total_tokens': 1005, 'completion_tokens_details': {'accepted_prediction_tokens': 0, 'audio_tokens': 0, 'reasoning_tokens': 128, 'rejected_prediction_tokens': 0}, 'prompt_tokens_details': {'audio_tokens': 0, 'cached_tokens': 0}}, 'model_name': 'o1-mini-2024-09-12', 'system_fingerprint': 'fp_f56e40de61', 'finish_reason': 'stop', 'logprobs': None} id='run-03816454-7e8a-4439-a930-802ee9672787-0' usage_metadata={'input_tokens': 13, 'output_tokens': 992, 'total_tokens': 1005, 'input_token_details': {'audio': 0, 'cache_read': 0}, 'output_token_details': {'audio': 0, 'reasoning': 128}}\n"]}], "source": ["result=llm.invoke(\"What is agentic AI\")\n", "print(result)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["**Agentic AI** refers to artificial intelligence systems designed to operate as autonomous agents capable of making independent decisions, taking actions, and pursuing defined goals within an environment. The concept emphasizes the AI's ability to exhibit agency—the capacity to act intentionally and adaptively—much like a human or other autonomous entity would.\n", "\n", "### Key Characteristics of Agentic AI\n", "\n", "1. **Autonomy**: Agentic AI systems can operate without continuous human oversight, making decisions based on their programming, learning, and interactions with the environment.\n", "\n", "2. **Goal-Oriented Behavior**: These systems are designed with specific objectives and can plan and execute actions to achieve these goals.\n", "\n", "3. **Adaptability and Learning**: Agentic AI can learn from experiences, adapt to new situations, and improve performance over time through techniques like machine learning.\n", "\n", "4. **Perception and Interaction**: They can perceive their environment through sensors (e.g., cameras, microphones) and interact with it using actuators or interfaces, enabling dynamic responses to changing conditions.\n", "\n", "5. **Decision-Making**: Equipped with algorithms that allow for evaluating options, predicting outcomes, and making choices that align with their objectives.\n", "\n", "### Examples of Agentic AI\n", "\n", "- **Autonomous Vehicles**: Self-driving cars navigate roads, make real-time decisions, and adjust to traffic conditions without human intervention.\n", "  \n", "- **Robotic Assistants**: Robots in manufacturing or healthcare that perform tasks, respond to changes, and interact with humans effectively.\n", "  \n", "- **Virtual Personal Assistants**: AI like Sir<PERSON> or Alexa that can manage schedules, control smart devices, and provide information proactively based on user preferences and behaviors.\n", "\n", "- **Autonomous Drones**: Used in areas such as delivery, surveillance, or agriculture, these drones can navigate environments, avoid obstacles, and complete missions with minimal human guidance.\n", "\n", "### Distinction from Other AI Concepts\n", "\n", "While traditional AI systems might perform specific tasks based on predefined rules or respond to explicit commands, agentic AI embodies a higher level of independence and proactive behavior. Unlike narrow AI, which is limited to particular applications, agentic AI involves a broader scope of actions and decision-making processes aimed at achieving set objectives in varied and potentially unforeseen scenarios.\n", "\n", "### Applications of Agentic AI\n", "\n", "- **Healthcare**: Autonomous diagnostic systems that can analyze medical data, recommend treatments, and monitor patient progress with minimal human intervention.\n", "  \n", "- **Finance**: Automated trading systems that execute trades, manage portfolios, and respond to market changes autonomously.\n", "  \n", "- **Smart Homes and Cities**: Systems that manage energy use, security, transportation, and other infrastructure elements dynamically to optimize efficiency and quality of life.\n", "\n", "- **Customer Service**: Advanced chatbots and virtual agents that handle inquiries, resolve issues, and provide personalized support independently.\n", "\n", "### Ethical and Societal Considerations\n", "\n", "The deployment of agentic AI raises several ethical and societal issues:\n", "\n", "- **Accountability**: Determining responsibility for the actions and decisions made by autonomous agents.\n", "  \n", "- **Bias and Fairness**: Ensuring that agentic AI systems do not perpetuate or exacerbate existing biases in their decision-making processes.\n", "  \n", "- **Transparency**: Making AI decision-making processes understandable to users and stakeholders.\n", "  \n", "- **Privacy**: Managing the data gathered and used by autonomous agents to protect individual privacy rights.\n", "  \n", "- **Safety and Security**: Ensuring that agentic AI systems operate reliably and are protected against malicious attacks or unintended behaviors.\n", "\n", "### Future Directions\n", "\n", "As AI technology advances, agentic AI is expected to become increasingly sophisticated, with enhanced capabilities for reasoning, emotional intelligence, and ethical decision-making. Research is focused on creating more robust frameworks for ensuring that autonomous agents act in ways that are beneficial, transparent, and aligned with human values. Integration with other emerging technologies, such as the Internet of Things (IoT) and advanced robotics, will likely expand the applications and impact of agentic AI across various sectors.\n", "\n", "### Conclusion\n", "\n", "Agentic AI represents a significant evolution in artificial intelligence, emphasizing autonomy, intentionality, and goal-directed behavior. While it offers numerous benefits across diverse applications, it also necessitates careful consideration of ethical, legal, and societal implications to ensure that its development and deployment contribute positively to human welfare and societal advancement.\n"]}], "source": ["print(result.content)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["ChatPromptTemplate(input_variables=['input'], input_types={}, partial_variables={}, messages=[SystemMessagePromptTemplate(prompt=PromptTemplate(input_variables=[], input_types={}, partial_variables={}, template='You are an expert AI Engineer. Provide me answer based on the question'), additional_kwargs={}), HumanMessagePromptTemplate(prompt=PromptTemplate(input_variables=['input'], input_types={}, partial_variables={}, template='{input}'), additional_kwargs={})])"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_core.prompts import ChatPromptTemplate\n", "\n", "prompt=ChatPromptTemplate.from_messages(\n", "    [\n", "        (\"system\",\"You are an expert AI Engineer. Provide me answer based on the question\"),\n", "        (\"user\",\"{input}\")\n", "\n", "    ]\n", ")\n", "prompt\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["llm=ChatOpenAI(model=\"gpt-4o\")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'prompt' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[7], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m chain\u001b[38;5;241m=\u001b[39m\u001b[43mprompt\u001b[49m\u001b[38;5;241m|\u001b[39mllm \n\u001b[0;32m      3\u001b[0m response\u001b[38;5;241m=\u001b[39mchain\u001b[38;5;241m.\u001b[39minvoke({\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124minput\u001b[39m\u001b[38;5;124m\"\u001b[39m:\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mCan you tell me about <PERSON><PERSON>\u001b[39m\u001b[38;5;124m\"\u001b[39m})\n\u001b[0;32m      4\u001b[0m \u001b[38;5;28mprint\u001b[39m(response)\n", "\u001b[1;31mNameError\u001b[0m: name 'prompt' is not defined"]}], "source": ["chain=prompt|llm \n", "\n", "response=chain.invoke({\"input\":\"Can you tell me about <PERSON><PERSON>\"})\n", "print(response)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Langsmith is a tool developed by LangChain that focuses on managing and debugging applications powered by large language models (LLMs). It is specifically designed to work seamlessly with the LangChain framework, which developers use to build applications that leverage LLMs for various tasks such as natural language processing, chatbot development, information retrieval, and more.\n", "\n", "The primary features of Langsmith include:\n", "\n", "1. **Trace Visualizations**: <PERSON><PERSON> provides detailed visualizations of traces, helping developers understand the flow and execution of their applications. This feature aids in identifying bottlenecks or errors in the logic or data flow.\n", "\n", "2. **Dataset Management**: Lang<PERSON> offers tools for managing datasets, which can be crucial for training and fine-tuning models. It helps ensure that data is organized and accessible, facilitating more efficient model training and testing.\n", "\n", "3. **Feedback Integration**: Developers can integrate feedback mechanisms within their applications, allowing users to provide feedback on the model's performance. This feedback can then be used to refine and improve the application's output.\n", "\n", "4. **Agent Execution Visualization**: For applications that use complex agents with decision-making capabilities, <PERSON><PERSON> can visualize the steps and decisions made by these agents, aiding in debugging and optimization.\n", "\n", "5. **Testing Frameworks**: Langsmith includes frameworks for testing various aspects of language model applications, ensuring reliability and robustness before deployment.\n", "\n", "By providing these tools, <PERSON><PERSON> aims to streamline the development process for applications that rely on LLMs, making it easier for developers to create, manage, and optimize their AI-driven applications effectively.\n"]}], "source": ["print(response.content)"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"ename": "KeyError", "evalue": "\"Input to ChatPromptTemplate is missing variables {'context'}.  Expected: ['context'] Received: ['input']\\nNote: if you intended {context} to be part of the string and not a variable, please escape it with double curly braces like: '{{context}}'.\\nFor troubleshooting, visit: https://python.langchain.com/docs/troubleshooting/errors/INVALID_PROMPT_INPUT \"", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[26], line 6\u001b[0m\n\u001b[0;32m      2\u001b[0m output_parser\u001b[38;5;241m=\u001b[39mStrOutputParser()\n\u001b[0;32m      4\u001b[0m chain\u001b[38;5;241m=\u001b[39mprompt\u001b[38;5;241m|\u001b[39mllm\u001b[38;5;241m|\u001b[39moutput_parser\n\u001b[1;32m----> 6\u001b[0m response\u001b[38;5;241m=\u001b[39m\u001b[43mchain\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43minvoke\u001b[49m\u001b[43m(\u001b[49m\u001b[43m{\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43minput\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mCan you tell me about <PERSON><PERSON>?\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m}\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m      7\u001b[0m \u001b[38;5;28mprint\u001b[39m(response)\n", "File \u001b[1;32me:\\agenticAI\\AgenticAIWorkspace\\venv\\Lib\\site-packages\\langchain_core\\runnables\\base.py:3014\u001b[0m, in \u001b[0;36mRunnableSequence.invoke\u001b[1;34m(self, input, config, **kwargs)\u001b[0m\n\u001b[0;32m   3012\u001b[0m context\u001b[38;5;241m.\u001b[39mrun(_set_config_context, config)\n\u001b[0;32m   3013\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m i \u001b[38;5;241m==\u001b[39m \u001b[38;5;241m0\u001b[39m:\n\u001b[1;32m-> 3014\u001b[0m     \u001b[38;5;28minput\u001b[39m \u001b[38;5;241m=\u001b[39m \u001b[43mcontext\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrun\u001b[49m\u001b[43m(\u001b[49m\u001b[43mstep\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43minvoke\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43minput\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mconfig\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   3015\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m   3016\u001b[0m     \u001b[38;5;28minput\u001b[39m \u001b[38;5;241m=\u001b[39m context\u001b[38;5;241m.\u001b[39mrun(step\u001b[38;5;241m.\u001b[39minvoke, \u001b[38;5;28minput\u001b[39m, config)\n", "File \u001b[1;32me:\\agenticAI\\AgenticAIWorkspace\\venv\\Lib\\site-packages\\langchain_core\\prompts\\base.py:210\u001b[0m, in \u001b[0;36mBasePromptTemplate.invoke\u001b[1;34m(self, input, config, **kwargs)\u001b[0m\n\u001b[0;32m    208\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mtags:\n\u001b[0;32m    209\u001b[0m     config[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtags\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m=\u001b[39m config[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtags\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m+\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mtags\n\u001b[1;32m--> 210\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_call_with_config\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    211\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_format_prompt_with_error_handling\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    212\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;28;43minput\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[0;32m    213\u001b[0m \u001b[43m    \u001b[49m\u001b[43mconfig\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    214\u001b[0m \u001b[43m    \u001b[49m\u001b[43mrun_type\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mprompt\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[0;32m    215\u001b[0m \u001b[43m    \u001b[49m\u001b[43mserialized\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_serialized\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    216\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32me:\\agenticAI\\AgenticAIWorkspace\\venv\\Lib\\site-packages\\langchain_core\\runnables\\base.py:1914\u001b[0m, in \u001b[0;36mRunnable._call_with_config\u001b[1;34m(self, func, input, config, run_type, serialized, **kwargs)\u001b[0m\n\u001b[0;32m   1910\u001b[0m     context \u001b[38;5;241m=\u001b[39m copy_context()\n\u001b[0;32m   1911\u001b[0m     context\u001b[38;5;241m.\u001b[39mrun(_set_config_context, child_config)\n\u001b[0;32m   1912\u001b[0m     output \u001b[38;5;241m=\u001b[39m cast(\n\u001b[0;32m   1913\u001b[0m         Output,\n\u001b[1;32m-> 1914\u001b[0m         \u001b[43mcontext\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrun\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m   1915\u001b[0m \u001b[43m            \u001b[49m\u001b[43mcall_func_with_variable_args\u001b[49m\u001b[43m,\u001b[49m\u001b[43m  \u001b[49m\u001b[38;5;66;43;03m# type: ignore[arg-type]\u001b[39;49;00m\n\u001b[0;32m   1916\u001b[0m \u001b[43m            \u001b[49m\u001b[43mfunc\u001b[49m\u001b[43m,\u001b[49m\u001b[43m  \u001b[49m\u001b[38;5;66;43;03m# type: ignore[arg-type]\u001b[39;49;00m\n\u001b[0;32m   1917\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;28;43minput\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m  \u001b[49m\u001b[38;5;66;43;03m# type: ignore[arg-type]\u001b[39;49;00m\n\u001b[0;32m   1918\u001b[0m \u001b[43m            \u001b[49m\u001b[43mconfig\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1919\u001b[0m \u001b[43m            \u001b[49m\u001b[43mrun_manager\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1920\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1921\u001b[0m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m,\n\u001b[0;32m   1922\u001b[0m     )\n\u001b[0;32m   1923\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mBaseException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[0;32m   1924\u001b[0m     run_manager\u001b[38;5;241m.\u001b[39mon_chain_error(e)\n", "File \u001b[1;32me:\\agenticAI\\AgenticAIWorkspace\\venv\\Lib\\site-packages\\langchain_core\\runnables\\config.py:396\u001b[0m, in \u001b[0;36mcall_func_with_variable_args\u001b[1;34m(func, input, config, run_manager, **kwargs)\u001b[0m\n\u001b[0;32m    394\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m run_manager \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;129;01mand\u001b[39;00m accepts_run_manager(func):\n\u001b[0;32m    395\u001b[0m     kwargs[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mrun_manager\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m=\u001b[39m run_manager\n\u001b[1;32m--> 396\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43minput\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32me:\\agenticAI\\AgenticAIWorkspace\\venv\\Lib\\site-packages\\langchain_core\\prompts\\base.py:184\u001b[0m, in \u001b[0;36mBasePromptTemplate._format_prompt_with_error_handling\u001b[1;34m(self, inner_input)\u001b[0m\n\u001b[0;32m    183\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21m_format_prompt_with_error_handling\u001b[39m(\u001b[38;5;28mself\u001b[39m, inner_input: \u001b[38;5;28mdict\u001b[39m) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m PromptValue:\n\u001b[1;32m--> 184\u001b[0m     _inner_input \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_validate_input\u001b[49m\u001b[43m(\u001b[49m\u001b[43minner_input\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    185\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mformat_prompt(\u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39m_inner_input)\n", "File \u001b[1;32me:\\agenticAI\\AgenticAIWorkspace\\venv\\Lib\\site-packages\\langchain_core\\prompts\\base.py:178\u001b[0m, in \u001b[0;36mBasePromptTemplate._validate_input\u001b[1;34m(self, inner_input)\u001b[0m\n\u001b[0;32m    172\u001b[0m     example_key \u001b[38;5;241m=\u001b[39m missing\u001b[38;5;241m.\u001b[39mpop()\n\u001b[0;32m    173\u001b[0m     msg \u001b[38;5;241m+\u001b[39m\u001b[38;5;241m=\u001b[39m (\n\u001b[0;32m    174\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124mNote: if you intended \u001b[39m\u001b[38;5;130;01m{{\u001b[39;00m\u001b[38;5;132;01m{\u001b[39;00mexample_key\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;130;01m}}\u001b[39;00m\u001b[38;5;124m to be part of the string\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m    175\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m and not a variable, please escape it with double curly braces like: \u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m    176\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;130;01m{{\u001b[39;00m\u001b[38;5;130;01m{{\u001b[39;00m\u001b[38;5;132;01m{\u001b[39;00mexample_key\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;130;01m}}\u001b[39;00m\u001b[38;5;130;01m}}\u001b[39;00m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m.\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m    177\u001b[0m     )\n\u001b[1;32m--> 178\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mKeyError\u001b[39;00m(\n\u001b[0;32m    179\u001b[0m         create_message(message\u001b[38;5;241m=\u001b[39mmsg, error_code\u001b[38;5;241m=\u001b[39mErrorCode\u001b[38;5;241m.\u001b[39mINVALID_PROMPT_INPUT)\n\u001b[0;32m    180\u001b[0m     )\n\u001b[0;32m    181\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m inner_input\n", "\u001b[1;31m<PERSON>eyError\u001b[0m: \"Input to ChatPromptTemplate is missing variables {'context'}.  Expected: ['context'] Received: ['input']\\nNote: if you intended {context} to be part of the string and not a variable, please escape it with double curly braces like: '{{context}}'.\\nFor troubleshooting, visit: https://python.langchain.com/docs/troubleshooting/errors/INVALID_PROMPT_INPUT \""]}], "source": ["from langchain_core.output_parsers import StrOutputParser\n", "output_parser=StrOutputParser()\n", "\n", "chain=prompt|llm|output_parser\n", "\n", "response=chain.invoke({\"input\":\"Can you tell me about <PERSON><PERSON>?\"})\n", "print(response)"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["from langchain_core.prompts import PromptTemplate\n", "from langchain_core.output_parsers import JsonOutputParser\n", "\n", "\n", "output_parser=JsonOutputParser()\n", "prompt = PromptTemplate(\n", "    template=\"Answer the user query.\\n{format_instructions}\\n{query}\\n\",\n", "    input_variables=[\"query\"],\n", "    partial_variables={\"format_instructions\": output_parser.get_format_instructions()},\n", ")\n"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'Langsmith': {'Description': 'Langsmith is a language learning platform that offers a variety of courses for individuals looking to improve their language skills. The platform utilizes interactive lessons, quizzes, and exercises to help users learn and practice languages in a fun and engaging way.', 'Features': ['Interactive lessons', 'Quizzes and exercises', 'Multiple language options', 'Progress tracking', 'Personalized learning experience'], 'Benefits': ['Improves language skills', 'Flexible learning schedule', 'Access to native speakers for practice', 'Engaging and interactive learning experience']}}\n"]}], "source": ["chain=prompt|llm|output_parser\n", "\n", "response=chain.invoke({\"query\":\"Can you tell me about Lang<PERSON>?\"})\n", "print(response)"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"data": {"text/plain": ["PromptTemplate(input_variables=['query'], input_types={}, partial_variables={'format_instructions': 'Return a JSON object.'}, template='Answer the user query.\\n{format_instructions}\\n{query}\\n')\n", "| ChatOpenAI(client=<openai.resources.chat.completions.Completions object at 0x000002511F884110>, async_client=<openai.resources.chat.completions.AsyncCompletions object at 0x000002511F825A00>, model_kwargs={}, openai_api_key='********************************************************************************************************************************************************************', openai_proxy='')\n", "| JsonOutputParser()"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["chain"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### RAG"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [], "source": ["from langchain_community.document_loaders import WebBaseLoader"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"data": {"text/plain": ["<langchain_community.document_loaders.web_base.WebBaseLoader at 0x25120f2d4f0>"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["loader=WebBaseLoader(\"https://python.langchain.com/docs/tutorials/llm_chain/\")\n", "loader"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"data": {"text/plain": ["[Document(metadata={'source': 'https://python.langchain.com/docs/tutorials/llm_chain/', 'title': 'Build a simple LLM application with chat models and prompt templates | 🦜️🔗 LangChain', 'description': \"In this quickstart we'll show you how to build a simple LLM application with <PERSON>Chain. This application will translate text from English into another language. This is a relatively simple LLM application - it's just a single LLM call plus some prompting. Still, this is a great way to get started with LangChain - a lot of features can be built with just some prompting and an LLM call!\", 'language': 'en'}, page_content='\\n\\n\\n\\n\\nBuild a simple LLM application with chat models and prompt templates | 🦜️🔗 LangChain\\n\\n\\n\\n\\n\\n\\nSkip to main contentJoin us at  Interrupt: The Agent AI Conference by LangChain on May 13 & 14 in San Francisco!IntegrationsAPI ReferenceMoreContributingPeopleError referenceLangSmithLangGraphLangChain HubLangChain JS/TSv0.3v0.3v0.2v0.1💬SearchIntroductionTutorialsBuild a Question Answering application over a Graph DatabaseTutorialsBuild a simple LLM application with chat models and prompt templatesBuild a ChatbotBuild a Retrieval Augmented Generation (RAG) App: Part 2Build an Extraction ChainBuild an AgentTaggingBuild a Retrieval Augmented Generation (RAG) App: Part 1Build a semantic search engineBuild a Question/Answering system over SQL dataSummarize TextHow-to guidesHow-to guidesHow to use tools in a chainHow to use a vectorstore as a retrieverHow to add memory to chatbotsHow to use example selectorsHow to add a semantic layer over graph databaseHow to invoke runnables in parallelHow to stream chat model responsesHow to add default invocation args to a RunnableHow to add retrieval to chatbotsHow to use few shot examples in chat modelsHow to do tool/function callingHow to install LangChain packagesHow to add examples to the prompt for query analysisHow to use few shot examplesHow to run custom functionsHow to use output parsers to parse an LLM response into structured formatHow to handle cases where no queries are generatedHow to route between sub-chainsHow to return structured data from a modelHow to summarize text through parallelizationHow to summarize text through iterative refinementHow to summarize text in a single LLM callHow to use toolkitsHow to add ad-hoc tool calling capability to LLMs and Chat ModelsBuild an Agent with AgentExecutor (Legacy)How to construct knowledge graphsHow to partially format prompt templatesHow to handle multiple queries when doing query analysisHow to use built-in tools and toolkitsHow to pass through arguments from one step to the nextHow to compose prompts togetherHow to handle multiple retrievers when doing query analysisHow to add values to a chain\\'s stateHow to construct filters for query analysisHow to configure runtime chain internalsHow deal with high cardinality categoricals when doing query analysisCustom Document LoaderHow to use the MultiQueryRetrieverHow to add scores to retriever resultsCachingHow to use callbacks in async environmentsHow to attach callbacks to a runnableHow to propagate callbacks  constructorHow to dispatch custom callback eventsHow to pass callbacks in at runtimeHow to split by characterHow to cache chat model responsesHow to handle rate limitsHow to init any model in one lineHow to track token usage in ChatModelsHow to add tools to chatbotsHow to split codeHow to do retrieval with contextual compressionHow to convert Runnables to ToolsHow to create custom callback handlersHow to create a custom chat model classCustom EmbeddingsHow to create a custom LLM classCustom RetrieverHow to create toolsHow to debug your LLM appsHow to load CSVsHow to load documents from a directoryHow to load HTMLHow to load JSONHow to load MarkdownHow to load Microsoft Office filesHow to load PDFsHow to load web pagesHow to create a dynamic (self-constructing) chainText embedding modelsHow to combine results from multiple retrieversHow to select examples from a LangSmith datasetHow to select examples by lengthHow to select examples by maximal marginal relevance (MMR)How to select examples by n-gram overlapHow to select examples by similarityHow to use reference examples when doing extractionHow to handle long text when doing extractionHow to use prompting alone (no tool calling) to do extractionHow to add fallbacks to a runnableHow to filter messagesHybrid SearchHow to use the LangChain indexing APIHow to inspect runnablesLangChain Expression Language CheatsheetHow to cache LLM responsesHow to track token usage for LLMsRun models locallyHow to get log probabilitiesHow to reorder retrieved results to mitigate the \"lost in the middle\" effectHow to split Markdown by HeadersHow to merge consecutive messages of the same typeHow to add message historyHow to migrate from legacy LangChain agents to LangGraphHow to retrieve using multiple vectors per documentHow to pass multimodal data directly to modelsHow to use multimodal promptsHow to create a custom Output ParserHow to use the output-fixing parserHow to parse JSON outputHow to retry when a parsing error occursHow to parse text from message objectsHow to parse XML outputHow to parse YAML outputHow to use the Parent Document RetrieverHow to use LangChain with different Pydantic versionsHow to add chat historyHow to get a RAG application to add citationsHow to do per-user retrievalHow to get your RAG application to return sourcesHow to stream results from your RAG applicationHow to split JSON dataHow to recursively split text by charactersResponse metadataHow to pass runtime secrets to runnablesHow to do \"self-querying\" retrievalHow to split text based on semantic similarityHow to chain runnablesHow to save and load LangChain objectsHow to split text by tokensHow to split HTMLHow to do question answering over CSVsHow to deal with large databases when doing SQL question-answeringHow to better prompt when doing SQL question-answeringHow to do query validation as part of SQL question-answeringHow to stream runnablesHow to stream responses from an LLMHow to use a time-weighted vector store retrieverHow to return artifacts from a toolHow to use chat models to call toolsHow to disable parallel tool callingHow to force models to call a toolHow to access the RunnableConfig from a toolHow to pass tool outputs to chat modelsHow to pass run time values to toolsHow to stream events from a toolHow to stream tool callsHow to convert tools to OpenAI FunctionsHow to handle tool errorsHow to use few-shot prompting with tool callingHow to add a human-in-the-loop for toolsHow to bind model-specific toolsHow to trim messagesHow to create and query vector storesConceptual guideAgentsArchitectureAsync programming with langchainCallbacksChat historyChat modelsDocument loadersEmbedding modelsEvaluationExample selectorsFew-shot promptingConceptual guideKey-value storesLangChain Expression Language (LCEL)MessagesMultimodalityOutput parsersPrompt TemplatesRetrieval augmented generation (RAG)RetrievalRetrieversRunnable interfaceStreamingStructured outputsTestingString-in, string-out llmsText splittersTokensTool callingToolsTracingVector storesWhy LangChain?Ecosystem🦜🛠️ LangSmith🦜🕸️ LangGraphVersionsv0.3v0.2Pydantic compatibilityMigrating from v0.0 chainsHow to migrate from v0.0 chainsMigrating from ConstitutionalChainMigrating from ConversationalChainMigrating from ConversationalRetrievalChainMigrating from LLMChainMigrating from LLMMathChainMigrating from LLMRouterChainMigrating from MapReduceDocumentsChainMigrating from MapRerankDocumentsChainMigrating from MultiPromptChainMigrating from RefineDocumentsChainMigrating from RetrievalQAMigrating from StuffDocumentsChainUpgrading to LangGraph memoryHow to migrate to LangGraph memoryHow to use BaseChatMessageHistory with LangGraphMigrating off ConversationBufferMemory or ConversationStringBufferMemoryMigrating off ConversationBufferWindowMemory or ConversationTokenBufferMemoryMigrating off ConversationSummaryMemory or ConversationSummaryBufferMemoryA Long-Term Memory AgentRelease policySecurity PolicyTutorialsBuild a simple LLM application with chat models and prompt templatesOn this pageBuild a simple LLM application with chat models and prompt templates\\nIn this quickstart we\\'ll show you how to build a simple LLM application with LangChain. This application will translate text from English into another language. This is a relatively simple LLM application - it\\'s just a single LLM call plus some prompting. Still, this is a great way to get started with LangChain - a lot of features can be built with just some prompting and an LLM call!\\nAfter reading this tutorial, you\\'ll have a high level overview of:\\n\\n\\nUsing language models\\n\\n\\nUsing prompt templates\\n\\n\\nDebugging and tracing your application using LangSmith\\n\\n\\nLet\\'s dive in!\\nSetup\\u200b\\nJupyter Notebook\\u200b\\nThis and other tutorials are perhaps most conveniently run in a Jupyter notebooks. Going through guides in an interactive environment is a great way to better understand them. See here for instructions on how to install.\\nInstallation\\u200b\\nTo install LangChain run:\\n\\nPipCondapip install langchainconda install langchain -c conda-forge\\nFor more details, see our Installation guide.\\nLangSmith\\u200b\\nMany of the applications you build with LangChain will contain multiple steps with multiple invocations of LLM calls.\\nAs these applications get more and more complex, it becomes crucial to be able to inspect what exactly is going on inside your chain or agent.\\nThe best way to do this is with LangSmith.\\nAfter you sign up at the link above, make sure to set your environment variables to start logging traces:\\nexport LANGSMITH_TRACING=\"true\"export LANGSMITH_API_KEY=\"...\"\\nOr, if in a notebook, you can set them with:\\nimport getpassimport osos.environ[\"LANGSMITH_TRACING\"] = \"true\"os.environ[\"LANGSMITH_API_KEY\"] = getpass.getpass()\\nUsing Language Models\\u200b\\nFirst up, let\\'s learn how to use a language model by itself. LangChain supports many different language models that you can use interchangeably. For details on getting started with a specific model, refer to supported integrations.\\n\\nSelect chat model:Groq▾GroqOpenAIAnthropicAzureGoogle VertexAWSCohereNVIDIAFireworks AIMistral AITogether AIDatabrickspip install -qU \"langchain[groq]\"import getpassimport osif not os.environ.get(\"GROQ_API_KEY\"):  os.environ[\"GROQ_API_KEY\"] = getpass.getpass(\"Enter API key for Groq: \")from langchain.chat_models import init_chat_modelmodel = init_chat_model(\"llama3-8b-8192\", model_provider=\"groq\")\\nLet\\'s first use the model directly. ChatModels are instances of LangChain Runnables, which means they expose a standard interface for interacting with them. To simply call the model, we can pass in a list of messages to the .invoke method.\\nfrom langchain_core.messages import HumanMessage, SystemMessagemessages = [    SystemMessage(\"Translate the following from English into Italian\"),    HumanMessage(\"hi!\"),]model.invoke(messages)API Reference:HumanMessage | SystemMessage\\nAIMessage(content=\\'Ciao!\\', additional_kwargs={\\'refusal\\': None}, response_metadata={\\'token_usage\\': {\\'completion_tokens\\': 3, \\'prompt_tokens\\': 20, \\'total_tokens\\': 23, \\'completion_tokens_details\\': {\\'accepted_prediction_tokens\\': 0, \\'audio_tokens\\': 0, \\'reasoning_tokens\\': 0, \\'rejected_prediction_tokens\\': 0}, \\'prompt_tokens_details\\': {\\'audio_tokens\\': 0, \\'cached_tokens\\': 0}}, \\'model_name\\': \\'gpt-4o-mini-2024-07-18\\', \\'system_fingerprint\\': \\'fp_0705bf87c0\\', \\'finish_reason\\': \\'stop\\', \\'logprobs\\': None}, id=\\'run-32654a56-627c-40e1-a141-ad9350bbfd3e-0\\', usage_metadata={\\'input_tokens\\': 20, \\'output_tokens\\': 3, \\'total_tokens\\': 23, \\'input_token_details\\': {\\'audio\\': 0, \\'cache_read\\': 0}, \\'output_token_details\\': {\\'audio\\': 0, \\'reasoning\\': 0}})\\ntipIf we\\'ve enabled LangSmith, we can see that this run is logged to LangSmith, and can see the LangSmith trace. The LangSmith trace reports token usage information, latency, standard model parameters (such as temperature), and other information.\\nNote that ChatModels receive message objects as input and generate message objects as output. In addition to text content, message objects convey conversational roles and hold important data, such as tool calls and token usage counts.\\nLangChain also supports chat model inputs via strings or OpenAI format. The following are equivalent:\\nmodel.invoke(\"Hello\")model.invoke([{\"role\": \"user\", \"content\": \"Hello\"}])model.invoke([HumanMessage(\"Hello\")])\\nStreaming\\u200b\\nBecause chat models are Runnables, they expose a standard interface that includes async and streaming modes of invocation. This allows us to stream individual tokens from a chat model:\\nfor token in model.stream(messages):    print(token.content, end=\"|\")\\n|C|iao|!||\\nYou can find more details on streaming chat model outputs in this guide.\\nPrompt Templates\\u200b\\nRight now we are passing a list of messages directly into the language model. Where does this list of messages come from? Usually, it is constructed from a combination of user input and application logic. This application logic usually takes the raw user input and transforms it into a list of messages ready to pass to the language model. Common transformations include adding a system message or formatting a template with the user input.\\nPrompt templates are a concept in LangChain designed to assist with this transformation. They take in raw user input and return data (a prompt) that is ready to pass into a language model.\\nLet\\'s create a prompt template here. It will take in two user variables:\\n\\nlanguage: The language to translate text into\\ntext: The text to translate\\n\\nfrom langchain_core.prompts import ChatPromptTemplatesystem_template = \"Translate the following from English into {language}\"prompt_template = ChatPromptTemplate.from_messages(    [(\"system\", system_template), (\"user\", \"{text}\")])API Reference:ChatPromptTemplate\\nNote that ChatPromptTemplate supports multiple message roles in a single template. We format the language parameter into the system message, and the user text into a user message.\\nThe input to this prompt template is a dictionary. We can play around with this prompt template by itself to see what it does by itself\\nprompt = prompt_template.invoke({\"language\": \"Italian\", \"text\": \"hi!\"})prompt\\nChatPromptValue(messages=[SystemMessage(content=\\'Translate the following from English into Italian\\', additional_kwargs={}, response_metadata={}), HumanMessage(content=\\'hi!\\', additional_kwargs={}, response_metadata={})])\\nWe can see that it returns a ChatPromptValue that consists of two messages. If we want to access the messages directly we do:\\nprompt.to_messages()\\n[SystemMessage(content=\\'Translate the following from English into Italian\\', additional_kwargs={}, response_metadata={}), HumanMessage(content=\\'hi!\\', additional_kwargs={}, response_metadata={})]\\nFinally, we can invoke the chat model on the formatted prompt:\\nresponse = model.invoke(prompt)print(response.content)\\nCiao!\\ntipMessage content can contain both text and content blocks with additional structure. See this guide for more information.\\nIf we take a look at the LangSmith trace, we can see exactly what prompt the chat model receives, along with token usage information, latency, standard model parameters (such as temperature), and other information.\\nConclusion\\u200b\\nThat\\'s it! In this tutorial you\\'ve learned how to create your first simple LLM application. You\\'ve learned how to work with language models, how to create a prompt template, and how to get great observability into applications you create with LangSmith.\\nThis just scratches the surface of what you will want to learn to become a proficient AI Engineer. Luckily - we\\'ve got a lot of other resources!\\nFor further reading on the core concepts of LangChain, we\\'ve got detailed Conceptual Guides.\\nIf you have more specific questions on these concepts, check out the following sections of the how-to guides:\\n\\nChat models\\nPrompt templates\\n\\nAnd the LangSmith docs:\\n\\nLangSmith\\nEdit this pageWas this page helpful?PreviousTutorialsNextBuild a ChatbotSetupJupyter NotebookInstallationLangSmithUsing Language ModelsStreamingPrompt TemplatesConclusionCommunityTwitterGitHubOrganizationPythonJS/TSMoreHomepageBlogYouTubeCopyright © 2025 LangChain, Inc.\\n\\n')]"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["document=loader.load()\n", "document"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"data": {"text/plain": ["[Document(metadata={'source': 'https://python.langchain.com/docs/tutorials/llm_chain/', 'title': 'Build a simple LLM application with chat models and prompt templates | 🦜️🔗 LangChain', 'description': \"In this quickstart we'll show you how to build a simple LLM application with LangChain. This application will translate text from English into another language. This is a relatively simple LLM application - it's just a single LLM call plus some prompting. Still, this is a great way to get started with LangChain - a lot of features can be built with just some prompting and an LLM call!\", 'language': 'en'}, page_content='Build a simple LLM application with chat models and prompt templates | 🦜️🔗 LangChain'),\n", " Document(metadata={'source': 'https://python.langchain.com/docs/tutorials/llm_chain/', 'title': 'Build a simple LLM application with chat models and prompt templates | 🦜️🔗 LangChain', 'description': \"In this quickstart we'll show you how to build a simple LLM application with LangChain. This application will translate text from English into another language. This is a relatively simple LLM application - it's just a single LLM call plus some prompting. Still, this is a great way to get started with LangChain - a lot of features can be built with just some prompting and an LLM call!\", 'language': 'en'}, page_content='Skip to main contentJoin us at  Interrupt: The Agent AI Conference by LangChain on May 13 & 14 in San Francisco!IntegrationsAPI ReferenceMoreContributingPeopleError referenceLangSmithLangGraphLangChain HubLangChain JS/TSv0.3v0.3v0.2v0.1💬SearchIntroductionTutorialsBuild a Question Answering application over a Graph DatabaseTutorialsBuild a simple LLM application with chat models and prompt templatesBuild a ChatbotBuild a Retrieval Augmented Generation (RAG) App: Part 2Build an Extraction ChainBuild an AgentTaggingBuild a Retrieval Augmented Generation (RAG) App: Part 1Build a semantic search engineBuild a Question/Answering system over SQL dataSummarize TextHow-to guidesHow-to guidesHow to use tools in a chainHow to use a vectorstore as a retrieverHow to add memory to chatbotsHow to use example selectorsHow to add a semantic layer over graph databaseHow to invoke runnables in parallelHow to stream chat model responsesHow to add default invocation args to a RunnableHow to add retrieval'),\n", " Document(metadata={'source': 'https://python.langchain.com/docs/tutorials/llm_chain/', 'title': 'Build a simple LLM application with chat models and prompt templates | 🦜️🔗 LangChain', 'description': \"In this quickstart we'll show you how to build a simple LLM application with LangChain. This application will translate text from English into another language. This is a relatively simple LLM application - it's just a single LLM call plus some prompting. Still, this is a great way to get started with LangChain - a lot of features can be built with just some prompting and an LLM call!\", 'language': 'en'}, page_content='selectorsHow to add a semantic layer over graph databaseHow to invoke runnables in parallelHow to stream chat model responsesHow to add default invocation args to a RunnableHow to add retrieval to chatbotsHow to use few shot examples in chat modelsHow to do tool/function callingHow to install LangChain packagesHow to add examples to the prompt for query analysisHow to use few shot examplesHow to run custom functionsHow to use output parsers to parse an LLM response into structured formatHow to handle cases where no queries are generatedHow to route between sub-chainsHow to return structured data from a modelHow to summarize text through parallelizationHow to summarize text through iterative refinementHow to summarize text in a single LLM callHow to use toolkitsHow to add ad-hoc tool calling capability to LLMs and Chat ModelsBuild an Agent with AgentExecutor (Legacy)How to construct knowledge graphsHow to partially format prompt templatesHow to handle multiple queries when doing query'),\n", " Document(metadata={'source': 'https://python.langchain.com/docs/tutorials/llm_chain/', 'title': 'Build a simple LLM application with chat models and prompt templates | 🦜️🔗 LangChain', 'description': \"In this quickstart we'll show you how to build a simple LLM application with LangChain. This application will translate text from English into another language. This is a relatively simple LLM application - it's just a single LLM call plus some prompting. Still, this is a great way to get started with LangChain - a lot of features can be built with just some prompting and an LLM call!\", 'language': 'en'}, page_content=\"capability to LLMs and Chat ModelsBuild an Agent with AgentExecutor (Legacy)How to construct knowledge graphsHow to partially format prompt templatesHow to handle multiple queries when doing query analysisHow to use built-in tools and toolkitsHow to pass through arguments from one step to the nextHow to compose prompts togetherHow to handle multiple retrievers when doing query analysisHow to add values to a chain's stateHow to construct filters for query analysisHow to configure runtime chain internalsHow deal with high cardinality categoricals when doing query analysisCustom Document LoaderHow to use the MultiQueryRetrieverHow to add scores to retriever resultsCachingHow to use callbacks in async environmentsHow to attach callbacks to a runnableHow to propagate callbacks  constructorHow to dispatch custom callback eventsHow to pass callbacks in at runtimeHow to split by characterHow to cache chat model responsesHow to handle rate limitsHow to init any model in one lineHow to track\"),\n", " Document(metadata={'source': 'https://python.langchain.com/docs/tutorials/llm_chain/', 'title': 'Build a simple LLM application with chat models and prompt templates | 🦜️🔗 LangChain', 'description': \"In this quickstart we'll show you how to build a simple LLM application with <PERSON>Chain. This application will translate text from English into another language. This is a relatively simple LLM application - it's just a single LLM call plus some prompting. Still, this is a great way to get started with LangChain - a lot of features can be built with just some prompting and an LLM call!\", 'language': 'en'}, page_content='to dispatch custom callback eventsHow to pass callbacks in at runtimeHow to split by characterHow to cache chat model responsesHow to handle rate limitsHow to init any model in one lineHow to track token usage in ChatModelsHow to add tools to chatbotsHow to split codeHow to do retrieval with contextual compressionHow to convert Runnables to ToolsHow to create custom callback handlersHow to create a custom chat model classCustom EmbeddingsHow to create a custom LLM classCustom RetrieverHow to create toolsHow to debug your LLM appsHow to load CSVsHow to load documents from a directoryHow to load HTMLHow to load JSONHow to load MarkdownHow to load Microsoft Office filesHow to load PDFsHow to load web pagesHow to create a dynamic (self-constructing) chainText embedding modelsHow to combine results from multiple retrieversHow to select examples from a LangSmith datasetHow to select examples by lengthHow to select examples by maximal marginal relevance (MMR)How to select examples by n-gram'),\n", " Document(metadata={'source': 'https://python.langchain.com/docs/tutorials/llm_chain/', 'title': 'Build a simple LLM application with chat models and prompt templates | 🦜️🔗 LangChain', 'description': \"In this quickstart we'll show you how to build a simple LLM application with LangChain. This application will translate text from English into another language. This is a relatively simple LLM application - it's just a single LLM call plus some prompting. Still, this is a great way to get started with LangChain - a lot of features can be built with just some prompting and an LLM call!\", 'language': 'en'}, page_content='from multiple retrieversHow to select examples from a LangSmith datasetHow to select examples by lengthHow to select examples by maximal marginal relevance (MMR)How to select examples by n-gram overlapHow to select examples by similarityHow to use reference examples when doing extractionHow to handle long text when doing extractionHow to use prompting alone (no tool calling) to do extractionHow to add fallbacks to a runnableHow to filter messagesHybrid SearchHow to use the LangChain indexing APIHow to inspect runnablesLangChain Expression Language CheatsheetHow to cache LLM responsesHow to track token usage for LLMsRun models locallyHow to get log probabilitiesHow to reorder retrieved results to mitigate the \"lost in the middle\" effectHow to split Markdown by HeadersHow to merge consecutive messages of the same typeHow to add message historyHow to migrate from legacy LangChain agents to LangGraphHow to retrieve using multiple vectors per documentHow to pass multimodal data directly to'),\n", " Document(metadata={'source': 'https://python.langchain.com/docs/tutorials/llm_chain/', 'title': 'Build a simple LLM application with chat models and prompt templates | 🦜️🔗 LangChain', 'description': \"In this quickstart we'll show you how to build a simple LLM application with LangChain. This application will translate text from English into another language. This is a relatively simple LLM application - it's just a single LLM call plus some prompting. Still, this is a great way to get started with LangChain - a lot of features can be built with just some prompting and an LLM call!\", 'language': 'en'}, page_content='messages of the same typeHow to add message historyHow to migrate from legacy LangChain agents to LangGraphHow to retrieve using multiple vectors per documentHow to pass multimodal data directly to modelsHow to use multimodal promptsHow to create a custom Output ParserHow to use the output-fixing parserHow to parse JSON outputHow to retry when a parsing error occursHow to parse text from message objectsHow to parse XML outputHow to parse YAML outputHow to use the Parent Document RetrieverHow to use <PERSON><PERSON>hai<PERSON> with different Pydantic versionsHow to add chat historyHow to get a RAG application to add citationsHow to do per-user retrievalHow to get your RAG application to return sourcesHow to stream results from your RAG applicationHow to split JSON dataHow to recursively split text by charactersResponse metadataHow to pass runtime secrets to runnablesHow to do \"self-querying\" retrievalHow to split text based on semantic similarityHow to chain runnablesHow to save and load LangChain'),\n", " Document(metadata={'source': 'https://python.langchain.com/docs/tutorials/llm_chain/', 'title': 'Build a simple LLM application with chat models and prompt templates | 🦜️🔗 LangChain', 'description': \"In this quickstart we'll show you how to build a simple LLM application with LangChain. This application will translate text from English into another language. This is a relatively simple LLM application - it's just a single LLM call plus some prompting. Still, this is a great way to get started with LangChain - a lot of features can be built with just some prompting and an LLM call!\", 'language': 'en'}, page_content='metadataHow to pass runtime secrets to runnablesHow to do \"self-querying\" retrievalHow to split text based on semantic similarityHow to chain runnablesHow to save and load LangChain objectsHow to split text by tokensHow to split HTMLHow to do question answering over CSVsHow to deal with large databases when doing SQL question-answeringHow to better prompt when doing SQL question-answeringHow to do query validation as part of SQL question-answeringHow to stream runnablesHow to stream responses from an LLMHow to use a time-weighted vector store retrieverHow to return artifacts from a toolHow to use chat models to call toolsHow to disable parallel tool callingHow to force models to call a toolHow to access the RunnableConfig from a toolHow to pass tool outputs to chat modelsHow to pass run time values to toolsHow to stream events from a toolHow to stream tool callsHow to convert tools to OpenAI FunctionsHow to handle tool errorsHow to use few-shot prompting with tool callingHow to add a'),\n", " Document(metadata={'source': 'https://python.langchain.com/docs/tutorials/llm_chain/', 'title': 'Build a simple LLM application with chat models and prompt templates | 🦜️🔗 LangChain', 'description': \"In this quickstart we'll show you how to build a simple LLM application with LangChain. This application will translate text from English into another language. This is a relatively simple LLM application - it's just a single LLM call plus some prompting. Still, this is a great way to get started with LangChain - a lot of features can be built with just some prompting and an LLM call!\", 'language': 'en'}, page_content='values to toolsHow to stream events from a toolHow to stream tool callsHow to convert tools to OpenAI FunctionsHow to handle tool errorsHow to use few-shot prompting with tool callingHow to add a human-in-the-loop for toolsHow to bind model-specific toolsHow to trim messagesHow to create and query vector storesConceptual guideAgentsArchitectureAsync programming with langchainCallbacksChat historyChat modelsDocument loadersEmbedding modelsEvaluationExample selectorsFew-shot promptingConceptual guideKey-value storesLangChain Expression Language (LCEL)MessagesMultimodalityOutput parsersPrompt TemplatesRetrieval augmented generation (RAG)RetrievalRetrieversRunnable interfaceStreamingStructured outputsTestingString-in, string-out llmsText splittersTokensTool callingToolsTracingVector storesWhy LangChain?Ecosystem🦜🛠️ LangSmith🦜🕸️ LangGraphVersionsv0.3v0.2Pydantic compatibilityMigrating from v0.0 chainsHow to migrate from v0.0 chainsMigrating from ConstitutionalChainMigrating from'),\n", " Document(metadata={'source': 'https://python.langchain.com/docs/tutorials/llm_chain/', 'title': 'Build a simple LLM application with chat models and prompt templates | 🦜️🔗 LangChain', 'description': \"In this quickstart we'll show you how to build a simple LLM application with <PERSON><PERSON>hain. This application will translate text from English into another language. This is a relatively simple LLM application - it's just a single LLM call plus some prompting. Still, this is a great way to get started with LangChain - a lot of features can be built with just some prompting and an LLM call!\", 'language': 'en'}, page_content='storesWhy LangChain?Ecosystem🦜🛠️ LangSmith🦜🕸️ LangGraphVersionsv0.3v0.2Pydantic compatibilityMigrating from v0.0 chainsHow to migrate from v0.0 chainsMigrating from ConstitutionalChainMigrating from ConversationalChainMigrating from ConversationalRetrievalChainMigrating from LLMChainMigrating from LLMMathChainMigrating from LLMRouterChainMigrating from MapReduceDocumentsChainMigrating from MapRerankDocumentsChainMigrating from MultiPromptChainMigrating from RefineDocumentsChainMigrating from RetrievalQAMigrating from StuffDocumentsChainUpgrading to LangGraph memoryHow to migrate to LangGraph memoryHow to use BaseChatMessageHistory with LangGraphMigrating off ConversationBufferMemory or ConversationStringBufferMemoryMigrating off ConversationBufferWindowMemory or ConversationTokenBufferMemoryMigrating off ConversationSummaryMemory or ConversationSummaryBufferMemoryA Long-Term Memory AgentRelease policySecurity PolicyTutorialsBuild a simple LLM application with chat models and prompt'),\n", " Document(metadata={'source': 'https://python.langchain.com/docs/tutorials/llm_chain/', 'title': 'Build a simple LLM application with chat models and prompt templates | 🦜️🔗 LangChain', 'description': \"In this quickstart we'll show you how to build a simple LLM application with LangChain. This application will translate text from English into another language. This is a relatively simple LLM application - it's just a single LLM call plus some prompting. Still, this is a great way to get started with LangChain - a lot of features can be built with just some prompting and an LLM call!\", 'language': 'en'}, page_content='off ConversationSummaryMemory or ConversationSummaryBufferMemoryA Long-Term Memory AgentRelease policySecurity PolicyTutorialsBuild a simple LLM application with chat models and prompt templatesOn this pageBuild a simple LLM application with chat models and prompt templates'),\n", " Document(metadata={'source': 'https://python.langchain.com/docs/tutorials/llm_chain/', 'title': 'Build a simple LLM application with chat models and prompt templates | 🦜️🔗 LangChain', 'description': \"In this quickstart we'll show you how to build a simple LLM application with LangChain. This application will translate text from English into another language. This is a relatively simple LLM application - it's just a single LLM call plus some prompting. Still, this is a great way to get started with LangChain - a lot of features can be built with just some prompting and an LLM call!\", 'language': 'en'}, page_content=\"In this quickstart we'll show you how to build a simple LLM application with LangChain. This application will translate text from English into another language. This is a relatively simple LLM application - it's just a single LLM call plus some prompting. Still, this is a great way to get started with LangChain - a lot of features can be built with just some prompting and an LLM call!\\nAfter reading this tutorial, you'll have a high level overview of:\"),\n", " Document(metadata={'source': 'https://python.langchain.com/docs/tutorials/llm_chain/', 'title': 'Build a simple LLM application with chat models and prompt templates | 🦜️🔗 LangChain', 'description': \"In this quickstart we'll show you how to build a simple LLM application with <PERSON><PERSON>hain. This application will translate text from English into another language. This is a relatively simple LLM application - it's just a single LLM call plus some prompting. Still, this is a great way to get started with LangChain - a lot of features can be built with just some prompting and an LLM call!\", 'language': 'en'}, page_content=\"Using language models\\n\\n\\nUsing prompt templates\\n\\n\\nDebugging and tracing your application using LangSmith\\n\\n\\nLet's dive in!\\nSetup\\u200b\\nJupyter Notebook\\u200b\\nThis and other tutorials are perhaps most conveniently run in a Jupyter notebooks. Going through guides in an interactive environment is a great way to better understand them. See here for instructions on how to install.\\nInstallation\\u200b\\nTo install <PERSON><PERSON><PERSON><PERSON> run:\"),\n", " Document(metadata={'source': 'https://python.langchain.com/docs/tutorials/llm_chain/', 'title': 'Build a simple LLM application with chat models and prompt templates | 🦜️🔗 LangChain', 'description': \"In this quickstart we'll show you how to build a simple LLM application with LangChain. This application will translate text from English into another language. This is a relatively simple LLM application - it's just a single LLM call plus some prompting. Still, this is a great way to get started with LangChain - a lot of features can be built with just some prompting and an LLM call!\", 'language': 'en'}, page_content='PipCondapip install langchainconda install langchain -c conda-forge\\nFor more details, see our Installation guide.\\nLangSmith\\u200b\\nMany of the applications you build with LangChain will contain multiple steps with multiple invocations of LLM calls.\\nAs these applications get more and more complex, it becomes crucial to be able to inspect what exactly is going on inside your chain or agent.\\nThe best way to do this is with LangSmith.\\nAfter you sign up at the link above, make sure to set your environment variables to start logging traces:\\nexport LANGSMITH_TRACING=\"true\"export LANGSMITH_API_KEY=\"...\"\\nOr, if in a notebook, you can set them with:\\nimport getpassimport osos.environ[\"LANGSMITH_TRACING\"] = \"true\"os.environ[\"LANGSMITH_API_KEY\"] = getpass.getpass()\\nUsing Language Models\\u200b'),\n", " Document(metadata={'source': 'https://python.langchain.com/docs/tutorials/llm_chain/', 'title': 'Build a simple LLM application with chat models and prompt templates | 🦜️🔗 LangChain', 'description': \"In this quickstart we'll show you how to build a simple LLM application with <PERSON><PERSON>hain. This application will translate text from English into another language. This is a relatively simple LLM application - it's just a single LLM call plus some prompting. Still, this is a great way to get started with LangChain - a lot of features can be built with just some prompting and an LLM call!\", 'language': 'en'}, page_content='Or, if in a notebook, you can set them with:\\nimport getpassimport osos.environ[\"LANGSMITH_TRACING\"] = \"true\"os.environ[\"LANGSMITH_API_KEY\"] = getpass.getpass()\\nUsing Language Models\\u200b\\nFirst up, let\\'s learn how to use a language model by itself. LangChain supports many different language models that you can use interchangeably. For details on getting started with a specific model, refer to supported integrations.'),\n", " Document(metadata={'source': 'https://python.langchain.com/docs/tutorials/llm_chain/', 'title': 'Build a simple LLM application with chat models and prompt templates | 🦜️🔗 LangChain', 'description': \"In this quickstart we'll show you how to build a simple LLM application with LangChain. This application will translate text from English into another language. This is a relatively simple LLM application - it's just a single LLM call plus some prompting. Still, this is a great way to get started with LangChain - a lot of features can be built with just some prompting and an LLM call!\", 'language': 'en'}, page_content='Select chat model:Groq▾GroqOpenAIAnthropicAzureGoogle VertexAWSCohereNVIDIAFireworks AIMistral AITogether AIDatabrickspip install -qU \"langchain[groq]\"import getpassimport osif not os.environ.get(\"GROQ_API_KEY\"):  os.environ[\"GROQ_API_KEY\"] = getpass.getpass(\"Enter API key for Groq: \")from langchain.chat_models import init_chat_modelmodel = init_chat_model(\"llama3-8b-8192\", model_provider=\"groq\")\\nLet\\'s first use the model directly. ChatModels are instances of LangChain Runnables, which means they expose a standard interface for interacting with them. To simply call the model, we can pass in a list of messages to the .invoke method.\\nfrom langchain_core.messages import HumanMessage, SystemMessagemessages = [    SystemMessage(\"Translate the following from English into Italian\"),    HumanMessage(\"hi!\"),]model.invoke(messages)API Reference:HumanMessage | SystemMessage'),\n", " Document(metadata={'source': 'https://python.langchain.com/docs/tutorials/llm_chain/', 'title': 'Build a simple LLM application with chat models and prompt templates | 🦜️🔗 LangChain', 'description': \"In this quickstart we'll show you how to build a simple LLM application with <PERSON><PERSON>hain. This application will translate text from English into another language. This is a relatively simple LLM application - it's just a single LLM call plus some prompting. Still, this is a great way to get started with LangChain - a lot of features can be built with just some prompting and an LLM call!\", 'language': 'en'}, page_content=\"AIMessage(content='Ciao!', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 3, 'prompt_tokens': 20, 'total_tokens': 23, 'completion_tokens_details': {'accepted_prediction_tokens': 0, 'audio_tokens': 0, 'reasoning_tokens': 0, 'rejected_prediction_tokens': 0}, 'prompt_tokens_details': {'audio_tokens': 0, 'cached_tokens': 0}}, 'model_name': 'gpt-4o-mini-2024-07-18', 'system_fingerprint': 'fp_0705bf87c0', 'finish_reason': 'stop', 'logprobs': None}, id='run-32654a56-627c-40e1-a141-ad9350bbfd3e-0', usage_metadata={'input_tokens': 20, 'output_tokens': 3, 'total_tokens': 23, 'input_token_details': {'audio': 0, 'cache_read': 0}, 'output_token_details': {'audio': 0, 'reasoning': 0}})\\ntipIf we've enabled LangSmith, we can see that this run is logged to LangSmith, and can see the LangSmith trace. The LangSmith trace reports token usage information, latency, standard model parameters (such as temperature), and other information.\"),\n", " Document(metadata={'source': 'https://python.langchain.com/docs/tutorials/llm_chain/', 'title': 'Build a simple LLM application with chat models and prompt templates | 🦜️🔗 LangChain', 'description': \"In this quickstart we'll show you how to build a simple LLM application with LangChain. This application will translate text from English into another language. This is a relatively simple LLM application - it's just a single LLM call plus some prompting. Still, this is a great way to get started with LangChain - a lot of features can be built with just some prompting and an LLM call!\", 'language': 'en'}, page_content='Note that ChatModels receive message objects as input and generate message objects as output. In addition to text content, message objects convey conversational roles and hold important data, such as tool calls and token usage counts.\\nLangChain also supports chat model inputs via strings or OpenAI format. The following are equivalent:\\nmodel.invoke(\"Hello\")model.invoke([{\"role\": \"user\", \"content\": \"Hello\"}])model.invoke([HumanMessage(\"Hello\")])\\nStreaming\\u200b\\nBecause chat models are Runnables, they expose a standard interface that includes async and streaming modes of invocation. This allows us to stream individual tokens from a chat model:\\nfor token in model.stream(messages):    print(token.content, end=\"|\")\\n|C|iao|!||\\nYou can find more details on streaming chat model outputs in this guide.\\nPrompt Templates\\u200b'),\n", " Document(metadata={'source': 'https://python.langchain.com/docs/tutorials/llm_chain/', 'title': 'Build a simple LLM application with chat models and prompt templates | 🦜️🔗 LangChain', 'description': \"In this quickstart we'll show you how to build a simple LLM application with LangChain. This application will translate text from English into another language. This is a relatively simple LLM application - it's just a single LLM call plus some prompting. Still, this is a great way to get started with LangChain - a lot of features can be built with just some prompting and an LLM call!\", 'language': 'en'}, page_content='for token in model.stream(messages):    print(token.content, end=\"|\")\\n|C|iao|!||\\nYou can find more details on streaming chat model outputs in this guide.\\nPrompt Templates\\u200b\\nRight now we are passing a list of messages directly into the language model. Where does this list of messages come from? Usually, it is constructed from a combination of user input and application logic. This application logic usually takes the raw user input and transforms it into a list of messages ready to pass to the language model. Common transformations include adding a system message or formatting a template with the user input.\\nPrompt templates are a concept in LangChain designed to assist with this transformation. They take in raw user input and return data (a prompt) that is ready to pass into a language model.\\nLet\\'s create a prompt template here. It will take in two user variables:'),\n", " Document(metadata={'source': 'https://python.langchain.com/docs/tutorials/llm_chain/', 'title': 'Build a simple LLM application with chat models and prompt templates | 🦜️🔗 LangChain', 'description': \"In this quickstart we'll show you how to build a simple LLM application with LangChain. This application will translate text from English into another language. This is a relatively simple LLM application - it's just a single LLM call plus some prompting. Still, this is a great way to get started with LangChain - a lot of features can be built with just some prompting and an LLM call!\", 'language': 'en'}, page_content='language: The language to translate text into\\ntext: The text to translate'),\n", " Document(metadata={'source': 'https://python.langchain.com/docs/tutorials/llm_chain/', 'title': 'Build a simple LLM application with chat models and prompt templates | 🦜️🔗 LangChain', 'description': \"In this quickstart we'll show you how to build a simple LLM application with <PERSON><PERSON>hain. This application will translate text from English into another language. This is a relatively simple LLM application - it's just a single LLM call plus some prompting. Still, this is a great way to get started with LangChain - a lot of features can be built with just some prompting and an LLM call!\", 'language': 'en'}, page_content='from langchain_core.prompts import ChatPromptTemplatesystem_template = \"Translate the following from English into {language}\"prompt_template = ChatPromptTemplate.from_messages(    [(\"system\", system_template), (\"user\", \"{text}\")])API Reference:ChatPromptTemplate\\nNote that ChatPromptTemplate supports multiple message roles in a single template. We format the language parameter into the system message, and the user text into a user message.\\nThe input to this prompt template is a dictionary. We can play around with this prompt template by itself to see what it does by itself\\nprompt = prompt_template.invoke({\"language\": \"Italian\", \"text\": \"hi!\"})prompt\\nChatPromptValue(messages=[SystemMessage(content=\\'Translate the following from English into Italian\\', additional_kwargs={}, response_metadata={}), HumanMessage(content=\\'hi!\\', additional_kwargs={}, response_metadata={})])'),\n", " Document(metadata={'source': 'https://python.langchain.com/docs/tutorials/llm_chain/', 'title': 'Build a simple LLM application with chat models and prompt templates | 🦜️🔗 LangChain', 'description': \"In this quickstart we'll show you how to build a simple LLM application with <PERSON><PERSON>hain. This application will translate text from English into another language. This is a relatively simple LLM application - it's just a single LLM call plus some prompting. Still, this is a great way to get started with LangChain - a lot of features can be built with just some prompting and an LLM call!\", 'language': 'en'}, page_content=\"We can see that it returns a ChatPromptValue that consists of two messages. If we want to access the messages directly we do:\\nprompt.to_messages()\\n[SystemMessage(content='Translate the following from English into Italian', additional_kwargs={}, response_metadata={}), HumanMessage(content='hi!', additional_kwargs={}, response_metadata={})]\\nFinally, we can invoke the chat model on the formatted prompt:\\nresponse = model.invoke(prompt)print(response.content)\\nCiao!\\ntipMessage content can contain both text and content blocks with additional structure. See this guide for more information.\\nIf we take a look at the <PERSON>Smith trace, we can see exactly what prompt the chat model receives, along with token usage information, latency, standard model parameters (such as temperature), and other information.\\nConclusion\\u200b\"),\n", " Document(metadata={'source': 'https://python.langchain.com/docs/tutorials/llm_chain/', 'title': 'Build a simple LLM application with chat models and prompt templates | 🦜️🔗 LangChain', 'description': \"In this quickstart we'll show you how to build a simple LLM application with LangChain. This application will translate text from English into another language. This is a relatively simple LLM application - it's just a single LLM call plus some prompting. Still, this is a great way to get started with LangChain - a lot of features can be built with just some prompting and an LLM call!\", 'language': 'en'}, page_content=\"Conclusion\\u200b\\nThat's it! In this tutorial you've learned how to create your first simple LLM application. You've learned how to work with language models, how to create a prompt template, and how to get great observability into applications you create with LangSmith.\\nThis just scratches the surface of what you will want to learn to become a proficient AI Engineer. Luckily - we've got a lot of other resources!\\nFor further reading on the core concepts of Lang<PERSON>hai<PERSON>, we've got detailed Conceptual Guides.\\nIf you have more specific questions on these concepts, check out the following sections of the how-to guides:\"),\n", " Document(metadata={'source': 'https://python.langchain.com/docs/tutorials/llm_chain/', 'title': 'Build a simple LLM application with chat models and prompt templates | 🦜️🔗 LangChain', 'description': \"In this quickstart we'll show you how to build a simple LLM application with <PERSON><PERSON>hai<PERSON>. This application will translate text from English into another language. This is a relatively simple LLM application - it's just a single LLM call plus some prompting. Still, this is a great way to get started with LangChain - a lot of features can be built with just some prompting and an LLM call!\", 'language': 'en'}, page_content='Chat models\\nPrompt templates\\n\\nAnd the Lang<PERSON>mith docs:\\n\\nLangSmith\\nEdit this pageWas this page helpful?PreviousTutorialsNextBuild a ChatbotSetupJupyter NotebookInstallationLangSmithUsing Language ModelsStreamingPrompt TemplatesConclusionCommunityTwitterGitHubOrganizationPythonJS/TSMoreHomepageBlogYouTubeCopyright © 2025 LangChain, Inc.')]"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_text_splitters import RecursiveCharacterTextSplitter\n", "\n", "text_splitter=RecursiveCharacterTextSplitter(chunk_size=1000,chunk_overlap=200)\n", "documents=text_splitter.split_documents(document)\n", "documents"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [], "source": ["from langchain_openai import OpenAIEmbeddings\n", "embeddings=OpenAIEmbeddings()"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"data": {"text/plain": ["<langchain_community.vectorstores.faiss.FAISS at 0x2524d037920>"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_community.vectorstores import FAISS\n", "\n", "vectorstore=FAISS.from_documents(documents,embeddings)\n", "vectorstore"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["\"In this quickstart we'll show you how to build a simple LLM application with LangChain. This application will translate text from English into another language. This is a relatively simple LLM application - it's just a single LLM call plus some prompting. Still, this is a great way to get started with LangChain - a lot of features can be built with just some prompting and an LLM call!\\nAfter reading this tutorial, you'll have a high level overview of:\""]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["query=\"This is a relatively simple LLM application \"\n", "\n", "result=vectorstore.similarity_search(query)\n", "result[0].page_content"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [], "source": ["from langchain_core.prompts import ChatPromptTemplate\n", "\n", "prompt=ChatPromptTemplate.from_template(\n", "    \"\"\"\n", "Answer the following question based only on the provided context:\n", "<context>\n", "{context}\n", "</context>\n", "\n", "\n", "\"\"\"\n", ")"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/plain": ["ChatPromptTemplate(input_variables=['context'], input_types={}, partial_variables={}, messages=[HumanMessagePromptTemplate(prompt=PromptTemplate(input_variables=['context'], input_types={}, partial_variables={}, template='\\nAnswer the following question based only on the provided context:\\n<context>\\n{context}\\n</context>\\n\\n\\n'), additional_kwargs={})])"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["prompt"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"data": {"text/plain": ["RunnableBinding(bound=RunnableBinding(bound=RunnableAssign(mapper={\n", "  context: RunnableLambda(format_docs)\n", "}), kwargs={}, config={'run_name': 'format_inputs'}, config_factories=[])\n", "| ChatPromptTemplate(input_variables=['context'], input_types={}, partial_variables={}, messages=[SystemMessagePromptTemplate(prompt=PromptTemplate(input_variables=['context'], input_types={}, partial_variables={}, template=\"What are everyone's favorite colors:\\n\\n{context}\"), additional_kwargs={})])\n", "| ChatOpenAI(client=<openai.resources.chat.completions.Completions object at 0x000002511F884110>, async_client=<openai.resources.chat.completions.AsyncCompletions object at 0x000002511F825A00>, model_kwargs={}, openai_api_key='********************************************************************************************************************************************************************', openai_proxy='')\n", "| StrOutputParser(), kwargs={}, config={'run_name': 'stuff_documents_chain'}, config_factories=[])"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["# pip install -U langchain langchain-community\n", "\n", "from langchain_community.chat_models import ChatOpenAI\n", "from langchain_core.documents import Document\n", "from langchain_core.prompts import ChatPromptTemplate\n", "from langchain.chains.combine_documents import create_stuff_documents_chain\n", "\n", "prompt = ChatPromptTemplate.from_messages(\n", "    [(\"system\", \"What are everyone's favorite colors:\\n\\n{context}\")]\n", ")\n", "llm = ChatOpenAI(model=\"gpt-3.5-turbo\")\n", "chain = create_stuff_documents_chain(llm, prompt)\n", "\n", "chain"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"data": {"text/plain": ["'\\n\\nJesse: Red\\nJamal: Green, Orange'"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["docs = [\n", "    Document(page_content=\"<PERSON> loves red but not yellow\"),\n", "    Document(page_content = \"<PERSON> loves green but not as much as he loves orange\")\n", "]\n", "\n", "chain.invoke({\"context\": docs})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"data": {"text/plain": ["RunnableBinding(bound=RunnableBinding(bound=RunnableAssign(mapper={\n", "  context: RunnableLambda(format_docs)\n", "}), kwargs={}, config={'run_name': 'format_inputs'}, config_factories=[])\n", "| ChatPromptTemplate(input_variables=['context'], input_types={}, partial_variables={}, messages=[HumanMessagePromptTemplate(prompt=PromptTemplate(input_variables=['context'], input_types={}, partial_variables={}, template='\\nAnswer the following question based only on the provided context:\\n<context>\\n{context}\\n</context>\\n\\n\\n'), additional_kwargs={})])\n", "| ChatOpenAI(client=<openai.resources.chat.completions.Completions object at 0x000002511F884110>, async_client=<openai.resources.chat.completions.AsyncCompletions object at 0x000002511F825A00>, model_kwargs={}, openai_api_key='********************************************************************************************************************************************************************', openai_proxy='')\n", "| StrOutputParser(), kwargs={}, config={'run_name': 'stuff_documents_chain'}, config_factories=[])"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain.chains.combine_documents import create_stuff_documents_chain\n", "document_chain=create_stuff_documents_chain(llm,prompt)\n", "document_chain"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"data": {"text/plain": ["[Document(id='3e8bdbfd-f6d5-4325-83f7-5e1bcc37c768', metadata={'source': 'https://python.langchain.com/docs/tutorials/llm_chain/', 'title': 'Build a simple LLM application with chat models and prompt templates | 🦜️🔗 LangChain', 'description': \"In this quickstart we'll show you how to build a simple LLM application with LangChain. This application will translate text from English into another language. This is a relatively simple LLM application - it's just a single LLM call plus some prompting. Still, this is a great way to get started with Lang<PERSON>hain - a lot of features can be built with just some prompting and an LLM call!\", 'language': 'en'}, page_content='Note that ChatModels receive message objects as input and generate message objects as output. In addition to text content, message objects convey conversational roles and hold important data, such as tool calls and token usage counts.\\nLangChain also supports chat model inputs via strings or OpenAI format. The following are equivalent:\\nmodel.invoke(\"Hello\")model.invoke([{\"role\": \"user\", \"content\": \"Hello\"}])model.invoke([HumanMessage(\"Hello\")])\\nStreaming\\u200b\\nBecause chat models are Runnables, they expose a standard interface that includes async and streaming modes of invocation. This allows us to stream individual tokens from a chat model:\\nfor token in model.stream(messages):    print(token.content, end=\"|\")\\n|C|iao|!||\\nYou can find more details on streaming chat model outputs in this guide.\\nPrompt Templates\\u200b'),\n", " Document(id='3a8daad3-23f4-4654-ba03-6bcbc927196d', metadata={'source': 'https://python.langchain.com/docs/tutorials/llm_chain/', 'title': 'Build a simple LLM application with chat models and prompt templates | 🦜️🔗 LangChain', 'description': \"In this quickstart we'll show you how to build a simple LLM application with LangChain. This application will translate text from English into another language. This is a relatively simple LLM application - it's just a single LLM call plus some prompting. Still, this is a great way to get started with Lang<PERSON>hain - a lot of features can be built with just some prompting and an LLM call!\", 'language': 'en'}, page_content='for token in model.stream(messages):    print(token.content, end=\"|\")\\n|C|iao|!||\\nYou can find more details on streaming chat model outputs in this guide.\\nPrompt Templates\\u200b\\nRight now we are passing a list of messages directly into the language model. Where does this list of messages come from? Usually, it is constructed from a combination of user input and application logic. This application logic usually takes the raw user input and transforms it into a list of messages ready to pass to the language model. Common transformations include adding a system message or formatting a template with the user input.\\nPrompt templates are a concept in LangChain designed to assist with this transformation. They take in raw user input and return data (a prompt) that is ready to pass into a language model.\\nLet\\'s create a prompt template here. It will take in two user variables:'),\n", " Document(id='0d51b3e7-9da1-4f00-8082-98c5e1591046', metadata={'source': 'https://python.langchain.com/docs/tutorials/llm_chain/', 'title': 'Build a simple LLM application with chat models and prompt templates | 🦜️🔗 LangChain', 'description': \"In this quickstart we'll show you how to build a simple LLM application with LangChain. This application will translate text from English into another language. This is a relatively simple LLM application - it's just a single LLM call plus some prompting. Still, this is a great way to get started with LangChain - a lot of features can be built with just some prompting and an LLM call!\", 'language': 'en'}, page_content='Select chat model:Groq▾GroqOpenAIAnthropicAzureGoogle VertexAWSCohereNVIDIAFireworks AIMistral AITogether AIDatabrickspip install -qU \"langchain[groq]\"import getpassimport osif not os.environ.get(\"GROQ_API_KEY\"):  os.environ[\"GROQ_API_KEY\"] = getpass.getpass(\"Enter API key for Groq: \")from langchain.chat_models import init_chat_modelmodel = init_chat_model(\"llama3-8b-8192\", model_provider=\"groq\")\\nLet\\'s first use the model directly. ChatModels are instances of LangChain Runnables, which means they expose a standard interface for interacting with them. To simply call the model, we can pass in a list of messages to the .invoke method.\\nfrom langchain_core.messages import HumanMessage, SystemMessagemessages = [    SystemMessage(\"Translate the following from English into Italian\"),    HumanMessage(\"hi!\"),]model.invoke(messages)API Reference:HumanMessage | SystemMessage'),\n", " Document(id='8d0a0523-75da-41c5-a218-15330bae8852', metadata={'source': 'https://python.langchain.com/docs/tutorials/llm_chain/', 'title': 'Build a simple LLM application with chat models and prompt templates | 🦜️🔗 LangChain', 'description': \"In this quickstart we'll show you how to build a simple LLM application with LangChain. This application will translate text from English into another language. This is a relatively simple LLM application - it's just a single LLM call plus some prompting. Still, this is a great way to get started with LangChain - a lot of features can be built with just some prompting and an LLM call!\", 'language': 'en'}, page_content=\"We can see that it returns a ChatPromptValue that consists of two messages. If we want to access the messages directly we do:\\nprompt.to_messages()\\n[SystemMessage(content='Translate the following from English into Italian', additional_kwargs={}, response_metadata={}), HumanMessage(content='hi!', additional_kwargs={}, response_metadata={})]\\nFinally, we can invoke the chat model on the formatted prompt:\\nresponse = model.invoke(prompt)print(response.content)\\nCiao!\\ntipMessage content can contain both text and content blocks with additional structure. See this guide for more information.\\nIf we take a look at the LangSmith trace, we can see exactly what prompt the chat model receives, along with token usage information, latency, standard model parameters (such as temperature), and other information.\\nConclusion\\u200b\")]"]}, "execution_count": 39, "metadata": {}, "output_type": "execute_result"}], "source": ["vectorstore.similarity_search(\"Note that ChatModels receive message objects as inpu\")"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"data": {"text/plain": ["'What are some ways that <PERSON><PERSON><PERSON><PERSON> supports chat model inputs?'"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["document_chain.invoke({\n", "    \"input\":\"Note that ChatModels receive message objects as input\",\n", "    \"context\":[Document(page_content=\"Note that ChatModels receive message objects as input and generate message objects as output. In addition to text content, message objects convey conversational roles and hold important data, such as tool calls and token usage counts.\\nLangChain also supports chat model inputs via strings or OpenAI format. The following are equivalent:\\nmodel.invoke('Hello')model.invoke([{'role': 'user', 'content': 'Hello'}])model.invoke([HumanMessage('Hello')]) StreamingBecause chat models are Runnables, they expose a standard interface that includes async and streaming modes of invocation. This allows us to stream individual tokens from a chat model:\\nfor token in model.stream(messages):    print(token.content, end='|')|C|iao|!|| You can find more details on streaming chat model outputs in this guide.\\nPrompt Templates\")]\n", "})"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [{"data": {"text/plain": ["RunnableBinding(bound=RunnableAssign(mapper={\n", "  context: RunnableBinding(bound=RunnableLambda(lambda x: x['input'])\n", "           | VectorStoreRetriever(tags=['FAISS', 'OpenAIEmbeddings'], vectorstore=<langchain_community.vectorstores.faiss.FAISS object at 0x000002524D037920>, search_kwargs={}), kwargs={}, config={'run_name': 'retrieve_documents'}, config_factories=[])\n", "})\n", "| RunnableAssign(mapper={\n", "    answer: RunnableBinding(bound=RunnableBinding(bound=RunnableAssign(mapper={\n", "              context: RunnableLambda(format_docs)\n", "            }), kwargs={}, config={'run_name': 'format_inputs'}, config_factories=[])\n", "            | ChatPromptTemplate(input_variables=['context'], input_types={}, partial_variables={}, messages=[HumanMessagePromptTemplate(prompt=PromptTemplate(input_variables=['context'], input_types={}, partial_variables={}, template='\\nAnswer the following question based only on the provided context:\\n<context>\\n{context}\\n</context>\\n\\n\\n'), additional_kwargs={})])\n", "            | ChatOpenAI(client=<openai.resources.chat.completions.Completions object at 0x000002511F884110>, async_client=<openai.resources.chat.completions.AsyncCompletions object at 0x000002511F825A00>, model_kwargs={}, openai_api_key='********************************************************************************************************************************************************************', openai_proxy='')\n", "            | StrOutputParser(), kwargs={}, config={'run_name': 'stuff_documents_chain'}, config_factories=[])\n", "  }), kwargs={}, config={'run_name': 'retrieval_chain'}, config_factories=[])"]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["retriever=vectorstore.as_retriever()\n", "\n", "from langchain.chains import create_retrieval_chain\n", "\n", "retrieval_chain=create_retrieval_chain(retriever,document_chain)\n", "retrieval_chain"]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [], "source": ["result=retrieval_chain.invoke({\"input\":\"Note that ChatModels receive message objects as input\"})"]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [{"data": {"text/plain": ["'The list of messages passed into the language model usually comes from a combination of user input and application logic. Prompt templates in LangChain assist with transforming raw user input into a format ready to pass into a language model. These prompt templates take in user variables and return a prompt that can be passed into the model.'"]}, "execution_count": 47, "metadata": {}, "output_type": "execute_result"}], "source": ["result['answer']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "ai", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.0"}}, "nbformat": 4, "nbformat_minor": 2}