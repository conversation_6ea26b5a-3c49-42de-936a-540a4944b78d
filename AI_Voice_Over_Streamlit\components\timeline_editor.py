#!/usr/bin/env python3
"""
Timeline Editor Component
Interactive timeline editing interface for Streamlit
"""

import streamlit as st
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import pandas as pd
import numpy as np
from typing import List, Dict, Optional
from datetime import timedelta

class TimelineEditor:
    """Interactive timeline editor for audio segments"""
    
    def __init__(self):
        self.current_time = 0.0
        self.selected_segment = None
        
    def render_timeline(self, segments: List[Dict], total_duration: float, 
                       current_time: float = 0.0) -> Dict:
        """
        Render interactive timeline with audio segments
        
        Args:
            segments: List of audio segment dictionaries
            total_duration: Total duration of audio in seconds
            current_time: Current playback position
            
        Returns:
            Dictionary with user interactions and selections
        """
        st.subheader("🎵 Audio Timeline")
        
        if not segments:
            st.warning("No audio segments available")
            return {}
        
        # Create timeline visualization
        fig = self.create_timeline_plot(segments, total_duration, current_time)
        
        # Display timeline
        timeline_container = st.container()
        with timeline_container:
            selected_data = st.plotly_chart(
                fig, 
                use_container_width=True,
                key="timeline_chart",
                on_select="rerun"
            )
        
        # Timeline controls
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            if st.button("⏮️ Previous"):
                self.navigate_to_previous_segment(segments, current_time)
        
        with col2:
            play_pause = st.button("▶️ Play/Pause")
        
        with col3:
            if st.button("⏭️ Next"):
                self.navigate_to_next_segment(segments, current_time)
        
        with col4:
            if st.button("🔄 Refresh"):
                st.rerun()
        
        # Time scrubber
        st.subheader("Time Navigation")
        scrub_time = st.slider(
            "Scrub to time",
            min_value=0.0,
            max_value=total_duration,
            value=current_time,
            step=0.1,
            format="%.1f s"
        )
        
        # Segment details
        if selected_data and hasattr(selected_data, 'selection') and selected_data.selection:
            self.show_segment_details(segments, selected_data.selection)
        
        return {
            'current_time': scrub_time,
            'play_pause_clicked': play_pause,
            'selected_data': selected_data
        }
    
    def create_timeline_plot(self, segments: List[Dict], total_duration: float, 
                           current_time: float) -> go.Figure:
        """Create interactive timeline plot"""
        
        # Create subplot with secondary y-axis for waveform
        fig = make_subplots(
            rows=2, cols=1,
            row_heights=[0.7, 0.3],
            subplot_titles=("Audio Segments", "Waveform"),
            vertical_spacing=0.1
        )
        
        # Add segment bars
        for i, segment in enumerate(segments):
            # Determine color based on modification status
            color = 'lightcoral' if segment.get('is_modified', False) else 'lightblue'
            
            fig.add_trace(
                go.Bar(
                    x=[segment['duration']],
                    y=[f"Segment {i+1}"],
                    orientation='h',
                    base=segment['start_time'],
                    name=f"Segment {i+1}",
                    text=f"{segment.get('text', '')[:30]}...",
                    textposition='inside',
                    marker_color=color,
                    hovertemplate=(
                        f"<b>Segment {i+1}</b><br>"
                        f"Start: {segment['start_time']:.2f}s<br>"
                        f"End: {segment['end_time']:.2f}s<br>"
                        f"Duration: {segment['duration']:.2f}s<br>"
                        f"Text: {segment.get('text', 'No text')}<br>"
                        f"<extra></extra>"
                    )
                ),
                row=1, col=1
            )
        
        # Add current time indicator
        fig.add_vline(
            x=current_time,
            line_dash="dash",
            line_color="red",
            annotation_text=f"Current: {current_time:.1f}s",
            row=1, col=1
        )
        
        # Add simplified waveform representation
        self.add_waveform_representation(fig, total_duration, current_time)
        
        # Update layout
        fig.update_layout(
            title="Interactive Audio Timeline",
            xaxis_title="Time (seconds)",
            height=500,
            showlegend=False,
            dragmode='select'
        )
        
        # Update x-axis range
        fig.update_xaxes(range=[0, total_duration])
        
        return fig
    
    def add_waveform_representation(self, fig: go.Figure, total_duration: float, 
                                  current_time: float):
        """Add simplified waveform representation"""
        # Generate mock waveform data
        time_points = np.linspace(0, total_duration, 1000)
        waveform = np.random.normal(0, 0.5, 1000) * np.exp(-0.1 * time_points)
        
        fig.add_trace(
            go.Scatter(
                x=time_points,
                y=waveform,
                mode='lines',
                name='Waveform',
                line=dict(color='blue', width=1),
                fill='tonexty'
            ),
            row=2, col=1
        )
        
        # Add current time indicator to waveform
        fig.add_vline(
            x=current_time,
            line_dash="dash",
            line_color="red",
            row=2, col=1
        )
    
    def show_segment_details(self, segments: List[Dict], selection_data):
        """Show details for selected segment"""
        st.subheader("📝 Segment Details")
        
        # For now, show first segment details
        # In a real implementation, we'd parse the selection data
        if segments:
            segment = segments[0]
            
            col1, col2 = st.columns(2)
            
            with col1:
                st.metric("Start Time", f"{segment['start_time']:.2f}s")
                st.metric("Duration", f"{segment['duration']:.2f}s")
            
            with col2:
                st.metric("End Time", f"{segment['end_time']:.2f}s")
                st.metric("Confidence", f"{segment.get('confidence', 0):.2f}")
            
            # Text editing
            current_text = segment.get('text', '')
            new_text = st.text_area(
                "Edit transcript",
                value=current_text,
                height=100,
                key=f"segment_text_{segment.get('start_time', 0)}"
            )
            
            if new_text != current_text:
                if st.button("💾 Save Changes"):
                    # Update segment text
                    segment['text'] = new_text
                    segment['is_modified'] = True
                    st.success("Segment updated!")
                    st.rerun()
    
    def navigate_to_previous_segment(self, segments: List[Dict], current_time: float) -> float:
        """Navigate to previous segment"""
        for segment in reversed(segments):
            if segment['start_time'] < current_time:
                return segment['start_time']
        return 0.0
    
    def navigate_to_next_segment(self, segments: List[Dict], current_time: float) -> float:
        """Navigate to next segment"""
        for segment in segments:
            if segment['start_time'] > current_time:
                return segment['start_time']
        return segments[-1]['end_time'] if segments else 0.0
    
    def render_segment_list(self, segments: List[Dict]) -> Optional[int]:
        """Render list of segments for selection"""
        st.subheader("📋 Segment List")
        
        if not segments:
            st.info("No segments available")
            return None
        
        # Create DataFrame for display
        segment_data = []
        for i, segment in enumerate(segments):
            segment_data.append({
                'Segment': i + 1,
                'Start': f"{segment['start_time']:.2f}s",
                'End': f"{segment['end_time']:.2f}s",
                'Duration': f"{segment['duration']:.2f}s",
                'Text': segment.get('text', '')[:50] + '...' if len(segment.get('text', '')) > 50 else segment.get('text', ''),
                'Modified': '✅' if segment.get('is_modified', False) else '❌'
            })
        
        df = pd.DataFrame(segment_data)
        
        # Display as interactive table
        selected_rows = st.dataframe(
            df,
            use_container_width=True,
            hide_index=True,
            on_select="rerun",
            selection_mode="single-row"
        )
        
        if hasattr(selected_rows, 'selection') and selected_rows.selection.rows:
            return selected_rows.selection.rows[0]
        
        return None
    
    def export_timeline_data(self, segments: List[Dict]) -> str:
        """Export timeline data as JSON"""
        import json
        
        export_data = {
            'segments': segments,
            'export_timestamp': pd.Timestamp.now().isoformat(),
            'total_segments': len(segments)
        }
        
        return json.dumps(export_data, indent=2)
