# 🚀 Quick Start Guide

## Installation Options

### Option 1: Automatic Installation (Recommended)
```bash
cd AI_Voice_Over_Streamlit
python install.py
```

### Option 2: Manual Installation
```bash
cd AI_Voice_Over_Streamlit
pip install -r requirements.txt
```

### Option 3: Step-by-Step Installation
If you encounter issues, install packages individually:

```bash
# Core packages
pip install streamlit numpy pandas requests python-dateutil pillow tqdm python-dotenv

# Azure packages
pip install azure-cognitiveservices-speech azure-storage-blob azure-identity azure-mgmt-storage

# Audio processing
pip install moviepy pydub scipy plotly matplotlib

# Advanced audio (optional)
pip install librosa soundfile noisereduce
```

## Prerequisites

1. **Python 3.8+** - Check with `python --version`
2. **Azure Account** - With Speech Services and Storage configured
3. **FFmpeg** - For video processing (install separately)

### Installing FFmpeg
- **Windows**: Download from [ffmpeg.org](https://ffmpeg.org/download.html)
- **macOS**: `brew install ffmpeg`
- **Linux**: `sudo apt install ffmpeg`

## Running the Application

### Method 1: Using the launcher
```bash
python run_app.py
```

### Method 2: Direct Streamlit
```bash
streamlit run app.py
```

The application will open in your browser at `http://localhost:8501`

## First Time Setup

1. **Verify Azure Configuration**
   - Ensure your `AI_Voice_Over_Azure` system is properly configured
   - Check that Azure credentials are valid

2. **Test with a Sample Video**
   - Use a short video (< 1 minute) for initial testing
   - Supported formats: MP4, AVI, MOV, MKV, WEBM

## Basic Usage Flow

1. **🎬 Upload Video**: Provide video URL or upload file
2. **🎵 Extract Timeline**: Process audio with timestamps
3. **✨ Polish Audio**: Apply AI enhancement (optional)
4. **📝 Edit Transcript**: Modify text with timeline sync
5. **🎥 Preview**: Review synchronized result
6. **📤 Export**: Download final video

## Troubleshooting

### Common Issues

**"Azure modules not found"**
- Ensure the parent `AI_Voice_Over_Azure` directory exists
- Check Azure package installation

**"FFmpeg not found"**
- Install FFmpeg system-wide
- Add FFmpeg to system PATH

**"Audio processing failed"**
- Check video file format compatibility
- Ensure sufficient disk space
- Try with a smaller video file

**"Timeline not loading"**
- Refresh the page
- Check browser console for errors
- Try a different browser

### Performance Tips

- Use shorter videos (< 5 minutes) for faster processing
- Close other browser tabs to free memory
- Ensure stable internet connection for Azure services

## Feature Limitations

Current version includes:
- ✅ Video upload and processing
- ✅ Audio timeline extraction
- ✅ Basic AI audio polishing
- ✅ Timeline-based transcript editing
- ✅ Synchronized preview
- ✅ Multi-format export

Future enhancements:
- 🔄 Real-time audio preview
- 🔄 Advanced waveform visualization
- 🔄 Batch processing
- 🔄 Cloud deployment

## Getting Help

1. Check the main [README.md](README.md) for detailed documentation
2. Review error messages in the Streamlit interface
3. Check the console output for technical details
4. Ensure all prerequisites are properly installed

## Example Workflow

```bash
# 1. Install
python install.py

# 2. Run
python run_app.py

# 3. In browser:
#    - Upload video: "https://example.com/sample.mp4"
#    - Select voice: "Aria"
#    - Process through each step
#    - Export final result
```

---

**Note**: This system extends your existing Azure AI Voice-Over functionality with an enhanced Streamlit interface. All original features remain available in the command-line version.
