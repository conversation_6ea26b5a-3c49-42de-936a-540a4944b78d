#!/usr/bin/env python3
"""
Session State Management Utilities
Helper functions for managing Streamlit session state
"""

import streamlit as st
import json
import os
from typing import Dict, Any, Optional
from pathlib import Path

class SessionStateManager:
    """Manages Streamlit session state for the voice-over application"""
    
    @staticmethod
    def initialize_session_state():
        """Initialize all required session state variables"""
        
        # Project data
        if 'project_data' not in st.session_state:
            st.session_state.project_data = {
                'video_url': None,
                'video_file': None,
                'selected_voice': None,
                'audio_timeline': None,
                'transcription': None,
                'polished_audio_path': None,
                'enhancement_results': None,
                'edited_timeline_path': None,
                'final_video_path': None,
                'project_dir': None,
                'current_step': 1
            }
        
        # Processing components
        if 'azure_converter' not in st.session_state:
            try:
                import sys
                from pathlib import Path
                parent_dir = Path(__file__).parent.parent.parent
                sys.path.append(str(parent_dir / "AI_Voice_Over_Azure"))
                from azure_voice_converter import AzureVoiceConverter
                st.session_state.azure_converter = AzureVoiceConverter()
            except Exception as e:
                st.error(f"Failed to initialize Azure converter: {e}")
                st.stop()
        
        # UI state
        if 'current_playback_time' not in st.session_state:
            st.session_state.current_playback_time = 0.0
        
        if 'selected_segment' not in st.session_state:
            st.session_state.selected_segment = None
        
        if 'timeline_editor' not in st.session_state:
            st.session_state.timeline_editor = None
        
        if 'audio_processor' not in st.session_state:
            st.session_state.audio_processor = None
        
        if 'audio_polisher' not in st.session_state:
            st.session_state.audio_polisher = None
    
    @staticmethod
    def save_project_state(project_id: str) -> bool:
        """Save current project state to file"""
        try:
            from config import get_project_path
            
            project_path = get_project_path(project_id)
            project_path.mkdir(parents=True, exist_ok=True)
            
            # Prepare serializable data
            state_data = {
                'project_data': st.session_state.project_data.copy(),
                'current_playback_time': st.session_state.current_playback_time,
                'selected_segment': st.session_state.selected_segment
            }
            
            # Remove non-serializable items
            if 'video_file' in state_data['project_data']:
                state_data['project_data']['video_file'] = None
            
            state_file = project_path / "project_state.json"
            with open(state_file, 'w') as f:
                json.dump(state_data, f, indent=2, default=str)
            
            return True
            
        except Exception as e:
            st.error(f"Failed to save project state: {e}")
            return False
    
    @staticmethod
    def load_project_state(project_id: str) -> bool:
        """Load project state from file"""
        try:
            from config import get_project_path
            
            project_path = get_project_path(project_id)
            state_file = project_path / "project_state.json"
            
            if not state_file.exists():
                return False
            
            with open(state_file, 'r') as f:
                state_data = json.load(f)
            
            # Restore session state
            st.session_state.project_data.update(state_data.get('project_data', {}))
            st.session_state.current_playback_time = state_data.get('current_playback_time', 0.0)
            st.session_state.selected_segment = state_data.get('selected_segment')
            
            return True
            
        except Exception as e:
            st.error(f"Failed to load project state: {e}")
            return False
    
    @staticmethod
    def reset_project_state():
        """Reset project state to initial values"""
        st.session_state.project_data = {
            'video_url': None,
            'video_file': None,
            'selected_voice': None,
            'audio_timeline': None,
            'transcription': None,
            'polished_audio_path': None,
            'enhancement_results': None,
            'edited_timeline_path': None,
            'final_video_path': None,
            'project_dir': None,
            'current_step': 1
        }
        st.session_state.current_playback_time = 0.0
        st.session_state.selected_segment = None
    
    @staticmethod
    def get_project_progress() -> Dict[str, Any]:
        """Get current project progress information"""
        project_data = st.session_state.project_data
        
        steps_completed = []
        if project_data.get('video_url') or project_data.get('video_file'):
            steps_completed.append("Video Upload")
        
        if project_data.get('audio_timeline'):
            steps_completed.append("Audio Timeline")
        
        if project_data.get('polished_audio_path'):
            steps_completed.append("AI Polish")
        
        if project_data.get('edited_timeline_path'):
            steps_completed.append("Transcript Edit")
        
        if project_data.get('final_video_path'):
            steps_completed.append("Final Export")
        
        return {
            'current_step': project_data.get('current_step', 1),
            'steps_completed': steps_completed,
            'total_steps': 6,
            'progress_percentage': (len(steps_completed) / 6) * 100
        }
    
    @staticmethod
    def cleanup_temp_files():
        """Clean up temporary files for current project"""
        try:
            project_dir = st.session_state.project_data.get('project_dir')
            if project_dir and os.path.exists(project_dir):
                import shutil
                shutil.rmtree(project_dir)
                st.session_state.project_data['project_dir'] = None
                return True
        except Exception as e:
            st.warning(f"Could not clean up temporary files: {e}")
        return False
    
    @staticmethod
    def validate_session_state() -> bool:
        """Validate that session state is in a consistent state"""
        try:
            # Check required components
            if 'azure_converter' not in st.session_state:
                return False
            
            if 'project_data' not in st.session_state:
                return False
            
            # Check step consistency
            current_step = st.session_state.project_data.get('current_step', 1)
            
            if current_step > 1 and not (st.session_state.project_data.get('video_url') or 
                                       st.session_state.project_data.get('video_file')):
                return False
            
            if current_step > 2 and not st.session_state.project_data.get('audio_timeline'):
                return False
            
            return True
            
        except Exception:
            return False

def init_session_state():
    """Convenience function to initialize session state"""
    SessionStateManager.initialize_session_state()

def save_project(project_id: str) -> bool:
    """Convenience function to save project"""
    return SessionStateManager.save_project_state(project_id)

def load_project(project_id: str) -> bool:
    """Convenience function to load project"""
    return SessionStateManager.load_project_state(project_id)

def reset_project():
    """Convenience function to reset project"""
    SessionStateManager.reset_project_state()

def get_progress() -> Dict[str, Any]:
    """Convenience function to get progress"""
    return SessionStateManager.get_project_progress()
