{"cells": [{"cell_type": "markdown", "metadata": {"id": "NIOdYiJBGmCQ"}, "source": ["![CrewAI](https://miro.medium.com/v2/resize:fit:1400/0*-7HC-GJCxjn-Dm7i.png)\n", "\n"]}, {"cell_type": "markdown", "metadata": {"id": "8M4dbVGHBD7V"}, "source": ["### Phase 1: Introduction & Fundamentals\n", "\n", "[CrewAI Official Dumentation](https://docs.crewai.com/introduction)\n", "\n", "Topics to Discuss Here\n", "\n", "\n", "1.   Crew\n", "2.   Agents\n", "3.   Tasks\n", "4.   <PERSON><PERSON>\n", "5.   <PERSON><PERSON>\n", "\n"]}, {"cell_type": "markdown", "metadata": {"id": "y0omiFRqGLtJ"}, "source": []}, {"cell_type": "markdown", "metadata": {"id": "BeBf7JjtGMwM"}, "source": ["### Phase 2: Setting Up & Basic Agent Implementation"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "eAz48JIHN_Dd"}, "outputs": [], "source": ["# Step 1: Install CrewAI\n", "# !pip install -q crewai"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "8SU12ytQByB0", "outputId": "52b8ea34-6a48-42e9-9a67-9d4cbfd3a48f"}, "outputs": [], "source": ["# !pip show crewai"]}, {"cell_type": "markdown", "metadata": {"id": "MJxm6BQCF2RR"}, "source": ["### API Check"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"id": "qPN9X_SGIG4c"}, "outputs": [], "source": ["# !pip install -Uq openai"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Ea-hMUBVFshw", "outputId": "985cf541-4ff3-47b5-dd36-7f3b514eddf4"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["API key loaded from userdata.\n", "Arrr, ye can use the `isinstance()` function to see if an object be an instance of a class in Python. The syntax be as follows:\n", "\n", "```python\n", "if isinstance(obj, ClassName):\n", "    # Do somethin'\n", "```\n", "\n", "This will return `True` if `obj` be an instance of `ClassName`, or any subclass thereof.\n"]}], "source": ["import os\n", "from google.colab import userdata\n", "from openai import OpenAI\n", "\n", "api_key = userdata.get('OPENAI_API_KEY')\n", "\n", "if api_key:\n", "  os.environ[\"OPENAI_API_KEY\"] = api_key\n", "  print(\"API key loaded from userdata.\")\n", "else:\n", "  print(\"API key not found in userdata. Please set OPENAI_API_KEY in userdata.\")\n", "\n", "################## OPENAI API CHECK ############################\n", "\n", "\n", "client = OpenAI(\n", "    api_key=os.environ.get(\"OPENAI_API_KEY\"),\n", ")\n", "\n", "response = client.responses.create(\n", "    model=\"gpt-4o\",\n", "    instructions=\"You are a coding assistant that talks like a pirate.\",\n", "    input=\"How do I check if a Python object is an instance of a class?\",\n", ")\n", "\n", "print(response.output_text)"]}, {"cell_type": "markdown", "metadata": {"id": "tc1dcDQljJZc"}, "source": ["# Optional LLMS"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["import os\n", "from dotenv import load_dotenv\n", "load_dotenv()\n", "os.environ[\"GROQ_API_KEY\"]=os.getenv(\"GROQ_API_KEY\")"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"id": "xHep5-IgjI7w"}, "outputs": [], "source": ["from crewai import LLM\n", "\n", "# Basic configuration\n", "llm = LLM(model=\"gpt-4\")\n", "\n", "# Advanced configuration with detailed parameters\n", "llm = LLM(\n", "    model=\"gpt-4o-mini\",\n", "    temperature=0.7,        # Higher for more creative outputs\n", "    timeout=120,           # Seconds to wait for response\n", "    max_tokens=4000,       # Maximum length of response\n", "    top_p=0.9,            # Nucleus sampling parameter\n", "    frequency_penalty=0.1, # Reduce repetition\n", "    presence_penalty=0.1,  # Encourage topic diversity\n", "    response_format={\"type\": \"json\"},  # For structured outputs\n", "    seed=42               # For reproducible results\n", ")\n", "\n", "# GROQ\n", "llm = LLM(\n", "    model=\"groq/llama-3.2-90b-text-preview\",\n", "    temperature=0.7\n", ")\n", "\n", "# OLLAMA\n", "llm = LLM(\n", "    model=\"ollama/llama3:70b\",\n", "    base_url=\"http://localhost:11434\"\n", ")"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"id": "7wXeSddaALeC"}, "outputs": [], "source": ["# Step 2: Import necessary libraries\n", "from crewai import Agent, Task, Crew"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"id": "r7wXZzwSALNy"}, "outputs": [], "source": ["# Step 3: Define a simple agent\n", "agent1 = Agent(\n", "    name=\"Researcher\",\n", "    description=\"An AI agent that researches and gathers information.\",\n", "    goal=\"Find relevant information on a given topic.\",\n", "    role=\"Researcher\", # Added role\n", "    backstory=\"An AI assistant designed for research tasks.\"  # Added backstory\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "kT2KkzO9ALKN"}, "outputs": [], "source": ["# Step 4: Create a simple task\n", "research_task = Task(\n", "    name=\"Research Task\",\n", "    description=\"Search for the latest advancements in AI and summarize them.\",\n", "    agent=agent1,\n", "    expected_output=\"A summary of the latest advancements in AI\" # Added expected output\n", ")"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Dq7H787KALG3", "outputId": "ce9ff329-5c28-45f8-9635-ffe608c4a2f0"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["ERROR:root:<PERSON><PERSON>LL<PERSON> call failed: litellm.NotFoundError: NotFoundError: OpenAIException - Error code: 404 - {'error': {'code': '404', 'message': 'Resource not found'}}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "LiteLLM.Info: If you need to debug this error, use `litellm._turn_on_debug()'.\n", "\n", "\u001b[91m Error during LLM call: litellm.NotFoundError: NotFoundError: OpenAIException - Error code: 404 - {'error': {'code': '404', 'message': 'Resource not found'}}\u001b[00m\n", "\u001b[91m An unknown error occurred. Please check the details below.\u001b[00m\n", "\u001b[91m Error details: litellm.NotFoundError: NotFoundError: OpenAIException - Error code: 404 - {'error': {'code': '404', 'message': 'Resource not found'}}\u001b[00m\n"]}, {"ename": "NotFoundError", "evalue": "litellm.NotFoundError: NotFoundError: OpenAIException - Error code: 404 - {'error': {'code': '404', 'message': 'Resource not found'}}", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNotFoundError\u001b[0m                             <PERSON><PERSON> (most recent call last)", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\ai\\Lib\\site-packages\\litellm\\llms\\openai\\openai.py:711\u001b[0m, in \u001b[0;36mOpenAIChatCompletion.completion\u001b[1;34m(self, model_response, timeout, optional_params, litellm_params, logging_obj, model, messages, print_verbose, api_key, api_base, api_version, dynamic_params, azure_ad_token, acompletion, logger_fn, headers, custom_prompt_dict, client, organization, custom_llm_provider, drop_params)\u001b[0m\n\u001b[0;32m    710\u001b[0m             \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m--> 711\u001b[0m                 \u001b[38;5;28;01mraise\u001b[39;00m e\n\u001b[0;32m    712\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m OpenAIError \u001b[38;5;28;01mas\u001b[39;00m e:\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\ai\\Lib\\site-packages\\litellm\\llms\\openai\\openai.py:638\u001b[0m, in \u001b[0;36mOpenAIChatCompletion.completion\u001b[1;34m(self, model_response, timeout, optional_params, litellm_params, logging_obj, model, messages, print_verbose, api_key, api_base, api_version, dynamic_params, azure_ad_token, acompletion, logger_fn, headers, custom_prompt_dict, client, organization, custom_llm_provider, drop_params)\u001b[0m\n\u001b[0;32m    626\u001b[0m logging_obj\u001b[38;5;241m.\u001b[39mpre_call(\n\u001b[0;32m    627\u001b[0m     \u001b[38;5;28minput\u001b[39m\u001b[38;5;241m=\u001b[39mmessages,\n\u001b[0;32m    628\u001b[0m     api_key\u001b[38;5;241m=\u001b[39mopenai_client\u001b[38;5;241m.\u001b[39mapi_key,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m    634\u001b[0m     },\n\u001b[0;32m    635\u001b[0m )\n\u001b[0;32m    637\u001b[0m headers, response \u001b[38;5;241m=\u001b[39m (\n\u001b[1;32m--> 638\u001b[0m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmake_sync_openai_chat_completion_request\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    639\u001b[0m \u001b[43m        \u001b[49m\u001b[43mopenai_client\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mopenai_client\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    640\u001b[0m \u001b[43m        \u001b[49m\u001b[43mdata\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mdata\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    641\u001b[0m \u001b[43m        \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtimeout\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    642\u001b[0m \u001b[43m        \u001b[49m\u001b[43mlogging_obj\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mlogging_obj\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    643\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    644\u001b[0m )\n\u001b[0;32m    646\u001b[0m logging_obj\u001b[38;5;241m.\u001b[39mmodel_call_details[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mresponse_headers\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m=\u001b[39m headers\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\ai\\Lib\\site-packages\\litellm\\litellm_core_utils\\logging_utils.py:145\u001b[0m, in \u001b[0;36mtrack_llm_api_timing.<locals>.decorator.<locals>.sync_wrapper\u001b[1;34m(*args, **kwargs)\u001b[0m\n\u001b[0;32m    144\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m--> 145\u001b[0m     result \u001b[38;5;241m=\u001b[39m \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    146\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m result\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\ai\\Lib\\site-packages\\litellm\\llms\\openai\\openai.py:457\u001b[0m, in \u001b[0;36mOpenAIChatCompletion.make_sync_openai_chat_completion_request\u001b[1;34m(self, openai_client, data, timeout, logging_obj)\u001b[0m\n\u001b[0;32m    456\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m--> 457\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m e\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\ai\\Lib\\site-packages\\litellm\\llms\\openai\\openai.py:439\u001b[0m, in \u001b[0;36mOpenAIChatCompletion.make_sync_openai_chat_completion_request\u001b[1;34m(self, openai_client, data, timeout, logging_obj)\u001b[0m\n\u001b[0;32m    438\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m--> 439\u001b[0m     raw_response \u001b[38;5;241m=\u001b[39m \u001b[43mopenai_client\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mchat\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcompletions\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mwith_raw_response\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcreate\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    440\u001b[0m \u001b[43m        \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mdata\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtimeout\u001b[49m\n\u001b[0;32m    441\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    443\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mhasattr\u001b[39m(raw_response, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mheaders\u001b[39m\u001b[38;5;124m\"\u001b[39m):\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\ai\\Lib\\site-packages\\openai\\_legacy_response.py:364\u001b[0m, in \u001b[0;36mto_raw_response_wrapper.<locals>.wrapped\u001b[1;34m(*args, **kwargs)\u001b[0m\n\u001b[0;32m    362\u001b[0m kwargs[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mextra_headers\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m=\u001b[39m extra_headers\n\u001b[1;32m--> 364\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m cast(LegacyAPIResponse[R], \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m)\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\ai\\Lib\\site-packages\\openai\\_utils\\_utils.py:279\u001b[0m, in \u001b[0;36mrequired_args.<locals>.inner.<locals>.wrapper\u001b[1;34m(*args, **kwargs)\u001b[0m\n\u001b[0;32m    278\u001b[0m     \u001b[38;5;28;01<PERSON><PERSON><PERSON>\u001b[39;00m \u001b[38;5;167;01mTypeError\u001b[39;00m(msg)\n\u001b[1;32m--> 279\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\ai\\Lib\\site-packages\\openai\\resources\\chat\\completions\\completions.py:914\u001b[0m, in \u001b[0;36mCompletions.create\u001b[1;34m(self, messages, model, audio, frequency_penalty, function_call, functions, logit_bias, logprobs, max_completion_tokens, max_tokens, metadata, modalities, n, parallel_tool_calls, prediction, presence_penalty, reasoning_effort, response_format, seed, service_tier, stop, store, stream, stream_options, temperature, tool_choice, tools, top_logprobs, top_p, user, web_search_options, extra_headers, extra_query, extra_body, timeout)\u001b[0m\n\u001b[0;32m    913\u001b[0m validate_response_format(response_format)\n\u001b[1;32m--> 914\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_post\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    915\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43m/chat/completions\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[0;32m    916\u001b[0m \u001b[43m    \u001b[49m\u001b[43mbody\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmaybe_transform\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    917\u001b[0m \u001b[43m        \u001b[49m\u001b[43m{\u001b[49m\n\u001b[0;32m    918\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mmessages\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mmessages\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    919\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mmodel\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mmodel\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    920\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43maudio\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43maudio\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    921\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mfrequency_penalty\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mfrequency_penalty\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    922\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mfunction_call\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mfunction_call\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    923\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mfunctions\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mfunctions\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    924\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mlogit_bias\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mlogit_bias\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    925\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mlogprobs\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mlogprobs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    926\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mmax_completion_tokens\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mmax_completion_tokens\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    927\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mmax_tokens\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mmax_tokens\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    928\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mmetadata\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mmetadata\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    929\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mmodalities\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mmodalities\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    930\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mn\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mn\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    931\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mparallel_tool_calls\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mparallel_tool_calls\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    932\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mprediction\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mprediction\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    933\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mpresence_penalty\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mpresence_penalty\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    934\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mreasoning_effort\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mreasoning_effort\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    935\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mresponse_format\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mresponse_format\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    936\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mseed\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mseed\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    937\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mservice_tier\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mservice_tier\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    938\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mstop\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mstop\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    939\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mstore\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mstore\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    940\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mstream\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mstream\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    941\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mstream_options\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mstream_options\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    942\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mtemperature\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mtemperature\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    943\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mtool_choice\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mtool_choice\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    944\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mtools\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mtools\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    945\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mtop_logprobs\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mtop_logprobs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    946\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mtop_p\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mtop_p\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    947\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43muser\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43muser\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    948\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mweb_search_options\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mweb_search_options\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    949\u001b[0m \u001b[43m        \u001b[49m\u001b[43m}\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    950\u001b[0m \u001b[43m        \u001b[49m\u001b[43mcompletion_create_params\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mCompletionCreateParams\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    951\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    952\u001b[0m \u001b[43m    \u001b[49m\u001b[43moptions\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmake_request_options\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    953\u001b[0m \u001b[43m        \u001b[49m\u001b[43mextra_headers\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mextra_headers\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mextra_query\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mextra_query\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mextra_body\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mextra_body\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtimeout\u001b[49m\n\u001b[0;32m    954\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    955\u001b[0m \u001b[43m    \u001b[49m\u001b[43mcast_to\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mChatCompletion\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    956\u001b[0m \u001b[43m    \u001b[49m\u001b[43mstream\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mstream\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01mor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[0;32m    957\u001b[0m \u001b[43m    \u001b[49m\u001b[43mstream_cls\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mStream\u001b[49m\u001b[43m[\u001b[49m\u001b[43mChatCompletionChunk\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    958\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\ai\\Lib\\site-packages\\openai\\_base_client.py:1242\u001b[0m, in \u001b[0;36mSyncAPIClient.post\u001b[1;34m(self, path, cast_to, body, options, files, stream, stream_cls)\u001b[0m\n\u001b[0;32m   1239\u001b[0m opts \u001b[38;5;241m=\u001b[39m FinalRequestOptions\u001b[38;5;241m.\u001b[39mconstruct(\n\u001b[0;32m   1240\u001b[0m     method\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mpost\u001b[39m\u001b[38;5;124m\"\u001b[39m, url\u001b[38;5;241m=\u001b[39mpath, json_data\u001b[38;5;241m=\u001b[39mbody, files\u001b[38;5;241m=\u001b[39mto_httpx_files(files), \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39moptions\n\u001b[0;32m   1241\u001b[0m )\n\u001b[1;32m-> 1242\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m cast(ResponseT, \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrequest\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcast_to\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mopts\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstream\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mstream\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstream_cls\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mstream_cls\u001b[49m\u001b[43m)\u001b[49m)\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\ai\\Lib\\site-packages\\openai\\_base_client.py:919\u001b[0m, in \u001b[0;36mSyncAPIClient.request\u001b[1;34m(self, cast_to, options, remaining_retries, stream, stream_cls)\u001b[0m\n\u001b[0;32m    917\u001b[0m     retries_taken \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m0\u001b[39m\n\u001b[1;32m--> 919\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_request\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    920\u001b[0m \u001b[43m    \u001b[49m\u001b[43mcast_to\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcast_to\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    921\u001b[0m \u001b[43m    \u001b[49m\u001b[43moptions\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43moptions\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    922\u001b[0m \u001b[43m    \u001b[49m\u001b[43mstream\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mstream\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    923\u001b[0m \u001b[43m    \u001b[49m\u001b[43mstream_cls\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mstream_cls\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    924\u001b[0m \u001b[43m    \u001b[49m\u001b[43mretries_taken\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mretries_taken\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    925\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\ai\\Lib\\site-packages\\openai\\_base_client.py:1023\u001b[0m, in \u001b[0;36mSyncAPIClient._request\u001b[1;34m(self, cast_to, options, retries_taken, stream, stream_cls)\u001b[0m\n\u001b[0;32m   1022\u001b[0m     log\u001b[38;5;241m.\u001b[39mdebug(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mRe-raising status error\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m-> 1023\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_make_status_error_from_response(err\u001b[38;5;241m.\u001b[39mresponse) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[0;32m   1025\u001b[0m \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_process_response(\n\u001b[0;32m   1026\u001b[0m     cast_to\u001b[38;5;241m=\u001b[39mcast_to,\n\u001b[0;32m   1027\u001b[0m     options\u001b[38;5;241m=\u001b[39moptions,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m   1031\u001b[0m     retries_taken\u001b[38;5;241m=\u001b[39mretries_taken,\n\u001b[0;32m   1032\u001b[0m )\n", "\u001b[1;31mNotFoundError\u001b[0m: Error code: 404 - {'error': {'code': '404', 'message': 'Resource not found'}}", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[1;31m<PERSON>penAIError\u001b[0m                               <PERSON><PERSON> (most recent call last)", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\ai\\Lib\\site-packages\\litellm\\main.py:1692\u001b[0m, in \u001b[0;36mcompletion\u001b[1;34m(model, messages, timeout, temperature, top_p, n, stream, stream_options, stop, max_completion_tokens, max_tokens, modalities, prediction, audio, presence_penalty, frequency_penalty, logit_bias, user, reasoning_effort, response_format, seed, tools, tool_choice, logprobs, top_logprobs, parallel_tool_calls, deployment_id, extra_headers, functions, function_call, base_url, api_version, api_key, model_list, **kwargs)\u001b[0m\n\u001b[0;32m   1686\u001b[0m     logging\u001b[38;5;241m.\u001b[39mpost_call(\n\u001b[0;32m   1687\u001b[0m         \u001b[38;5;28minput\u001b[39m\u001b[38;5;241m=\u001b[39mmessages,\n\u001b[0;32m   1688\u001b[0m         api_key\u001b[38;5;241m=\u001b[39mapi_key,\n\u001b[0;32m   1689\u001b[0m         original_response\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mstr\u001b[39m(e),\n\u001b[0;32m   1690\u001b[0m         additional_args\u001b[38;5;241m=\u001b[39m{\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mheaders\u001b[39m\u001b[38;5;124m\"\u001b[39m: headers},\n\u001b[0;32m   1691\u001b[0m     )\n\u001b[1;32m-> 1692\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m e\n\u001b[0;32m   1694\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m optional_params\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mstream\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;28;01mFalse\u001b[39;00m):\n\u001b[0;32m   1695\u001b[0m     \u001b[38;5;66;03m## LOGGING\u001b[39;00m\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\ai\\Lib\\site-packages\\litellm\\main.py:1665\u001b[0m, in \u001b[0;36mcompletion\u001b[1;34m(model, messages, timeout, temperature, top_p, n, stream, stream_options, stop, max_completion_tokens, max_tokens, modalities, prediction, audio, presence_penalty, frequency_penalty, logit_bias, user, reasoning_effort, response_format, seed, tools, tool_choice, logprobs, top_logprobs, parallel_tool_calls, deployment_id, extra_headers, functions, function_call, base_url, api_version, api_key, model_list, **kwargs)\u001b[0m\n\u001b[0;32m   1664\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m-> 1665\u001b[0m     response \u001b[38;5;241m=\u001b[39m \u001b[43mopenai_chat_completions\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcompletion\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m   1666\u001b[0m \u001b[43m        \u001b[49m\u001b[43mmodel\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmodel\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1667\u001b[0m \u001b[43m        \u001b[49m\u001b[43mmessages\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmessages\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1668\u001b[0m \u001b[43m        \u001b[49m\u001b[43mheaders\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mheaders\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1669\u001b[0m \u001b[43m        \u001b[49m\u001b[43mmodel_response\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmodel_response\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1670\u001b[0m \u001b[43m        \u001b[49m\u001b[43mprint_verbose\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mprint_verbose\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1671\u001b[0m \u001b[43m        \u001b[49m\u001b[43mapi_key\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mapi_key\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1672\u001b[0m \u001b[43m        \u001b[49m\u001b[43mapi_base\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mapi_base\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1673\u001b[0m \u001b[43m        \u001b[49m\u001b[43macompletion\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43macompletion\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1674\u001b[0m \u001b[43m        \u001b[49m\u001b[43mlogging_obj\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mlogging\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1675\u001b[0m \u001b[43m        \u001b[49m\u001b[43moptional_params\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43moptional_params\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1676\u001b[0m \u001b[43m        \u001b[49m\u001b[43mlitellm_params\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mlitellm_params\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1677\u001b[0m \u001b[43m        \u001b[49m\u001b[43mlogger_fn\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mlogger_fn\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1678\u001b[0m \u001b[43m        \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtimeout\u001b[49m\u001b[43m,\u001b[49m\u001b[43m  \u001b[49m\u001b[38;5;66;43;03m# type: ignore\u001b[39;49;00m\n\u001b[0;32m   1679\u001b[0m \u001b[43m        \u001b[49m\u001b[43mcustom_prompt_dict\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcustom_prompt_dict\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1680\u001b[0m \u001b[43m        \u001b[49m\u001b[43mclient\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mclient\u001b[49m\u001b[43m,\u001b[49m\u001b[43m  \u001b[49m\u001b[38;5;66;43;03m# pass AsyncOpenAI, OpenAI client\u001b[39;49;00m\n\u001b[0;32m   1681\u001b[0m \u001b[43m        \u001b[49m\u001b[43morganization\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43morganization\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1682\u001b[0m \u001b[43m        \u001b[49m\u001b[43mcustom_llm_provider\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcustom_llm_provider\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1683\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   1684\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[0;32m   1685\u001b[0m     \u001b[38;5;66;03m## LOGGING - log the original exception returned\u001b[39;00m\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\ai\\Lib\\site-packages\\litellm\\llms\\openai\\openai.py:721\u001b[0m, in \u001b[0;36mOpenAIChatCompletion.completion\u001b[1;34m(self, model_response, timeout, optional_params, litellm_params, logging_obj, model, messages, print_verbose, api_key, api_base, api_version, dynamic_params, azure_ad_token, acompletion, logger_fn, headers, custom_prompt_dict, client, organization, custom_llm_provider, drop_params)\u001b[0m\n\u001b[0;32m    720\u001b[0m     error_headers \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mgetattr\u001b[39m(error_response, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mheaders\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m)\n\u001b[1;32m--> 721\u001b[0m \u001b[38;5;28;01mraise\u001b[39;00m OpenAIError(\n\u001b[0;32m    722\u001b[0m     status_code\u001b[38;5;241m=\u001b[39mstatus_code, message\u001b[38;5;241m=\u001b[39merror_text, headers\u001b[38;5;241m=\u001b[39merror_headers\n\u001b[0;32m    723\u001b[0m )\n", "\u001b[1;31mOpenAIError\u001b[0m: Error code: 404 - {'error': {'code': '404', 'message': 'Resource not found'}}", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[1;31mNotFoundError\u001b[0m                             <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[14], line 3\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;66;03m# Step 5: Initialize a crew (single agent for now)\u001b[39;00m\n\u001b[0;32m      2\u001b[0m crew \u001b[38;5;241m=\u001b[39m Crew(agents\u001b[38;5;241m=\u001b[39m[agent1], tasks\u001b[38;5;241m=\u001b[39m[research_task])\n\u001b[1;32m----> 3\u001b[0m \u001b[43mcrew\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mkickoff\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\ai\\Lib\\site-packages\\crewai\\crew.py:640\u001b[0m, in \u001b[0;36mCrew.kickoff\u001b[1;34m(self, inputs)\u001b[0m\n\u001b[0;32m    637\u001b[0m metrics: List[UsageMetrics] \u001b[38;5;241m=\u001b[39m []\n\u001b[0;32m    639\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mprocess \u001b[38;5;241m==\u001b[39m Process\u001b[38;5;241m.\u001b[39msequential:\n\u001b[1;32m--> 640\u001b[0m     result \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_run_sequential_process\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    641\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mprocess \u001b[38;5;241m==\u001b[39m Process\u001b[38;5;241m.\u001b[39mhierarchical:\n\u001b[0;32m    642\u001b[0m     result \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_run_hierarchical_process()\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\ai\\Lib\\site-packages\\crewai\\crew.py:752\u001b[0m, in \u001b[0;36mCrew._run_sequential_process\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m    750\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21m_run_sequential_process\u001b[39m(\u001b[38;5;28mself\u001b[39m) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m CrewOutput:\n\u001b[0;32m    751\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"Executes tasks sequentially and returns the final output.\"\"\"\u001b[39;00m\n\u001b[1;32m--> 752\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_execute_tasks\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mtasks\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\ai\\Lib\\site-packages\\crewai\\crew.py:850\u001b[0m, in \u001b[0;36mCrew._execute_tasks\u001b[1;34m(self, tasks, start_index, was_replayed)\u001b[0m\n\u001b[0;32m    847\u001b[0m     futures\u001b[38;5;241m.\u001b[39mclear()\n\u001b[0;32m    849\u001b[0m context \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_get_context(task, task_outputs)\n\u001b[1;32m--> 850\u001b[0m task_output \u001b[38;5;241m=\u001b[39m \u001b[43mtask\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mexecute_sync\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    851\u001b[0m \u001b[43m    \u001b[49m\u001b[43magent\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43magent_to_use\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    852\u001b[0m \u001b[43m    \u001b[49m\u001b[43mcontext\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcontext\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    853\u001b[0m \u001b[43m    \u001b[49m\u001b[43mtools\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtools_for_task\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    854\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    855\u001b[0m task_outputs\u001b[38;5;241m.\u001b[39mappend(task_output)\n\u001b[0;32m    856\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_process_task_result(task, task_output)\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\ai\\Lib\\site-packages\\crewai\\task.py:310\u001b[0m, in \u001b[0;36mTask.execute_sync\u001b[1;34m(self, agent, context, tools)\u001b[0m\n\u001b[0;32m    303\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21mexecute_sync\u001b[39m(\n\u001b[0;32m    304\u001b[0m     \u001b[38;5;28mself\u001b[39m,\n\u001b[0;32m    305\u001b[0m     agent: Optional[BaseAgent] \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[0;32m    306\u001b[0m     context: Optional[\u001b[38;5;28mstr\u001b[39m] \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[0;32m    307\u001b[0m     tools: Optional[List[BaseTool]] \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[0;32m    308\u001b[0m ) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m TaskOutput:\n\u001b[0;32m    309\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"Execute the task synchronously.\"\"\"\u001b[39;00m\n\u001b[1;32m--> 310\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_execute_core\u001b[49m\u001b[43m(\u001b[49m\u001b[43magent\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcontext\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtools\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\ai\\Lib\\site-packages\\crewai\\task.py:454\u001b[0m, in \u001b[0;36mTask._execute_core\u001b[1;34m(self, agent, context, tools)\u001b[0m\n\u001b[0;32m    452\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mend_time \u001b[38;5;241m=\u001b[39m datetime\u001b[38;5;241m.\u001b[39mdatetime\u001b[38;5;241m.\u001b[39mnow()\n\u001b[0;32m    453\u001b[0m crewai_event_bus\u001b[38;5;241m.\u001b[39memit(\u001b[38;5;28mself\u001b[39m, TaskFailedEvent(error\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mstr\u001b[39m(e)))\n\u001b[1;32m--> 454\u001b[0m \u001b[38;5;28;01mraise\u001b[39;00m e\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\ai\\Lib\\site-packages\\crewai\\task.py:374\u001b[0m, in \u001b[0;36mTask._execute_core\u001b[1;34m(self, agent, context, tools)\u001b[0m\n\u001b[0;32m    372\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mprocessed_by_agents\u001b[38;5;241m.\u001b[39madd(agent\u001b[38;5;241m.\u001b[39mrole)\n\u001b[0;32m    373\u001b[0m crewai_event_bus\u001b[38;5;241m.\u001b[39memit(\u001b[38;5;28mself\u001b[39m, TaskStartedEvent(context\u001b[38;5;241m=\u001b[39mcontext))\n\u001b[1;32m--> 374\u001b[0m result \u001b[38;5;241m=\u001b[39m \u001b[43magent\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mexecute_task\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    375\u001b[0m \u001b[43m    \u001b[49m\u001b[43mtask\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[0;32m    376\u001b[0m \u001b[43m    \u001b[49m\u001b[43mcontext\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcontext\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    377\u001b[0m \u001b[43m    \u001b[49m\u001b[43mtools\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtools\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    378\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    380\u001b[0m pydantic_output, json_output \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_export_output(result)\n\u001b[0;32m    381\u001b[0m task_output \u001b[38;5;241m=\u001b[39m TaskOutput(\n\u001b[0;32m    382\u001b[0m     name\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mname,\n\u001b[0;32m    383\u001b[0m     description\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mdescription,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m    389\u001b[0m     output_format\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_get_output_format(),\n\u001b[0;32m    390\u001b[0m )\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\ai\\Lib\\site-packages\\crewai\\agent.py:266\u001b[0m, in \u001b[0;36mAgent.execute_task\u001b[1;34m(self, task, context, tools)\u001b[0m\n\u001b[0;32m    256\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m e\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__class__\u001b[39m\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__module__\u001b[39m\u001b[38;5;241m.\u001b[39mstartswith(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mlitellm\u001b[39m\u001b[38;5;124m\"\u001b[39m):\n\u001b[0;32m    257\u001b[0m     \u001b[38;5;66;03m# Do not retry on litellm errors\u001b[39;00m\n\u001b[0;32m    258\u001b[0m     crewai_event_bus\u001b[38;5;241m.\u001b[39memit(\n\u001b[0;32m    259\u001b[0m         \u001b[38;5;28mself\u001b[39m,\n\u001b[0;32m    260\u001b[0m         event\u001b[38;5;241m=\u001b[39mAgentExecutionErrorEvent(\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m    264\u001b[0m         ),\n\u001b[0;32m    265\u001b[0m     )\n\u001b[1;32m--> 266\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m e\n\u001b[0;32m    267\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_times_executed \u001b[38;5;241m+\u001b[39m\u001b[38;5;241m=\u001b[39m \u001b[38;5;241m1\u001b[39m\n\u001b[0;32m    268\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_times_executed \u001b[38;5;241m>\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mmax_retry_limit:\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\ai\\Lib\\site-packages\\crewai\\agent.py:247\u001b[0m, in \u001b[0;36mAgent.execute_task\u001b[1;34m(self, task, context, tools)\u001b[0m\n\u001b[0;32m    237\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m    238\u001b[0m     crewai_event_bus\u001b[38;5;241m.\u001b[39memit(\n\u001b[0;32m    239\u001b[0m         \u001b[38;5;28mself\u001b[39m,\n\u001b[0;32m    240\u001b[0m         event\u001b[38;5;241m=\u001b[39mAgentExecutionStartedEvent(\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m    245\u001b[0m         ),\n\u001b[0;32m    246\u001b[0m     )\n\u001b[1;32m--> 247\u001b[0m     result \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43magent_executor\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43minvoke\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    248\u001b[0m \u001b[43m        \u001b[49m\u001b[43m{\u001b[49m\n\u001b[0;32m    249\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43minput\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mtask_prompt\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    250\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mtool_names\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43magent_executor\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mtools_names\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    251\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mtools\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43magent_executor\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mtools_description\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    252\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mask_for_human_input\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mtask\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mhuman_input\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    253\u001b[0m \u001b[43m        \u001b[49m\u001b[43m}\u001b[49m\n\u001b[0;32m    254\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124moutput\u001b[39m\u001b[38;5;124m\"\u001b[39m]\n\u001b[0;32m    255\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[0;32m    256\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m e\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__class__\u001b[39m\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__module__\u001b[39m\u001b[38;5;241m.\u001b[39mstartswith(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mlitellm\u001b[39m\u001b[38;5;124m\"\u001b[39m):\n\u001b[0;32m    257\u001b[0m         \u001b[38;5;66;03m# Do not retry on litellm errors\u001b[39;00m\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\ai\\Lib\\site-packages\\crewai\\agents\\crew_agent_executor.py:119\u001b[0m, in \u001b[0;36mCrewAgentExecutor.invoke\u001b[1;34m(self, inputs)\u001b[0m\n\u001b[0;32m    116\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_handle_unknown_error(e)\n\u001b[0;32m    117\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m e\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__class__\u001b[39m\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__module__\u001b[39m\u001b[38;5;241m.\u001b[39mstartswith(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mlitellm\u001b[39m\u001b[38;5;124m\"\u001b[39m):\n\u001b[0;32m    118\u001b[0m     \u001b[38;5;66;03m# Do not retry on litellm errors\u001b[39;00m\n\u001b[1;32m--> 119\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m e\n\u001b[0;32m    120\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m    121\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m e\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\ai\\Lib\\site-packages\\crewai\\agents\\crew_agent_executor.py:108\u001b[0m, in \u001b[0;36mCrewAgentExecutor.invoke\u001b[1;34m(self, inputs)\u001b[0m\n\u001b[0;32m    105\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mask_for_human_input \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mbool\u001b[39m(inputs\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mask_for_human_input\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;28;01mFalse\u001b[39;00m))\n\u001b[0;32m    107\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m--> 108\u001b[0m     formatted_answer \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_invoke_loop\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    109\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mAssertionError\u001b[39;00m:\n\u001b[0;32m    110\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_printer\u001b[38;5;241m.\u001b[39mprint(\n\u001b[0;32m    111\u001b[0m         content\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mAgent failed to reach a final answer. This is likely a bug - please report it.\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[0;32m    112\u001b[0m         color\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mred\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[0;32m    113\u001b[0m     )\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\ai\\Lib\\site-packages\\crewai\\agents\\crew_agent_executor.py:166\u001b[0m, in \u001b[0;36mCrewAgentExecutor._invoke_loop\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m    163\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[0;32m    164\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m e\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__class__\u001b[39m\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__module__\u001b[39m\u001b[38;5;241m.\u001b[39mstartswith(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mlitellm\u001b[39m\u001b[38;5;124m\"\u001b[39m):\n\u001b[0;32m    165\u001b[0m         \u001b[38;5;66;03m# Do not retry on litellm errors\u001b[39;00m\n\u001b[1;32m--> 166\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m e\n\u001b[0;32m    167\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_is_context_length_exceeded(e):\n\u001b[0;32m    168\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_handle_context_length()\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\ai\\Lib\\site-packages\\crewai\\agents\\crew_agent_executor.py:146\u001b[0m, in \u001b[0;36mCrewAgentExecutor._invoke_loop\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m    142\u001b[0m     \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m\n\u001b[0;32m    144\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_enforce_rpm_limit()\n\u001b[1;32m--> 146\u001b[0m answer \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_get_llm_response\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    147\u001b[0m formatted_answer \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_process_llm_response(answer)\n\u001b[0;32m    149\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(formatted_answer, AgentAction):\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\ai\\Lib\\site-packages\\crewai\\agents\\crew_agent_executor.py:216\u001b[0m, in \u001b[0;36mCrewAgentExecutor._get_llm_response\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m    211\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[0;32m    212\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_printer\u001b[38;5;241m.\u001b[39mprint(\n\u001b[0;32m    213\u001b[0m         content\u001b[38;5;241m=\u001b[39m\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mError during LLM call: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00me\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m,\n\u001b[0;32m    214\u001b[0m         color\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mred\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[0;32m    215\u001b[0m     )\n\u001b[1;32m--> 216\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m e\n\u001b[0;32m    218\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m answer:\n\u001b[0;32m    219\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_printer\u001b[38;5;241m.\u001b[39mprint(\n\u001b[0;32m    220\u001b[0m         content\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mReceived None or empty response from LLM call.\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[0;32m    221\u001b[0m         color\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mred\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[0;32m    222\u001b[0m     )\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\ai\\Lib\\site-packages\\crewai\\agents\\crew_agent_executor.py:207\u001b[0m, in \u001b[0;36mCrewAgentExecutor._get_llm_response\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m    205\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124;03m\"\"\"Call the LLM and return the response, handling any invalid responses.\"\"\"\u001b[39;00m\n\u001b[0;32m    206\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m--> 207\u001b[0m     answer \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mllm\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcall\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    208\u001b[0m \u001b[43m        \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmessages\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    209\u001b[0m \u001b[43m        \u001b[49m\u001b[43mcallbacks\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcallbacks\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    210\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    211\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[0;32m    212\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_printer\u001b[38;5;241m.\u001b[39mprint(\n\u001b[0;32m    213\u001b[0m         content\u001b[38;5;241m=\u001b[39m\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mError during LLM call: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00me\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m,\n\u001b[0;32m    214\u001b[0m         color\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mred\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[0;32m    215\u001b[0m     )\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\ai\\Lib\\site-packages\\crewai\\llm.py:739\u001b[0m, in \u001b[0;36mLLM.call\u001b[1;34m(self, messages, tools, callbacks, available_functions)\u001b[0m\n\u001b[0;32m    735\u001b[0m         \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_handle_streaming_response(\n\u001b[0;32m    736\u001b[0m             params, callbacks, available_functions\n\u001b[0;32m    737\u001b[0m         )\n\u001b[0;32m    738\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m--> 739\u001b[0m         \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_handle_non_streaming_response\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    740\u001b[0m \u001b[43m            \u001b[49m\u001b[43mparams\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcallbacks\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mavailable_functions\u001b[49m\n\u001b[0;32m    741\u001b[0m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    743\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[0;32m    744\u001b[0m     crewai_event_bus\u001b[38;5;241m.\u001b[39memit(\n\u001b[0;32m    745\u001b[0m         \u001b[38;5;28mself\u001b[39m,\n\u001b[0;32m    746\u001b[0m         event\u001b[38;5;241m=\u001b[39mLLMCallFailedEvent(error\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mstr\u001b[39m(e)),\n\u001b[0;32m    747\u001b[0m     )\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\ai\\Lib\\site-packages\\crewai\\llm.py:575\u001b[0m, in \u001b[0;36mLLM._handle_non_streaming_response\u001b[1;34m(self, params, callbacks, available_functions)\u001b[0m\n\u001b[0;32m    564\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124;03m\"\"\"Handle a non-streaming response from the LLM.\u001b[39;00m\n\u001b[0;32m    565\u001b[0m \n\u001b[0;32m    566\u001b[0m \u001b[38;5;124;03mArgs:\u001b[39;00m\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m    572\u001b[0m \u001b[38;5;124;03m    str: The response text\u001b[39;00m\n\u001b[0;32m    573\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[0;32m    574\u001b[0m \u001b[38;5;66;03m# --- 1) Make the completion call\u001b[39;00m\n\u001b[1;32m--> 575\u001b[0m response \u001b[38;5;241m=\u001b[39m \u001b[43mlitellm\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcompletion\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mparams\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    577\u001b[0m \u001b[38;5;66;03m# --- 2) Extract response message and content\u001b[39;00m\n\u001b[0;32m    578\u001b[0m response_message \u001b[38;5;241m=\u001b[39m cast(Choices, cast(ModelResponse, response)\u001b[38;5;241m.\u001b[39mchoices)[\n\u001b[0;32m    579\u001b[0m     \u001b[38;5;241m0\u001b[39m\n\u001b[0;32m    580\u001b[0m ]\u001b[38;5;241m.\u001b[39mmessage\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\ai\\Lib\\site-packages\\litellm\\utils.py:1154\u001b[0m, in \u001b[0;36mclient.<locals>.wrapper\u001b[1;34m(*args, **kwargs)\u001b[0m\n\u001b[0;32m   1150\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m logging_obj:\n\u001b[0;32m   1151\u001b[0m     logging_obj\u001b[38;5;241m.\u001b[39mfailure_handler(\n\u001b[0;32m   1152\u001b[0m         e, traceback_exception, start_time, end_time\n\u001b[0;32m   1153\u001b[0m     )  \u001b[38;5;66;03m# DO NOT MAKE THREADED - router retry fallback relies on this!\u001b[39;00m\n\u001b[1;32m-> 1154\u001b[0m \u001b[38;5;28;01mraise\u001b[39;00m e\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\ai\\Lib\\site-packages\\litellm\\utils.py:1032\u001b[0m, in \u001b[0;36mclient.<locals>.wrapper\u001b[1;34m(*args, **kwargs)\u001b[0m\n\u001b[0;32m   1030\u001b[0m         print_verbose(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mError while checking max token limit: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mstr\u001b[39m(e)\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m   1031\u001b[0m \u001b[38;5;66;03m# MODEL CALL\u001b[39;00m\n\u001b[1;32m-> 1032\u001b[0m result \u001b[38;5;241m=\u001b[39m \u001b[43moriginal_function\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   1033\u001b[0m end_time \u001b[38;5;241m=\u001b[39m datetime\u001b[38;5;241m.\u001b[39mdatetime\u001b[38;5;241m.\u001b[39mnow()\n\u001b[0;32m   1034\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mstream\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;129;01min\u001b[39;00m kwargs \u001b[38;5;129;01mand\u001b[39;00m kwargs[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mstream\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mTrue\u001b[39;00m:\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\ai\\Lib\\site-packages\\litellm\\main.py:3068\u001b[0m, in \u001b[0;36mcompletion\u001b[1;34m(model, messages, timeout, temperature, top_p, n, stream, stream_options, stop, max_completion_tokens, max_tokens, modalities, prediction, audio, presence_penalty, frequency_penalty, logit_bias, user, reasoning_effort, response_format, seed, tools, tool_choice, logprobs, top_logprobs, parallel_tool_calls, deployment_id, extra_headers, functions, function_call, base_url, api_version, api_key, model_list, **kwargs)\u001b[0m\n\u001b[0;32m   3065\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m response\n\u001b[0;32m   3066\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[0;32m   3067\u001b[0m     \u001b[38;5;66;03m## Map to OpenAI Exception\u001b[39;00m\n\u001b[1;32m-> 3068\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[43mexception_type\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m   3069\u001b[0m \u001b[43m        \u001b[49m\u001b[43mmodel\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmodel\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   3070\u001b[0m \u001b[43m        \u001b[49m\u001b[43mcustom_llm_provider\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcustom_llm_provider\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   3071\u001b[0m \u001b[43m        \u001b[49m\u001b[43moriginal_exception\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43me\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   3072\u001b[0m \u001b[43m        \u001b[49m\u001b[43mcompletion_kwargs\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   3073\u001b[0m \u001b[43m        \u001b[49m\u001b[43mextra_kwargs\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   3074\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\ai\\Lib\\site-packages\\litellm\\litellm_core_utils\\exception_mapping_utils.py:2201\u001b[0m, in \u001b[0;36mexception_type\u001b[1;34m(model, original_exception, custom_llm_provider, completion_kwargs, extra_kwargs)\u001b[0m\n\u001b[0;32m   2199\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m exception_mapping_worked:\n\u001b[0;32m   2200\u001b[0m     \u001b[38;5;28msetattr\u001b[39m(e, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mlitellm_response_headers\u001b[39m\u001b[38;5;124m\"\u001b[39m, litellm_response_headers)\n\u001b[1;32m-> 2201\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m e\n\u001b[0;32m   2202\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m   2203\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m error_type \u001b[38;5;129;01min\u001b[39;00m litellm\u001b[38;5;241m.\u001b[39mLITELLM_EXCEPTION_TYPES:\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\ai\\Lib\\site-packages\\litellm\\litellm_core_utils\\exception_mapping_utils.py:399\u001b[0m, in \u001b[0;36mexception_type\u001b[1;34m(model, original_exception, custom_llm_provider, completion_kwargs, extra_kwargs)\u001b[0m\n\u001b[0;32m    397\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m original_exception\u001b[38;5;241m.\u001b[39mstatus_code \u001b[38;5;241m==\u001b[39m \u001b[38;5;241m404\u001b[39m:\n\u001b[0;32m    398\u001b[0m     exception_mapping_worked \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mTrue\u001b[39;00m\n\u001b[1;32m--> 399\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m NotFoundError(\n\u001b[0;32m    400\u001b[0m         message\u001b[38;5;241m=\u001b[39m\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mNotFoundError: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mexception_provider\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m - \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mmessage\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m,\n\u001b[0;32m    401\u001b[0m         model\u001b[38;5;241m=\u001b[39mmodel,\n\u001b[0;32m    402\u001b[0m         llm_provider\u001b[38;5;241m=\u001b[39mcustom_llm_provider,\n\u001b[0;32m    403\u001b[0m         response\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mgetattr\u001b[39m(original_exception, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mresponse\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m),\n\u001b[0;32m    404\u001b[0m         litellm_debug_info\u001b[38;5;241m=\u001b[39mextra_information,\n\u001b[0;32m    405\u001b[0m     )\n\u001b[0;32m    406\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m original_exception\u001b[38;5;241m.\u001b[39mstatus_code \u001b[38;5;241m==\u001b[39m \u001b[38;5;241m408\u001b[39m:\n\u001b[0;32m    407\u001b[0m     exception_mapping_worked \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mTrue\u001b[39;00m\n", "\u001b[1;31mNotFoundError\u001b[0m: litellm.NotFoundError: NotFoundError: OpenAIException - Error code: 404 - {'error': {'code': '404', 'message': 'Resource not found'}}"]}], "source": ["# Step 5: Initialize a crew (single agent for now)\n", "crew = Crew(agents=[agent1], tasks=[research_task])\n", "crew.kickoff()"]}, {"cell_type": "markdown", "metadata": {"id": "NaaI8UaGFl57"}, "source": ["### Phase 3: Multi-Agent Collaboration & Workflows"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "9lBxZ5wIALA-"}, "outputs": [], "source": ["# Step 6: Define multiple agents\n", "agent2 = Agent(\n", "    name=\"Writer\",\n", "    description=\"An AI agent that writes research reports.\",\n", "    goal=\"Create structured reports from gathered research data.\",\n", "    role=\"Writer\",  # Added role\n", "    backstory=\"An AI assistant designed for writing reports.\"  # Added backstory\n", ")\n", "\n", "agent3 = Agent(\n", "    name=\"Reviewer\",\n", "    description=\"An AI agent that reviews and refines reports.\",\n", "    goal=\"Ensure clarity, grammar, and accuracy in written content.\",\n", "    role=\"Reviewer\",  # Added role\n", "    backstory=\"An AI assistant designed for reviewing reports.\"  # Added backstory\n", ")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "AwrvWipdAK9u"}, "outputs": [], "source": ["# Step 7: Assign tasks to each agent\n", "gather_info = Task(\n", "    name=\"Gather Information\",\n", "    description=\"Find the latest research papers and summarize key findings.\",\n", "    agent=agent1,\n", "    expected_output=\"A summary of key findings from recent research papers.\"  # Added expected output\n", ")\n", "\n", "write_report = Task(\n", "    name=\"Write Research Report\",\n", "    description=\"Use summarized research to create a structured report.\",\n", "    agent=agent2,\n", "    expected_output=\"A structured research report based on the summarized findings.\"  # Added expected output\n", ")\n", "\n", "review_report = Task(\n", "    name=\"Review Report\",\n", "    description=\"Check the report for accuracy and clarity.\",\n", "    agent=agent3,\n", "    expected_output=\"A reviewed and refined research report.\"  # Added expected output\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "TF10S9-JA9zX", "outputId": "7dddb948-ba13-4fa6-de73-d7c3c1f0c285"}, "outputs": [{"data": {"text/plain": ["CrewOutput(raw=\"**Structured Research Report based on Summarized Findings (2023)**\\n\\n**1. Introduction**  \\nThis report summarizes key findings from recent research published in 2023, highlighting advancements in technology, climate change impacts, healthcare innovations, sustainable energy solutions, and mental health concerns related to social media usage. Each study presents significant implications for their respective domains, offering insights into challenges and future directions.\\n\\n**2. Advancements in Quantum Computing Algorithms**  \\n- **Authors: <AUTHORS>"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["# Step 8: Create a Crew with multiple agents\n", "multi_agent_crew = Crew(\n", "    agents=[agent1, agent2, agent3],\n", "    tasks=[gather_info, write_report, review_report]\n", ")\n", "\n", "multi_agent_crew.kickoff()"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "ai", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.0"}}, "nbformat": 4, "nbformat_minor": 0}