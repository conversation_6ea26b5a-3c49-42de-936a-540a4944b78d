#!/usr/bin/env python3
"""
Launch script for Advanced AI Voice-Over Streamlit Application
"""

import subprocess
import sys
import os
from pathlib import Path

def check_dependencies():
    """Check if required dependencies are installed"""
    required_packages = [
        'streamlit',
        'azure-cognitiveservices-speech',
        'moviepy',
        'librosa',
        'plotly'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ Missing required packages:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n📦 Install missing packages with:")
        print("   pip install -r requirements.txt")
        return False
    
    return True

def check_azure_config():
    """Check if Azure configuration is available"""
    try:
        parent_dir = Path(__file__).parent.parent
        azure_config_path = parent_dir / "AI_Voice_Over_Azure" / "config.py"
        
        if not azure_config_path.exists():
            print("⚠️  Azure configuration not found!")
            print(f"   Expected: {azure_config_path}")
            print("   Please ensure the AI_Voice_Over_Azure system is properly configured.")
            return False
        
        # Try to import Azure config
        sys.path.append(str(parent_dir / "AI_Voice_Over_Azure"))
        from config import AZURE_SPEECH_KEY, AZURE_SPEECH_REGION
        
        if AZURE_SPEECH_KEY == "your_azure_speech_key_here":
            print("⚠️  Azure Speech key not configured!")
            print("   Please update the Azure configuration with your credentials.")
            return False
        
        print("✅ Azure configuration found and valid")
        return True
        
    except Exception as e:
        print(f"❌ Error checking Azure configuration: {e}")
        return False

def main():
    """Main function to launch the Streamlit app"""
    print("🎬 Advanced AI Voice-Over System")
    print("=" * 50)
    
    # Check dependencies
    print("📦 Checking dependencies...")
    if not check_dependencies():
        sys.exit(1)
    print("✅ All dependencies found")
    
    # Check Azure configuration
    print("🔧 Checking Azure configuration...")
    if not check_azure_config():
        print("⚠️  Continuing without Azure validation (may cause runtime errors)")
    
    # Create necessary directories
    print("📁 Creating directories...")
    from config import create_directories
    create_directories()
    print("✅ Directories ready")
    
    # Launch Streamlit app
    print("🚀 Launching Streamlit application...")
    print("   URL: http://localhost:8501")
    print("   Press Ctrl+C to stop the application")
    print("=" * 50)
    
    try:
        # Change to the app directory
        app_dir = Path(__file__).parent
        os.chdir(app_dir)
        
        # Launch Streamlit
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", "app.py",
            "--server.port", "8501",
            "--server.address", "localhost",
            "--browser.gatherUsageStats", "false"
        ])
        
    except KeyboardInterrupt:
        print("\n👋 Application stopped by user")
    except Exception as e:
        print(f"\n❌ Error launching application: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
