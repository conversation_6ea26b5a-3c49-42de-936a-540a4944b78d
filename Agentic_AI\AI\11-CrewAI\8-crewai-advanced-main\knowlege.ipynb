{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Knowledge Sources in CrewAI"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## String knowledge source"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from crewai.knowledge.source.string_knowledge_source import StringKnowledgeSource\n", "\n", "# Define the knowledge\n", "policy_text = \"\"\"Our return policy allows customers to return any product within 30 days of purchase.\n", "                 Refunds will be issued only if the item is unused and in original packaging.\n", "                 Customers must provide proof of purchase when requesting a return.\"\"\"\n", "\n", "# Create a StringKnowledgeSource object\n", "return_policy_knowledge = StringKnowledgeSource(content=policy_text)\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from crewai import LLM\n", "\n", "llm = LLM(model=\"gpt-4o\")\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from crewai import Agent\n", "\n", "returns_agent = Agent(\n", "    role=\"Product Returns Assistant\",\n", "    goal=\"Answer customer questions about return policy accurately.\",\n", "    backstory=\"You work in customer service and specialize in returns, refunds, and policies.\",\n", "    allow_delegation=False,\n", "    verbose=True,\n", "    llm=llm\n", ")\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["from crewai import Task\n", "\n", "returns_task = Task(\n", "    description=\"Answer the following customer question about returns: {question}\",\n", "    expected_output=\"A concise and accurate answer.\",\n", "    agent=returns_agent\n", ")\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[93m \n", "[2025-04-19 18:12:37][WARNING]: Failed to init knowledge: Please provide an OpenAI API key. You can get one at https://platform.openai.com/account/api-keys\u001b[00m\n"]}], "source": ["from crewai import Crew, Process\n", "\n", "crew = Crew(\n", "    agents=[returns_agent],\n", "    tasks=[returns_task],\n", "    process=Process.sequential,\n", "    knowledge_sources=[return_policy_knowledge],  # This is key\n", "    verbose=True\n", ")\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080\">╭──────────────────────────────────────────── Crew Execution Started ─────────────────────────────────────────────╮</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080\">│</span>                                                                                                                 <span style=\"color: #008080; text-decoration-color: #008080\">│</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080\">│</span>  <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">Crew Execution Started</span>                                                                                         <span style=\"color: #008080; text-decoration-color: #008080\">│</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080\">│</span>  <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">Name: </span><span style=\"color: #008080; text-decoration-color: #008080\">crew</span>                                                                                                     <span style=\"color: #008080; text-decoration-color: #008080\">│</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080\">│</span>  <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">ID: </span><span style=\"color: #008080; text-decoration-color: #008080\">77e5f4b3-0238-44f4-be1c-df22779a02e1</span>                                                                       <span style=\"color: #008080; text-decoration-color: #008080\">│</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080\">│</span>                                                                                                                 <span style=\"color: #008080; text-decoration-color: #008080\">│</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080\">│</span>                                                                                                                 <span style=\"color: #008080; text-decoration-color: #008080\">│</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[36m╭─\u001b[0m\u001b[36m───────────────────────────────────────────\u001b[0m\u001b[36m Crew Execution Started \u001b[0m\u001b[36m────────────────────────────────────────────\u001b[0m\u001b[36m─╮\u001b[0m\n", "\u001b[36m│\u001b[0m                                                                                                                 \u001b[36m│\u001b[0m\n", "\u001b[36m│\u001b[0m  \u001b[1;36mCrew Execution Started\u001b[0m                                                                                         \u001b[36m│\u001b[0m\n", "\u001b[36m│\u001b[0m  \u001b[37mName: \u001b[0m\u001b[36mcrew\u001b[0m                                                                                                     \u001b[36m│\u001b[0m\n", "\u001b[36m│\u001b[0m  \u001b[37mID: \u001b[0m\u001b[36m77e5f4b3-0238-44f4-be1c-df22779a02e1\u001b[0m                                                                       \u001b[36m│\u001b[0m\n", "\u001b[36m│\u001b[0m                                                                                                                 \u001b[36m│\u001b[0m\n", "\u001b[36m│\u001b[0m                                                                                                                 \u001b[36m│\u001b[0m\n", "\u001b[36m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">\n", "</pre>\n"], "text/plain": ["\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">🚀 Crew: crew</span>\n", "└── <span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">📋 Task: 8bbbc838-007a-454a-92e9-4f44d1042c6d</span>\n", "    <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">   Status: </span><span style=\"color: #bfbf7f; text-decoration-color: #bfbf7f\">Executing Task...</span>\n", "</pre>\n"], "text/plain": ["\u001b[1;36m🚀 Crew: \u001b[0m\u001b[1;36mcrew\u001b[0m\n", "└── \u001b[1;33m📋 Task: 8bbbc838-007a-454a-92e9-4f44d1042c6d\u001b[0m\n", "    \u001b[37m   Status: \u001b[0m\u001b[2;33mExecuting Task...\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">\n", "</pre>\n"], "text/plain": ["\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">🚀 Crew: crew</span>\n", "└── <span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">📋 Task: 8bbbc838-007a-454a-92e9-4f44d1042c6d</span>\n", "    <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">   Status: </span><span style=\"color: #bfbf7f; text-decoration-color: #bfbf7f\">Executing Task...</span>\n", "    └── <span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">🤖 Agent: </span><span style=\"color: #008000; text-decoration-color: #008000\">Product Returns Assistant</span>\n", "        <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">    Status: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">In Progress</span>\n", "</pre>\n"], "text/plain": ["\u001b[1;36m🚀 Crew: \u001b[0m\u001b[1;36mcrew\u001b[0m\n", "└── \u001b[1;33m📋 Task: 8bbbc838-007a-454a-92e9-4f44d1042c6d\u001b[0m\n", "    \u001b[37m   Status: \u001b[0m\u001b[2;33mExecuting Task...\u001b[0m\n", "    └── \u001b[1;32m🤖 Agent: \u001b[0m\u001b[32mProduct Returns Assistant\u001b[0m\n", "        \u001b[37m    Status: \u001b[0m\u001b[1;32mIn Progress\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">\n", "</pre>\n"], "text/plain": ["\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m\u001b[95m# Agent:\u001b[00m \u001b[1m\u001b[92mProduct Returns Assistant\u001b[00m\n", "\u001b[95m## Task:\u001b[00m \u001b[92mAnswer the following customer question about returns: Can I get a refund if I used the item once?\u001b[00m\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">🤖 Agent: </span><span style=\"color: #008000; text-decoration-color: #008000\">Product Returns Assistant</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">    Status: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">In Progress</span>\n", "└── <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">🧠 </span><span style=\"color: #000080; text-decoration-color: #000080\">Thinking...</span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m🤖 Agent: \u001b[0m\u001b[32mProduct Returns Assistant\u001b[0m\n", "\u001b[37m    Status: \u001b[0m\u001b[1;32mIn Progress\u001b[0m\n", "└── \u001b[1;34m🧠 \u001b[0m\u001b[34mThinking...\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">\n", "</pre>\n"], "text/plain": ["\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "LiteLLM.Info: If you need to debug this error, use `litellm._turn_on_debug()'.\n", "\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">🚀 Crew: crew</span>\n", "└── <span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">📋 Task: 8bbbc838-007a-454a-92e9-4f44d1042c6d</span>\n", "    <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">   Status: </span><span style=\"color: #bfbf7f; text-decoration-color: #bfbf7f\">Executing Task...</span>\n", "    └── <span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">🤖 Agent: </span><span style=\"color: #008000; text-decoration-color: #008000\">Product Returns Assistant</span>\n", "        <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">    Status: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">In Progress</span>\n", "        └── <span style=\"color: #800000; text-decoration-color: #800000; font-weight: bold\">❌ LLM Failed</span>\n", "</pre>\n"], "text/plain": ["\u001b[1;36m🚀 Crew: \u001b[0m\u001b[1;36mcrew\u001b[0m\n", "└── \u001b[1;33m📋 Task: 8bbbc838-007a-454a-92e9-4f44d1042c6d\u001b[0m\n", "    \u001b[37m   Status: \u001b[0m\u001b[2;33mExecuting Task...\u001b[0m\n", "    └── \u001b[1;32m🤖 Agent: \u001b[0m\u001b[32mProduct Returns Assistant\u001b[0m\n", "        \u001b[37m    Status: \u001b[0m\u001b[1;32mIn Progress\u001b[0m\n", "        └── \u001b[1;31m❌ LLM Failed\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">\n", "</pre>\n"], "text/plain": ["\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #800000; text-decoration-color: #800000\">╭─────────────────────────────────────────────────── LLM Error ───────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #800000; text-decoration-color: #800000\">│</span>                                                                                                                 <span style=\"color: #800000; text-decoration-color: #800000\">│</span>\n", "<span style=\"color: #800000; text-decoration-color: #800000\">│</span>  <span style=\"color: #800000; text-decoration-color: #800000; font-weight: bold\">❌ LLM Call Failed</span>                                                                                             <span style=\"color: #800000; text-decoration-color: #800000\">│</span>\n", "<span style=\"color: #800000; text-decoration-color: #800000\">│</span>  <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">Error: </span><span style=\"color: #800000; text-decoration-color: #800000\">litellm.AuthenticationError: AuthenticationError: OpenAIException - The api_key client option must be </span>  <span style=\"color: #800000; text-decoration-color: #800000\">│</span>\n", "<span style=\"color: #800000; text-decoration-color: #800000\">│</span>  <span style=\"color: #800000; text-decoration-color: #800000\">set either by passing api_key to the client or by setting the OPENAI_API_KEY environment variable</span>              <span style=\"color: #800000; text-decoration-color: #800000\">│</span>\n", "<span style=\"color: #800000; text-decoration-color: #800000\">│</span>                                                                                                                 <span style=\"color: #800000; text-decoration-color: #800000\">│</span>\n", "<span style=\"color: #800000; text-decoration-color: #800000\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[31m╭─\u001b[0m\u001b[31m──────────────────────────────────────────────────\u001b[0m\u001b[31m LLM Error \u001b[0m\u001b[31m──────────────────────────────────────────────────\u001b[0m\u001b[31m─╮\u001b[0m\n", "\u001b[31m│\u001b[0m                                                                                                                 \u001b[31m│\u001b[0m\n", "\u001b[31m│\u001b[0m  \u001b[1;31m❌ LLM Call Failed\u001b[0m                                                                                             \u001b[31m│\u001b[0m\n", "\u001b[31m│\u001b[0m  \u001b[37mError: \u001b[0m\u001b[31mlitellm.AuthenticationError: AuthenticationError: OpenAIException - The api_key client option must be \u001b[0m  \u001b[31m│\u001b[0m\n", "\u001b[31m│\u001b[0m  \u001b[31mset either by passing api_key to the client or by setting the OPENAI_API_KEY environment variable\u001b[0m              \u001b[31m│\u001b[0m\n", "\u001b[31m│\u001b[0m                                                                                                                 \u001b[31m│\u001b[0m\n", "\u001b[31m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">\n", "</pre>\n"], "text/plain": ["\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:LiteLL<PERSON> call failed: litellm.AuthenticationError: AuthenticationError: OpenAIException - The api_key client option must be set either by passing api_key to the client or by setting the OPENAI_API_KEY environment variable\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[91m Error during LLM call: litellm.AuthenticationError: AuthenticationError: OpenAIException - The api_key client option must be set either by passing api_key to the client or by setting the OPENAI_API_KEY environment variable\u001b[00m\n", "\u001b[91m An unknown error occurred. Please check the details below.\u001b[00m\n", "\u001b[91m Error details: litellm.AuthenticationError: AuthenticationError: OpenAIException - The api_key client option must be set either by passing api_key to the client or by setting the OPENAI_API_KEY environment variable\u001b[00m\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">🚀 Crew: crew</span>\n", "└── <span style=\"color: #800000; text-decoration-color: #800000; font-weight: bold\">📋 Task: 8bbbc838-007a-454a-92e9-4f44d1042c6d</span>\n", "    <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">   Assigned to: </span><span style=\"color: #800000; text-decoration-color: #800000\">Product Returns Assistant</span>\n", "    <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">   Status: </span><span style=\"color: #800000; text-decoration-color: #800000; font-weight: bold\">❌ Failed</span>\n", "    └── <span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">🤖 Agent: </span><span style=\"color: #008000; text-decoration-color: #008000\">Product Returns Assistant</span>\n", "        <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">    Status: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">In Progress</span>\n", "        └── <span style=\"color: #800000; text-decoration-color: #800000; font-weight: bold\">❌ LLM Failed</span>\n", "</pre>\n"], "text/plain": ["\u001b[1;36m🚀 Crew: \u001b[0m\u001b[1;36mcrew\u001b[0m\n", "└── \u001b[1;31m📋 Task: 8bbbc838-007a-454a-92e9-4f44d1042c6d\u001b[0m\n", "    \u001b[37m   Assigned to: \u001b[0m\u001b[31mProduct Returns Assistant\u001b[0m\n", "    \u001b[37m   Status: \u001b[0m\u001b[1;31m❌ Failed\u001b[0m\n", "    └── \u001b[1;32m🤖 Agent: \u001b[0m\u001b[32mProduct Returns Assistant\u001b[0m\n", "        \u001b[37m    Status: \u001b[0m\u001b[1;32mIn Progress\u001b[0m\n", "        └── \u001b[1;31m❌ LLM Failed\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #800000; text-decoration-color: #800000\">╭───────────────────────────────────────────────── Task Failure ──────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #800000; text-decoration-color: #800000\">│</span>                                                                                                                 <span style=\"color: #800000; text-decoration-color: #800000\">│</span>\n", "<span style=\"color: #800000; text-decoration-color: #800000\">│</span>  <span style=\"color: #800000; text-decoration-color: #800000; font-weight: bold\">Task Failed</span>                                                                                                    <span style=\"color: #800000; text-decoration-color: #800000\">│</span>\n", "<span style=\"color: #800000; text-decoration-color: #800000\">│</span>  <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">Name: </span><span style=\"color: #800000; text-decoration-color: #800000\">8bbbc838-007a-454a-92e9-4f44d1042c6d</span>                                                                     <span style=\"color: #800000; text-decoration-color: #800000\">│</span>\n", "<span style=\"color: #800000; text-decoration-color: #800000\">│</span>  <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">Agent: </span><span style=\"color: #800000; text-decoration-color: #800000\">Product Returns Assistant</span>                                                                               <span style=\"color: #800000; text-decoration-color: #800000\">│</span>\n", "<span style=\"color: #800000; text-decoration-color: #800000\">│</span>                                                                                                                 <span style=\"color: #800000; text-decoration-color: #800000\">│</span>\n", "<span style=\"color: #800000; text-decoration-color: #800000\">│</span>                                                                                                                 <span style=\"color: #800000; text-decoration-color: #800000\">│</span>\n", "<span style=\"color: #800000; text-decoration-color: #800000\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[31m╭─\u001b[0m\u001b[31m────────────────────────────────────────────────\u001b[0m\u001b[31m Task Failure \u001b[0m\u001b[31m─────────────────────────────────────────────────\u001b[0m\u001b[31m─╮\u001b[0m\n", "\u001b[31m│\u001b[0m                                                                                                                 \u001b[31m│\u001b[0m\n", "\u001b[31m│\u001b[0m  \u001b[1;31mTask Failed\u001b[0m                                                                                                    \u001b[31m│\u001b[0m\n", "\u001b[31m│\u001b[0m  \u001b[37mName: \u001b[0m\u001b[31m8bbbc838-007a-454a-92e9-4f44d1042c6d\u001b[0m                                                                     \u001b[31m│\u001b[0m\n", "\u001b[31m│\u001b[0m  \u001b[37mAgent: \u001b[0m\u001b[31mProduct Returns Assistant\u001b[0m                                                                               \u001b[31m│\u001b[0m\n", "\u001b[31m│\u001b[0m                                                                                                                 \u001b[31m│\u001b[0m\n", "\u001b[31m│\u001b[0m                                                                                                                 \u001b[31m│\u001b[0m\n", "\u001b[31m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">\n", "</pre>\n"], "text/plain": ["\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #800000; text-decoration-color: #800000\">╭───────────────────────────────────────────────── Crew Failure ──────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #800000; text-decoration-color: #800000\">│</span>                                                                                                                 <span style=\"color: #800000; text-decoration-color: #800000\">│</span>\n", "<span style=\"color: #800000; text-decoration-color: #800000\">│</span>  <span style=\"color: #800000; text-decoration-color: #800000; font-weight: bold\">Crew Execution Failed</span>                                                                                          <span style=\"color: #800000; text-decoration-color: #800000\">│</span>\n", "<span style=\"color: #800000; text-decoration-color: #800000\">│</span>  <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">Name: </span><span style=\"color: #800000; text-decoration-color: #800000\">crew</span>                                                                                                     <span style=\"color: #800000; text-decoration-color: #800000\">│</span>\n", "<span style=\"color: #800000; text-decoration-color: #800000\">│</span>  <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">ID: </span><span style=\"color: #800000; text-decoration-color: #800000\">77e5f4b3-0238-44f4-be1c-df22779a02e1</span>                                                                       <span style=\"color: #800000; text-decoration-color: #800000\">│</span>\n", "<span style=\"color: #800000; text-decoration-color: #800000\">│</span>                                                                                                                 <span style=\"color: #800000; text-decoration-color: #800000\">│</span>\n", "<span style=\"color: #800000; text-decoration-color: #800000\">│</span>                                                                                                                 <span style=\"color: #800000; text-decoration-color: #800000\">│</span>\n", "<span style=\"color: #800000; text-decoration-color: #800000\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[31m╭─\u001b[0m\u001b[31m────────────────────────────────────────────────\u001b[0m\u001b[31m Crew Failure \u001b[0m\u001b[31m─────────────────────────────────────────────────\u001b[0m\u001b[31m─╮\u001b[0m\n", "\u001b[31m│\u001b[0m                                                                                                                 \u001b[31m│\u001b[0m\n", "\u001b[31m│\u001b[0m  \u001b[1;31mCrew Execution Failed\u001b[0m                                                                                          \u001b[31m│\u001b[0m\n", "\u001b[31m│\u001b[0m  \u001b[37mName: \u001b[0m\u001b[31mcrew\u001b[0m                                                                                                     \u001b[31m│\u001b[0m\n", "\u001b[31m│\u001b[0m  \u001b[37mID: \u001b[0m\u001b[31m77e5f4b3-0238-44f4-be1c-df22779a02e1\u001b[0m                                                                       \u001b[31m│\u001b[0m\n", "\u001b[31m│\u001b[0m                                                                                                                 \u001b[31m│\u001b[0m\n", "\u001b[31m│\u001b[0m                                                                                                                 \u001b[31m│\u001b[0m\n", "\u001b[31m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">\n", "</pre>\n"], "text/plain": ["\n"]}, "metadata": {}, "output_type": "display_data"}, {"ename": "AuthenticationError", "evalue": "litellm.AuthenticationError: AuthenticationError: OpenAIException - The api_key client option must be set either by passing api_key to the client or by setting the OPENAI_API_KEY environment variable", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                               <PERSON><PERSON> (most recent call last)", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\anaconda3\\envs\\crewai\\Lib\\site-packages\\litellm\\llms\\openai\\openai.py:711\u001b[39m, in \u001b[36mOpenAIChatCompletion.completion\u001b[39m\u001b[34m(self, model_response, timeout, optional_params, litellm_params, logging_obj, model, messages, print_verbose, api_key, api_base, api_version, dynamic_params, azure_ad_token, acompletion, logger_fn, headers, custom_prompt_dict, client, organization, custom_llm_provider, drop_params)\u001b[39m\n\u001b[32m    710\u001b[39m             \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m711\u001b[39m                 \u001b[38;5;28;01mraise\u001b[39;00m e\n\u001b[32m    712\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m OpenAIError \u001b[38;5;28;01mas\u001b[39;00m e:\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\anaconda3\\envs\\crewai\\Lib\\site-packages\\litellm\\llms\\openai\\openai.py:614\u001b[39m, in \u001b[36mOpenAIChatCompletion.completion\u001b[39m\u001b[34m(self, model_response, timeout, optional_params, litellm_params, logging_obj, model, messages, print_verbose, api_key, api_base, api_version, dynamic_params, azure_ad_token, acompletion, logger_fn, headers, custom_prompt_dict, client, organization, custom_llm_provider, drop_params)\u001b[39m\n\u001b[32m    611\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m OpenAIError(\n\u001b[32m    612\u001b[39m         status_code=\u001b[32m422\u001b[39m, message=\u001b[33m\"\u001b[39m\u001b[33mmax retries must be an int\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    613\u001b[39m     )\n\u001b[32m--> \u001b[39m\u001b[32m614\u001b[39m openai_client: OpenAI = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_get_openai_client\u001b[49m\u001b[43m(\u001b[49m\u001b[43m  \u001b[49m\u001b[38;5;66;43;03m# type: ignore\u001b[39;49;00m\n\u001b[32m    615\u001b[39m \u001b[43m    \u001b[49m\u001b[43mis_async\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[32m    616\u001b[39m \u001b[43m    \u001b[49m\u001b[43mapi_key\u001b[49m\u001b[43m=\u001b[49m\u001b[43mapi_key\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    617\u001b[39m \u001b[43m    \u001b[49m\u001b[43mapi_base\u001b[49m\u001b[43m=\u001b[49m\u001b[43mapi_base\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    618\u001b[39m \u001b[43m    \u001b[49m\u001b[43mapi_version\u001b[49m\u001b[43m=\u001b[49m\u001b[43mapi_version\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    619\u001b[39m \u001b[43m    \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m=\u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    620\u001b[39m \u001b[43m    \u001b[49m\u001b[43mmax_retries\u001b[49m\u001b[43m=\u001b[49m\u001b[43mmax_retries\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    621\u001b[39m \u001b[43m    \u001b[49m\u001b[43morganization\u001b[49m\u001b[43m=\u001b[49m\u001b[43morganization\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    622\u001b[39m \u001b[43m    \u001b[49m\u001b[43mclient\u001b[49m\u001b[43m=\u001b[49m\u001b[43mclient\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    623\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    625\u001b[39m \u001b[38;5;66;03m## LOGGING\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\anaconda3\\envs\\crewai\\Lib\\site-packages\\litellm\\llms\\openai\\openai.py:368\u001b[39m, in \u001b[36mOpenAIChatCompletion._get_openai_client\u001b[39m\u001b[34m(self, is_async, api_key, api_base, api_version, timeout, max_retries, organization, client)\u001b[39m\n\u001b[32m    366\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m368\u001b[39m     _new_client = \u001b[43mOpenAI\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    369\u001b[39m \u001b[43m        \u001b[49m\u001b[43mapi_key\u001b[49m\u001b[43m=\u001b[49m\u001b[43mapi_key\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    370\u001b[39m \u001b[43m        \u001b[49m\u001b[43mbase_url\u001b[49m\u001b[43m=\u001b[49m\u001b[43mapi_base\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    371\u001b[39m \u001b[43m        \u001b[49m\u001b[43mhttp_client\u001b[49m\u001b[43m=\u001b[49m\u001b[43mlitellm\u001b[49m\u001b[43m.\u001b[49m\u001b[43mclient_session\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    372\u001b[39m \u001b[43m        \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m=\u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    373\u001b[39m \u001b[43m        \u001b[49m\u001b[43mmax_retries\u001b[49m\u001b[43m=\u001b[49m\u001b[43mmax_retries\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    374\u001b[39m \u001b[43m        \u001b[49m\u001b[43morganization\u001b[49m\u001b[43m=\u001b[49m\u001b[43morganization\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    375\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    377\u001b[39m \u001b[38;5;66;03m## SAVE CACHE KEY\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\anaconda3\\envs\\crewai\\Lib\\site-packages\\openai\\_client.py:114\u001b[39m, in \u001b[36mOpenAI.__init__\u001b[39m\u001b[34m(self, api_key, organization, project, base_url, websocket_base_url, timeout, max_retries, default_headers, default_query, http_client, _strict_response_validation)\u001b[39m\n\u001b[32m    113\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m api_key \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m114\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m OpenAIError(\n\u001b[32m    115\u001b[39m         \u001b[33m\"\u001b[39m\u001b[33mThe api_key client option must be set either by passing api_key to the client or by setting the OPENAI_API_KEY environment variable\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    116\u001b[39m     )\n\u001b[32m    117\u001b[39m \u001b[38;5;28mself\u001b[39m.api_key = api_key\n", "\u001b[31mOpenAIError\u001b[39m: The api_key client option must be set either by passing api_key to the client or by setting the OPENAI_API_KEY environment variable", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                               <PERSON><PERSON> (most recent call last)", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\anaconda3\\envs\\crewai\\Lib\\site-packages\\litellm\\main.py:1692\u001b[39m, in \u001b[36mcompletion\u001b[39m\u001b[34m(model, messages, timeout, temperature, top_p, n, stream, stream_options, stop, max_completion_tokens, max_tokens, modalities, prediction, audio, presence_penalty, frequency_penalty, logit_bias, user, reasoning_effort, response_format, seed, tools, tool_choice, logprobs, top_logprobs, parallel_tool_calls, deployment_id, extra_headers, functions, function_call, base_url, api_version, api_key, model_list, **kwargs)\u001b[39m\n\u001b[32m   1686\u001b[39m     logging.post_call(\n\u001b[32m   1687\u001b[39m         \u001b[38;5;28minput\u001b[39m=messages,\n\u001b[32m   1688\u001b[39m         api_key=api_key,\n\u001b[32m   1689\u001b[39m         original_response=\u001b[38;5;28mstr\u001b[39m(e),\n\u001b[32m   1690\u001b[39m         additional_args={\u001b[33m\"\u001b[39m\u001b[33mheaders\u001b[39m\u001b[33m\"\u001b[39m: headers},\n\u001b[32m   1691\u001b[39m     )\n\u001b[32m-> \u001b[39m\u001b[32m1692\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m e\n\u001b[32m   1694\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m optional_params.get(\u001b[33m\"\u001b[39m\u001b[33mstream\u001b[39m\u001b[33m\"\u001b[39m, \u001b[38;5;28;01mFalse\u001b[39;00m):\n\u001b[32m   1695\u001b[39m     \u001b[38;5;66;03m## LOGGING\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\anaconda3\\envs\\crewai\\Lib\\site-packages\\litellm\\main.py:1665\u001b[39m, in \u001b[36mcompletion\u001b[39m\u001b[34m(model, messages, timeout, temperature, top_p, n, stream, stream_options, stop, max_completion_tokens, max_tokens, modalities, prediction, audio, presence_penalty, frequency_penalty, logit_bias, user, reasoning_effort, response_format, seed, tools, tool_choice, logprobs, top_logprobs, parallel_tool_calls, deployment_id, extra_headers, functions, function_call, base_url, api_version, api_key, model_list, **kwargs)\u001b[39m\n\u001b[32m   1664\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m-> \u001b[39m\u001b[32m1665\u001b[39m     response = \u001b[43mopenai_chat_completions\u001b[49m\u001b[43m.\u001b[49m\u001b[43mcompletion\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m   1666\u001b[39m \u001b[43m        \u001b[49m\u001b[43mmodel\u001b[49m\u001b[43m=\u001b[49m\u001b[43mmodel\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1667\u001b[39m \u001b[43m        \u001b[49m\u001b[43mmessages\u001b[49m\u001b[43m=\u001b[49m\u001b[43mmessages\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1668\u001b[39m \u001b[43m        \u001b[49m\u001b[43mheaders\u001b[49m\u001b[43m=\u001b[49m\u001b[43mheaders\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1669\u001b[39m \u001b[43m        \u001b[49m\u001b[43mmodel_response\u001b[49m\u001b[43m=\u001b[49m\u001b[43mmodel_response\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1670\u001b[39m \u001b[43m        \u001b[49m\u001b[43mprint_verbose\u001b[49m\u001b[43m=\u001b[49m\u001b[43mprint_verbose\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1671\u001b[39m \u001b[43m        \u001b[49m\u001b[43mapi_key\u001b[49m\u001b[43m=\u001b[49m\u001b[43mapi_key\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1672\u001b[39m \u001b[43m        \u001b[49m\u001b[43mapi_base\u001b[49m\u001b[43m=\u001b[49m\u001b[43mapi_base\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1673\u001b[39m \u001b[43m        \u001b[49m\u001b[43macompletion\u001b[49m\u001b[43m=\u001b[49m\u001b[43macompletion\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1674\u001b[39m \u001b[43m        \u001b[49m\u001b[43mlogging_obj\u001b[49m\u001b[43m=\u001b[49m\u001b[43mlogging\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1675\u001b[39m \u001b[43m        \u001b[49m\u001b[43moptional_params\u001b[49m\u001b[43m=\u001b[49m\u001b[43moptional_params\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1676\u001b[39m \u001b[43m        \u001b[49m\u001b[43mlitellm_params\u001b[49m\u001b[43m=\u001b[49m\u001b[43mlitellm_params\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1677\u001b[39m \u001b[43m        \u001b[49m\u001b[43mlogger_fn\u001b[49m\u001b[43m=\u001b[49m\u001b[43mlogger_fn\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1678\u001b[39m \u001b[43m        \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m=\u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m,\u001b[49m\u001b[43m  \u001b[49m\u001b[38;5;66;43;03m# type: ignore\u001b[39;49;00m\n\u001b[32m   1679\u001b[39m \u001b[43m        \u001b[49m\u001b[43mcustom_prompt_dict\u001b[49m\u001b[43m=\u001b[49m\u001b[43mcustom_prompt_dict\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1680\u001b[39m \u001b[43m        \u001b[49m\u001b[43mclient\u001b[49m\u001b[43m=\u001b[49m\u001b[43mclient\u001b[49m\u001b[43m,\u001b[49m\u001b[43m  \u001b[49m\u001b[38;5;66;43;03m# pass AsyncOpenAI, OpenAI client\u001b[39;49;00m\n\u001b[32m   1681\u001b[39m \u001b[43m        \u001b[49m\u001b[43morganization\u001b[49m\u001b[43m=\u001b[49m\u001b[43morganization\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1682\u001b[39m \u001b[43m        \u001b[49m\u001b[43mcustom_llm_provider\u001b[49m\u001b[43m=\u001b[49m\u001b[43mcustom_llm_provider\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1683\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1684\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[32m   1685\u001b[39m     \u001b[38;5;66;03m## LOGGING - log the original exception returned\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\anaconda3\\envs\\crewai\\Lib\\site-packages\\litellm\\llms\\openai\\openai.py:721\u001b[39m, in \u001b[36mOpenAIChatCompletion.completion\u001b[39m\u001b[34m(self, model_response, timeout, optional_params, litellm_params, logging_obj, model, messages, print_verbose, api_key, api_base, api_version, dynamic_params, azure_ad_token, acompletion, logger_fn, headers, custom_prompt_dict, client, organization, custom_llm_provider, drop_params)\u001b[39m\n\u001b[32m    720\u001b[39m     error_headers = \u001b[38;5;28mgetattr\u001b[39m(error_response, \u001b[33m\"\u001b[39m\u001b[33mheaders\u001b[39m\u001b[33m\"\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m)\n\u001b[32m--> \u001b[39m\u001b[32m721\u001b[39m \u001b[38;5;28;01mraise\u001b[39;00m OpenAIError(\n\u001b[32m    722\u001b[39m     status_code=status_code, message=error_text, headers=error_headers\n\u001b[32m    723\u001b[39m )\n", "\u001b[31mOpenAIError\u001b[39m: The api_key client option must be set either by passing api_key to the client or by setting the OPENAI_API_KEY environment variable", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[31mAuthenticationError\u001b[39m                       <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[6]\u001b[39m\u001b[32m, line 1\u001b[39m\n\u001b[32m----> \u001b[39m\u001b[32m1\u001b[39m result = \u001b[43mcrew\u001b[49m\u001b[43m.\u001b[49m\u001b[43mkickoff\u001b[49m\u001b[43m(\u001b[49m\u001b[43minputs\u001b[49m\u001b[43m=\u001b[49m\u001b[43m{\u001b[49m\n\u001b[32m      2\u001b[39m \u001b[43m    \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mquestion\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mCan I get a refund if I used the item once?\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\n\u001b[32m      3\u001b[39m \u001b[43m}\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m      5\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpprint\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m pprint\n\u001b[32m      6\u001b[39m pprint(result.raw)\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\anaconda3\\envs\\crewai\\Lib\\site-packages\\crewai\\crew.py:646\u001b[39m, in \u001b[36mCrew.kickoff\u001b[39m\u001b[34m(self, inputs)\u001b[39m\n\u001b[32m    643\u001b[39m metrics: List[UsageMetrics] = []\n\u001b[32m    645\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m.process == Process.sequential:\n\u001b[32m--> \u001b[39m\u001b[32m646\u001b[39m     result = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_run_sequential_process\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    647\u001b[39m \u001b[38;5;28;01melif\u001b[39;00m \u001b[38;5;28mself\u001b[39m.process == Process.hierarchical:\n\u001b[32m    648\u001b[39m     result = \u001b[38;5;28mself\u001b[39m._run_hierarchical_process()\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\anaconda3\\envs\\crewai\\Lib\\site-packages\\crewai\\crew.py:758\u001b[39m, in \u001b[36mCrew._run_sequential_process\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    756\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34m_run_sequential_process\u001b[39m(\u001b[38;5;28mself\u001b[39m) -> CrewOutput:\n\u001b[32m    757\u001b[39m \u001b[38;5;250m    \u001b[39m\u001b[33;03m\"\"\"Executes tasks sequentially and returns the final output.\"\"\"\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m758\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_execute_tasks\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mtasks\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\anaconda3\\envs\\crewai\\Lib\\site-packages\\crewai\\crew.py:861\u001b[39m, in \u001b[36mCrew._execute_tasks\u001b[39m\u001b[34m(self, tasks, start_index, was_replayed)\u001b[39m\n\u001b[32m    858\u001b[39m     futures.clear()\n\u001b[32m    860\u001b[39m context = \u001b[38;5;28mself\u001b[39m._get_context(task, task_outputs)\n\u001b[32m--> \u001b[39m\u001b[32m861\u001b[39m task_output = \u001b[43mtask\u001b[49m\u001b[43m.\u001b[49m\u001b[43mexecute_sync\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    862\u001b[39m \u001b[43m    \u001b[49m\u001b[43magent\u001b[49m\u001b[43m=\u001b[49m\u001b[43magent_to_use\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    863\u001b[39m \u001b[43m    \u001b[49m\u001b[43mcontext\u001b[49m\u001b[43m=\u001b[49m\u001b[43mcontext\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    864\u001b[39m \u001b[43m    \u001b[49m\u001b[43mtools\u001b[49m\u001b[43m=\u001b[49m\u001b[43mcast\u001b[49m\u001b[43m(\u001b[49m\u001b[43mList\u001b[49m\u001b[43m[\u001b[49m\u001b[43mBaseTool\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtools_for_task\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    865\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    866\u001b[39m task_outputs.append(task_output)\n\u001b[32m    867\u001b[39m \u001b[38;5;28mself\u001b[39m._process_task_result(task, task_output)\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\anaconda3\\envs\\crewai\\Lib\\site-packages\\crewai\\task.py:328\u001b[39m, in \u001b[36mTask.execute_sync\u001b[39m\u001b[34m(self, agent, context, tools)\u001b[39m\n\u001b[32m    321\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mexecute_sync\u001b[39m(\n\u001b[32m    322\u001b[39m     \u001b[38;5;28mself\u001b[39m,\n\u001b[32m    323\u001b[39m     agent: Optional[BaseAgent] = \u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[32m    324\u001b[39m     context: Optional[\u001b[38;5;28mstr\u001b[39m] = \u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[32m    325\u001b[39m     tools: Optional[List[BaseTool]] = \u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[32m    326\u001b[39m ) -> TaskOutput:\n\u001b[32m    327\u001b[39m \u001b[38;5;250m    \u001b[39m\u001b[33;03m\"\"\"Execute the task synchronously.\"\"\"\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m328\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_execute_core\u001b[49m\u001b[43m(\u001b[49m\u001b[43magent\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcontext\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtools\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\anaconda3\\envs\\crewai\\Lib\\site-packages\\crewai\\task.py:472\u001b[39m, in \u001b[36mTask._execute_core\u001b[39m\u001b[34m(self, agent, context, tools)\u001b[39m\n\u001b[32m    470\u001b[39m \u001b[38;5;28mself\u001b[39m.end_time = datetime.datetime.now()\n\u001b[32m    471\u001b[39m crewai_event_bus.emit(\u001b[38;5;28mself\u001b[39m, TaskFailedEvent(error=\u001b[38;5;28mstr\u001b[39m(e), task=\u001b[38;5;28mself\u001b[39m))\n\u001b[32m--> \u001b[39m\u001b[32m472\u001b[39m \u001b[38;5;28;01mraise\u001b[39;00m e\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\anaconda3\\envs\\crewai\\Lib\\site-packages\\crewai\\task.py:392\u001b[39m, in \u001b[36mTask._execute_core\u001b[39m\u001b[34m(self, agent, context, tools)\u001b[39m\n\u001b[32m    390\u001b[39m \u001b[38;5;28mself\u001b[39m.processed_by_agents.add(agent.role)\n\u001b[32m    391\u001b[39m crewai_event_bus.emit(\u001b[38;5;28mself\u001b[39m, TaskStartedEvent(context=context, task=\u001b[38;5;28mself\u001b[39m))\n\u001b[32m--> \u001b[39m\u001b[32m392\u001b[39m result = \u001b[43magent\u001b[49m\u001b[43m.\u001b[49m\u001b[43mexecute_task\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    393\u001b[39m \u001b[43m    \u001b[49m\u001b[43mtask\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m    394\u001b[39m \u001b[43m    \u001b[49m\u001b[43mcontext\u001b[49m\u001b[43m=\u001b[49m\u001b[43mcontext\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    395\u001b[39m \u001b[43m    \u001b[49m\u001b[43mtools\u001b[49m\u001b[43m=\u001b[49m\u001b[43mtools\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    396\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    398\u001b[39m pydantic_output, json_output = \u001b[38;5;28mself\u001b[39m._export_output(result)\n\u001b[32m    399\u001b[39m task_output = TaskOutput(\n\u001b[32m    400\u001b[39m     name=\u001b[38;5;28mself\u001b[39m.name,\n\u001b[32m    401\u001b[39m     description=\u001b[38;5;28mself\u001b[39m.description,\n\u001b[32m   (...)\u001b[39m\u001b[32m    407\u001b[39m     output_format=\u001b[38;5;28mself\u001b[39m._get_output_format(),\n\u001b[32m    408\u001b[39m )\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\anaconda3\\envs\\crewai\\Lib\\site-packages\\crewai\\agent.py:269\u001b[39m, in \u001b[36mAgent.execute_task\u001b[39m\u001b[34m(self, task, context, tools)\u001b[39m\n\u001b[32m    259\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m e.\u001b[34m__class__\u001b[39m.\u001b[34m__module__\u001b[39m.startswith(\u001b[33m\"\u001b[39m\u001b[33mlitellm\u001b[39m\u001b[33m\"\u001b[39m):\n\u001b[32m    260\u001b[39m     \u001b[38;5;66;03m# Do not retry on litellm errors\u001b[39;00m\n\u001b[32m    261\u001b[39m     crewai_event_bus.emit(\n\u001b[32m    262\u001b[39m         \u001b[38;5;28mself\u001b[39m,\n\u001b[32m    263\u001b[39m         event=AgentExecutionErrorEvent(\n\u001b[32m   (...)\u001b[39m\u001b[32m    267\u001b[39m         ),\n\u001b[32m    268\u001b[39m     )\n\u001b[32m--> \u001b[39m\u001b[32m269\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m e\n\u001b[32m    270\u001b[39m \u001b[38;5;28mself\u001b[39m._times_executed += \u001b[32m1\u001b[39m\n\u001b[32m    271\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m._times_executed > \u001b[38;5;28mself\u001b[39m.max_retry_limit:\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\anaconda3\\envs\\crewai\\Lib\\site-packages\\crewai\\agent.py:250\u001b[39m, in \u001b[36mAgent.execute_task\u001b[39m\u001b[34m(self, task, context, tools)\u001b[39m\n\u001b[32m    240\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m    241\u001b[39m     crewai_event_bus.emit(\n\u001b[32m    242\u001b[39m         \u001b[38;5;28mself\u001b[39m,\n\u001b[32m    243\u001b[39m         event=AgentExecutionStartedEvent(\n\u001b[32m   (...)\u001b[39m\u001b[32m    248\u001b[39m         ),\n\u001b[32m    249\u001b[39m     )\n\u001b[32m--> \u001b[39m\u001b[32m250\u001b[39m     result = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43magent_executor\u001b[49m\u001b[43m.\u001b[49m\u001b[43minvoke\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    251\u001b[39m \u001b[43m        \u001b[49m\u001b[43m{\u001b[49m\n\u001b[32m    252\u001b[39m \u001b[43m            \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43minput\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mtask_prompt\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    253\u001b[39m \u001b[43m            \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mtool_names\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43magent_executor\u001b[49m\u001b[43m.\u001b[49m\u001b[43mtools_names\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    254\u001b[39m \u001b[43m            \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mtools\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43magent_executor\u001b[49m\u001b[43m.\u001b[49m\u001b[43mtools_description\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    255\u001b[39m \u001b[43m            \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mask_for_human_input\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mtask\u001b[49m\u001b[43m.\u001b[49m\u001b[43mhuman_input\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    256\u001b[39m \u001b[43m        \u001b[49m\u001b[43m}\u001b[49m\n\u001b[32m    257\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m[\u001b[33m\"\u001b[39m\u001b[33moutput\u001b[39m\u001b[33m\"\u001b[39m]\n\u001b[32m    258\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[32m    259\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m e.\u001b[34m__class__\u001b[39m.\u001b[34m__module__\u001b[39m.startswith(\u001b[33m\"\u001b[39m\u001b[33mlitellm\u001b[39m\u001b[33m\"\u001b[39m):\n\u001b[32m    260\u001b[39m         \u001b[38;5;66;03m# Do not retry on litellm errors\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\anaconda3\\envs\\crewai\\Lib\\site-packages\\crewai\\agents\\crew_agent_executor.py:123\u001b[39m, in \u001b[36mCrewAgentExecutor.invoke\u001b[39m\u001b[34m(self, inputs)\u001b[39m\n\u001b[32m    120\u001b[39m handle_unknown_error(\u001b[38;5;28mself\u001b[39m._printer, e)\n\u001b[32m    121\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m e.\u001b[34m__class__\u001b[39m.\u001b[34m__module__\u001b[39m.startswith(\u001b[33m\"\u001b[39m\u001b[33mlitellm\u001b[39m\u001b[33m\"\u001b[39m):\n\u001b[32m    122\u001b[39m     \u001b[38;5;66;03m# Do not retry on litellm errors\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m123\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m e\n\u001b[32m    124\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m    125\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m e\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\anaconda3\\envs\\crewai\\Lib\\site-packages\\crewai\\agents\\crew_agent_executor.py:112\u001b[39m, in \u001b[36mCrewAgentExecutor.invoke\u001b[39m\u001b[34m(self, inputs)\u001b[39m\n\u001b[32m    109\u001b[39m \u001b[38;5;28mself\u001b[39m.ask_for_human_input = \u001b[38;5;28mbool\u001b[39m(inputs.get(\u001b[33m\"\u001b[39m\u001b[33mask_for_human_input\u001b[39m\u001b[33m\"\u001b[39m, \u001b[38;5;28;01mFalse\u001b[39;00m))\n\u001b[32m    111\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m112\u001b[39m     formatted_answer = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_invoke_loop\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    113\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mAssertionError\u001b[39;00m:\n\u001b[32m    114\u001b[39m     \u001b[38;5;28mself\u001b[39m._printer.print(\n\u001b[32m    115\u001b[39m         content=\u001b[33m\"\u001b[39m\u001b[33mAgent failed to reach a final answer. This is likely a bug - please report it.\u001b[39m\u001b[33m\"\u001b[39m,\n\u001b[32m    116\u001b[39m         color=\u001b[33m\"\u001b[39m\u001b[33mred\u001b[39m\u001b[33m\"\u001b[39m,\n\u001b[32m    117\u001b[39m     )\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\anaconda3\\envs\\crewai\\Lib\\site-packages\\crewai\\agents\\crew_agent_executor.py:208\u001b[39m, in \u001b[36mCrewAgentExecutor._invoke_loop\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    205\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[32m    206\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m e.\u001b[34m__class__\u001b[39m.\u001b[34m__module__\u001b[39m.startswith(\u001b[33m\"\u001b[39m\u001b[33mlitellm\u001b[39m\u001b[33m\"\u001b[39m):\n\u001b[32m    207\u001b[39m         \u001b[38;5;66;03m# Do not retry on litellm errors\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m208\u001b[39m         \u001b[38;5;28;01mraise\u001b[39;00m e\n\u001b[32m    209\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m is_context_length_exceeded(e):\n\u001b[32m    210\u001b[39m         handle_context_length(\n\u001b[32m    211\u001b[39m             respect_context_window=\u001b[38;5;28mself\u001b[39m.respect_context_window,\n\u001b[32m    212\u001b[39m             printer=\u001b[38;5;28mself\u001b[39m._printer,\n\u001b[32m   (...)\u001b[39m\u001b[32m    216\u001b[39m             i18n=\u001b[38;5;28mself\u001b[39m._i18n,\n\u001b[32m    217\u001b[39m         )\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\anaconda3\\envs\\crewai\\Lib\\site-packages\\crewai\\agents\\crew_agent_executor.py:155\u001b[39m, in \u001b[36mCrewAgentExecutor._invoke_loop\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    144\u001b[39m     formatted_answer = handle_max_iterations_exceeded(\n\u001b[32m    145\u001b[39m         formatted_answer,\n\u001b[32m    146\u001b[39m         printer=\u001b[38;5;28mself\u001b[39m._printer,\n\u001b[32m   (...)\u001b[39m\u001b[32m    150\u001b[39m         callbacks=\u001b[38;5;28mself\u001b[39m.callbacks,\n\u001b[32m    151\u001b[39m     )\n\u001b[32m    153\u001b[39m enforce_rpm_limit(\u001b[38;5;28mself\u001b[39m.request_within_rpm_limit)\n\u001b[32m--> \u001b[39m\u001b[32m155\u001b[39m answer = \u001b[43mget_llm_response\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    156\u001b[39m \u001b[43m    \u001b[49m\u001b[43mllm\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mllm\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    157\u001b[39m \u001b[43m    \u001b[49m\u001b[43mmessages\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mmessages\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    158\u001b[39m \u001b[43m    \u001b[49m\u001b[43mcallbacks\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mcallbacks\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    159\u001b[39m \u001b[43m    \u001b[49m\u001b[43mprinter\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_printer\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    160\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    161\u001b[39m formatted_answer = process_llm_response(answer, \u001b[38;5;28mself\u001b[39m.use_stop_words)\n\u001b[32m    163\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(formatted_answer, AgentAction):\n\u001b[32m    164\u001b[39m     \u001b[38;5;66;03m# Extract agent fingerprint if available\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\anaconda3\\envs\\crewai\\Lib\\site-packages\\crewai\\utilities\\agent_utils.py:157\u001b[39m, in \u001b[36mget_llm_response\u001b[39m\u001b[34m(llm, messages, callbacks, printer)\u001b[39m\n\u001b[32m    152\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[32m    153\u001b[39m     printer.print(\n\u001b[32m    154\u001b[39m         content=\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mError during LLM call: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00me\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m,\n\u001b[32m    155\u001b[39m         color=\u001b[33m\"\u001b[39m\u001b[33mred\u001b[39m\u001b[33m\"\u001b[39m,\n\u001b[32m    156\u001b[39m     )\n\u001b[32m--> \u001b[39m\u001b[32m157\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m e\n\u001b[32m    158\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m answer:\n\u001b[32m    159\u001b[39m     printer.print(\n\u001b[32m    160\u001b[39m         content=\u001b[33m\"\u001b[39m\u001b[33mReceived None or empty response from <PERSON>M call.\u001b[39m\u001b[33m\"\u001b[39m,\n\u001b[32m    161\u001b[39m         color=\u001b[33m\"\u001b[39m\u001b[33mred\u001b[39m\u001b[33m\"\u001b[39m,\n\u001b[32m    162\u001b[39m     )\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\anaconda3\\envs\\crewai\\Lib\\site-packages\\crewai\\utilities\\agent_utils.py:148\u001b[39m, in \u001b[36mget_llm_response\u001b[39m\u001b[34m(llm, messages, callbacks, printer)\u001b[39m\n\u001b[32m    146\u001b[39m \u001b[38;5;250m\u001b[39m\u001b[33;03m\"\"\"Call the LLM and return the response, handling any invalid responses.\"\"\"\u001b[39;00m\n\u001b[32m    147\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m148\u001b[39m     answer = \u001b[43mllm\u001b[49m\u001b[43m.\u001b[49m\u001b[43mcall\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    149\u001b[39m \u001b[43m        \u001b[49m\u001b[43mmessages\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    150\u001b[39m \u001b[43m        \u001b[49m\u001b[43mcallbacks\u001b[49m\u001b[43m=\u001b[49m\u001b[43mcallbacks\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    151\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    152\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[32m    153\u001b[39m     printer.print(\n\u001b[32m    154\u001b[39m         content=\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mError during LLM call: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00me\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m,\n\u001b[32m    155\u001b[39m         color=\u001b[33m\"\u001b[39m\u001b[33mred\u001b[39m\u001b[33m\"\u001b[39m,\n\u001b[32m    156\u001b[39m     )\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\anaconda3\\envs\\crewai\\Lib\\site-packages\\crewai\\llm.py:794\u001b[39m, in \u001b[36mLLM.call\u001b[39m\u001b[34m(self, messages, tools, callbacks, available_functions)\u001b[39m\n\u001b[32m    790\u001b[39m         \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m._handle_streaming_response(\n\u001b[32m    791\u001b[39m             params, callbacks, available_functions\n\u001b[32m    792\u001b[39m         )\n\u001b[32m    793\u001b[39m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m794\u001b[39m         \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_handle_non_streaming_response\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    795\u001b[39m \u001b[43m            \u001b[49m\u001b[43mparams\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcallbacks\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mavailable_functions\u001b[49m\n\u001b[32m    796\u001b[39m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    798\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[32m    799\u001b[39m     crewai_event_bus.emit(\n\u001b[32m    800\u001b[39m         \u001b[38;5;28mself\u001b[39m,\n\u001b[32m    801\u001b[39m         event=LLMCallFailedEvent(error=\u001b[38;5;28mstr\u001b[39m(e)),\n\u001b[32m    802\u001b[39m     )\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\anaconda3\\envs\\crewai\\Lib\\site-packages\\crewai\\llm.py:630\u001b[39m, in \u001b[36mLLM._handle_non_streaming_response\u001b[39m\u001b[34m(self, params, callbacks, available_functions)\u001b[39m\n\u001b[32m    619\u001b[39m \u001b[38;5;250m\u001b[39m\u001b[33;03m\"\"\"Handle a non-streaming response from the LLM.\u001b[39;00m\n\u001b[32m    620\u001b[39m \n\u001b[32m    621\u001b[39m \u001b[33;03mArgs:\u001b[39;00m\n\u001b[32m   (...)\u001b[39m\u001b[32m    627\u001b[39m \u001b[33;03m    str: The response text\u001b[39;00m\n\u001b[32m    628\u001b[39m \u001b[33;03m\"\"\"\u001b[39;00m\n\u001b[32m    629\u001b[39m \u001b[38;5;66;03m# --- 1) Make the completion call\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m630\u001b[39m response = \u001b[43mlitellm\u001b[49m\u001b[43m.\u001b[49m\u001b[43mcompletion\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mparams\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    632\u001b[39m \u001b[38;5;66;03m# --- 2) Extract response message and content\u001b[39;00m\n\u001b[32m    633\u001b[39m response_message = cast(Choices, cast(ModelResponse, response).choices)[\n\u001b[32m    634\u001b[39m     \u001b[32m0\u001b[39m\n\u001b[32m    635\u001b[39m ].message\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\anaconda3\\envs\\crewai\\Lib\\site-packages\\litellm\\utils.py:1154\u001b[39m, in \u001b[36mclient.<locals>.wrapper\u001b[39m\u001b[34m(*args, **kwargs)\u001b[39m\n\u001b[32m   1150\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m logging_obj:\n\u001b[32m   1151\u001b[39m     logging_obj.failure_handler(\n\u001b[32m   1152\u001b[39m         e, traceback_exception, start_time, end_time\n\u001b[32m   1153\u001b[39m     )  \u001b[38;5;66;03m# DO NOT MAKE THREADED - router retry fallback relies on this!\u001b[39;00m\n\u001b[32m-> \u001b[39m\u001b[32m1154\u001b[39m \u001b[38;5;28;01mraise\u001b[39;00m e\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\anaconda3\\envs\\crewai\\Lib\\site-packages\\litellm\\utils.py:1032\u001b[39m, in \u001b[36mclient.<locals>.wrapper\u001b[39m\u001b[34m(*args, **kwargs)\u001b[39m\n\u001b[32m   1030\u001b[39m         print_verbose(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mError while checking max token limit: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mstr\u001b[39m(e)\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m)\n\u001b[32m   1031\u001b[39m \u001b[38;5;66;03m# MODEL CALL\u001b[39;00m\n\u001b[32m-> \u001b[39m\u001b[32m1032\u001b[39m result = \u001b[43moriginal_function\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1033\u001b[39m end_time = datetime.datetime.now()\n\u001b[32m   1034\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[33m\"\u001b[39m\u001b[33mstream\u001b[39m\u001b[33m\"\u001b[39m \u001b[38;5;129;01min\u001b[39;00m kwargs \u001b[38;5;129;01mand\u001b[39;00m kwargs[\u001b[33m\"\u001b[39m\u001b[33mstream\u001b[39m\u001b[33m\"\u001b[39m] \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mTrue\u001b[39;00m:\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\anaconda3\\envs\\crewai\\Lib\\site-packages\\litellm\\main.py:3068\u001b[39m, in \u001b[36mcompletion\u001b[39m\u001b[34m(model, messages, timeout, temperature, top_p, n, stream, stream_options, stop, max_completion_tokens, max_tokens, modalities, prediction, audio, presence_penalty, frequency_penalty, logit_bias, user, reasoning_effort, response_format, seed, tools, tool_choice, logprobs, top_logprobs, parallel_tool_calls, deployment_id, extra_headers, functions, function_call, base_url, api_version, api_key, model_list, **kwargs)\u001b[39m\n\u001b[32m   3065\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m response\n\u001b[32m   3066\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[32m   3067\u001b[39m     \u001b[38;5;66;03m## Map to OpenAI Exception\u001b[39;00m\n\u001b[32m-> \u001b[39m\u001b[32m3068\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[43mexception_type\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m   3069\u001b[39m \u001b[43m        \u001b[49m\u001b[43mmodel\u001b[49m\u001b[43m=\u001b[49m\u001b[43mmodel\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   3070\u001b[39m \u001b[43m        \u001b[49m\u001b[43mcustom_llm_provider\u001b[49m\u001b[43m=\u001b[49m\u001b[43mcustom_llm_provider\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   3071\u001b[39m \u001b[43m        \u001b[49m\u001b[43moriginal_exception\u001b[49m\u001b[43m=\u001b[49m\u001b[43me\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   3072\u001b[39m \u001b[43m        \u001b[49m\u001b[43mcompletion_kwargs\u001b[49m\u001b[43m=\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   3073\u001b[39m \u001b[43m        \u001b[49m\u001b[43mextra_kwargs\u001b[49m\u001b[43m=\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   3074\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\anaconda3\\envs\\crewai\\Lib\\site-packages\\litellm\\litellm_core_utils\\exception_mapping_utils.py:2201\u001b[39m, in \u001b[36mexception_type\u001b[39m\u001b[34m(model, original_exception, custom_llm_provider, completion_kwargs, extra_kwargs)\u001b[39m\n\u001b[32m   2199\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m exception_mapping_worked:\n\u001b[32m   2200\u001b[39m     \u001b[38;5;28msetattr\u001b[39m(e, \u001b[33m\"\u001b[39m\u001b[33mlitellm_response_headers\u001b[39m\u001b[33m\"\u001b[39m, litellm_response_headers)\n\u001b[32m-> \u001b[39m\u001b[32m2201\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m e\n\u001b[32m   2202\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m   2203\u001b[39m     \u001b[38;5;28;01mfor\u001b[39;00m error_type \u001b[38;5;129;01min\u001b[39;00m litellm.LITELLM_EXCEPTION_TYPES:\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\anaconda3\\envs\\crewai\\Lib\\site-packages\\litellm\\litellm_core_utils\\exception_mapping_utils.py:357\u001b[39m, in \u001b[36mexception_type\u001b[39m\u001b[34m(model, original_exception, custom_llm_provider, completion_kwargs, extra_kwargs)\u001b[39m\n\u001b[32m    352\u001b[39m \u001b[38;5;28;01melif\u001b[39;00m (\n\u001b[32m    353\u001b[39m     \u001b[33m\"\u001b[39m\u001b[33mThe api_key client option must be set either by passing api_key to the client or by setting the OPENAI_API_KEY environment variable\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    354\u001b[39m     \u001b[38;5;129;01min\u001b[39;00m error_str\n\u001b[32m    355\u001b[39m ):\n\u001b[32m    356\u001b[39m     exception_mapping_worked = \u001b[38;5;28;01mTrue\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m357\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m AuthenticationError(\n\u001b[32m    358\u001b[39m         message=\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mAuthenticationError: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mexception_provider\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m - \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mmessage\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m,\n\u001b[32m    359\u001b[39m         llm_provider=custom_llm_provider,\n\u001b[32m    360\u001b[39m         model=model,\n\u001b[32m    361\u001b[39m         response=\u001b[38;5;28mgetattr\u001b[39m(original_exception, \u001b[33m\"\u001b[39m\u001b[33mresponse\u001b[39m\u001b[33m\"\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m),\n\u001b[32m    362\u001b[39m         litellm_debug_info=extra_information,\n\u001b[32m    363\u001b[39m     )\n\u001b[32m    364\u001b[39m \u001b[38;5;28;01melif\u001b[39;00m \u001b[33m\"\u001b[39m\u001b[33mMistral API raised a streaming error\u001b[39m\u001b[33m\"\u001b[39m \u001b[38;5;129;01min\u001b[39;00m error_str:\n\u001b[32m    365\u001b[39m     exception_mapping_worked = \u001b[38;5;28;01mTrue\u001b[39;00m\n", "\u001b[31mAuthenticationError\u001b[39m: litellm.AuthenticationError: AuthenticationError: OpenAIException - The api_key client option must be set either by passing api_key to the client or by setting the OPENAI_API_KEY environment variable"]}], "source": ["result = crew.kickoff(inputs={\n", "    \"question\": \"Can I get a refund if I used the item once?\"\n", "})\n", "\n", "from pprint import pprint\n", "pprint(result.raw)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Text Knowledge Source"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from crewai.knowledge.source.text_file_knowledge_source import TextFileKnowledgeSource\n", "\n", "text_source = TextFileKnowledgeSource(\n", "    file_paths=[\"hr_policy.txt\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from crewai import Agent, Task, Crew, Process, LLM\n", "\n", "llm = LLM(model=\"gpt-4o\")\n", "\n", "hr_agent = Agent(\n", "    role=\"HR Policy Assistant\",\n", "    goal=\"Answer employee questions about HR policies.\",\n", "    backstory=\"You're a reliable HR knowledge assistant.\",\n", "    knowledge_sources=[text_source],\n", "    llm=llm\n", ")\n", "\n", "task = Task(\n", "    description=\"What is the leave policy for new employees?\",\n", "    expected_output=\"A clear summary of the leave policy.\",\n", "    agent=hr_agent\n", ")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["crew = Crew(\n", "    agents=[hr_agent],\n", "    tasks=[task],\n", "    process=Process.sequential,\n", "    verbose=True\n", ")\n", "\n", "result = crew.kickoff()\n", "pprint(result.raw)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## PDF source"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from crewai.knowledge.source.pdf_knowledge_source import PDFKnowledgeSource\n", "\n", "pdf_source = PDFKnowledgeSource(\n", "    file_paths=[\"meeting_notes.pdf\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["meeting_summarizer = Agent(\n", "    role=\"Meeting Note Summarizer\",\n", "    goal=\"Provide concise summaries of weekly meetings.\",\n", "    backstory=\"You help the team stay updated on discussions.\",\n", "    knowledge_sources=[pdf_source],\n", "    llm=llm\n", ")\n", "\n", "task = Task(\n", "    description=\"Summarize the key action items from last week's meeting.\",\n", "    expected_output=\"A bullet-point list of action items.\",\n", "    agent=meeting_summarizer\n", ")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["crew = Crew(\n", "    agents=[meeting_summarizer],\n", "    tasks=[task],\n", "    process=Process.sequential,\n", "    verbose=True\n", ")\n", "\n", "result = crew.kickoff()\n", "pprint(result.raw)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## CSV source"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from crewai.knowledge.source.csv_knowledge_source import CSVKnowledgeSource\n", "\n", "csv_source = CSVKnowledgeSource(\n", "    file_paths=[\"feedback.csv\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["feedback_analyst = Agent(\n", "    role=\"User Feedback Analyst\",\n", "    goal=\"Identify common themes in user feedback.\",\n", "    backstory=\"You specialize in converting raw feedback into insights.\",\n", "    knowledge_sources=[csv_source],\n", "    llm=llm\n", ")\n", "\n", "task = Task(\n", "    description=\"What are the three most common complaints users had last month?\",\n", "    expected_output=\"A short list of recurring issues.\",\n", "    agent=feedback_analyst\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["crew = Crew(\n", "    agents=[feedback_analyst],\n", "    tasks=[task],\n", "    process=Process.sequential,\n", "    verbose=True\n", ")\n", "\n", "result = crew.kickoff()\n", "pprint(result.raw)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## JSON source"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from crewai.knowledge.source.json_knowledge_source import JSONKnowledgeSource\n", "\n", "json_source = JSONKnowledgeSource(\n", "    file_paths=[\"company_info.json\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["company_expert = Agent(\n", "    role=\"Company Info Specialist\",\n", "    goal=\"Answer questions about company structure and data.\",\n", "    backstory=\"You are an internal data assistant for org-level queries.\",\n", "    # knowledge_sources=[json_source],\n", "    llm=llm\n", ")\n", "\n", "task = Task(\n", "    description=\"How many teams are working on the product and what are their names?\",\n", "    expected_output=\"A list of team names and their sizes.\",\n", "    agent=company_expert\n", ")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["crew = Crew(\n", "    agents=[company_expert],\n", "    tasks=[task],\n", "    process=Process.sequential,\n", "    verbose=True,\n", "    knowledge_sources=[json_source]\n", ")\n", "\n", "result = crew.kickoff()\n", "print(result)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Custom embedding model"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ollama_embedder = {\n", "    \"provider\": \"ollama\",\n", "    \"config\": {\n", "        \"model\": \"nomic-embed-text\",  # Must match or be compatible with Ollama's supported embedding models\n", "        \"api_url\": \"http://localhost:11434\"\n", "    }\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["from crewai import Agent, LLM\n", "from dotenv import load_dotenv\n", "import os\n", "load_dotenv()  # Load environment variables from .env file\n", "#os.environ[\"OPENAI_API_KEY\"] = os.getenv(\"GEMINI_API_KEY\")\n", "\n", "\n", "# llm = LLM(model=\"gemini/gemini-2.0-flash\", verbose=True, temperature=0.5,\n", "#           api_key=os.getenv(\"GEMINI_API_KEY\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["openrouter_llm = LLM(\n", "    model='openrouter/qwen/qwq-32b:free',\n", "    api_key=os.environ[\"OPENROUTER_API_KEY\"],\n", "    temperature=0\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ollama_embedder={\n", "        \"provider\": \"google\",\n", "        \"config\": {\n", "            \"api_key\": os.getenv(\"GEMINI_API_KEY\"),\n", "            \"model\" : \"models/text-embedding-004\"\n", "            #\"model\": \"models/embedding-001\"\n", "        }\n", "    }"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from crewai.knowledge.source.string_knowledge_source import StringKnowledgeSource\n", "\n", "# Internal onboarding FAQ\n", "faq_content = \"\"\"\n", "- You can access your email via portal.company.com using your employee credentials.\n", "- The standard work hours are from 9am to 5pm, Monday to Friday.\n", "- All reimbursement requests must be submitted by the 5th of the following month.\n", "- For any IT-related issues, contact <EMAIL>.\n", "\"\"\"\n", "\n", "# Create a string knowledge source\n", "faq_knowledge = StringKnowledgeSource(content=faq_content, embedder=ollama_embedder)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from crewai import Agent\n", "\n", "hr_faq_agent = Agent(\n", "    role=\"HR Assistant\",\n", "    goal=\"Answer onboarding-related questions for new hires.\",\n", "    backstory=\"You are a helpful assistant who knows everything about internal policies and onboarding processes.\",\n", "    allow_delegation=False,\n", "    knowledge_sources=[faq_knowledge],\n", "    verbose=True,\n", "    embedder=ollama_embedder\n", ")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from crewai import Task\n", "\n", "task = Task(\n", "    description=\"Answer this onboarding question: {question}\",\n", "    expected_output=\"A short, accurate answer based on internal HR documentation.\",\n", "    agent=hr_faq_agent,\n", "    embedder=ollama_embedder\n", ")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080\">╭──────────────────────────────────────────── Crew Execution Started ─────────────────────────────────────────────╮</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080\">│</span>                                                                                                                 <span style=\"color: #008080; text-decoration-color: #008080\">│</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080\">│</span>  <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">Crew Execution Started</span>                                                                                         <span style=\"color: #008080; text-decoration-color: #008080\">│</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080\">│</span>  <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">Name: </span><span style=\"color: #008080; text-decoration-color: #008080\">crew</span>                                                                                                     <span style=\"color: #008080; text-decoration-color: #008080\">│</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080\">│</span>  <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">ID: </span><span style=\"color: #008080; text-decoration-color: #008080\">5e6a30f1-7a2a-4ec6-933c-ea64d4aebb2a</span>                                                                       <span style=\"color: #008080; text-decoration-color: #008080\">│</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080\">│</span>                                                                                                                 <span style=\"color: #008080; text-decoration-color: #008080\">│</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080\">│</span>                                                                                                                 <span style=\"color: #008080; text-decoration-color: #008080\">│</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[36m╭─\u001b[0m\u001b[36m───────────────────────────────────────────\u001b[0m\u001b[36m Crew Execution Started \u001b[0m\u001b[36m────────────────────────────────────────────\u001b[0m\u001b[36m─╮\u001b[0m\n", "\u001b[36m│\u001b[0m                                                                                                                 \u001b[36m│\u001b[0m\n", "\u001b[36m│\u001b[0m  \u001b[1;36mCrew Execution Started\u001b[0m                                                                                         \u001b[36m│\u001b[0m\n", "\u001b[36m│\u001b[0m  \u001b[37mName: \u001b[0m\u001b[36mcrew\u001b[0m                                                                                                     \u001b[36m│\u001b[0m\n", "\u001b[36m│\u001b[0m  \u001b[37mID: \u001b[0m\u001b[36m5e6a30f1-7a2a-4ec6-933c-ea64d4aebb2a\u001b[0m                                                                       \u001b[36m│\u001b[0m\n", "\u001b[36m│\u001b[0m                                                                                                                 \u001b[36m│\u001b[0m\n", "\u001b[36m│\u001b[0m                                                                                                                 \u001b[36m│\u001b[0m\n", "\u001b[36m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">\n", "</pre>\n"], "text/plain": ["\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">🚀 Crew: crew</span>\n", "└── <span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">📋 Task: 7fe8306e-d29e-4626-8b1a-06494e846287</span>\n", "    <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">   Status: </span><span style=\"color: #bfbf7f; text-decoration-color: #bfbf7f\">Executing Task...</span>\n", "</pre>\n"], "text/plain": ["\u001b[1;36m🚀 Crew: \u001b[0m\u001b[1;36mcrew\u001b[0m\n", "└── \u001b[1;33m📋 Task: 7fe8306e-d29e-4626-8b1a-06494e846287\u001b[0m\n", "    \u001b[37m   Status: \u001b[0m\u001b[2;33mExecuting Task...\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">\n", "</pre>\n"], "text/plain": ["\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">🚀 Crew: crew</span>\n", "└── <span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">📋 Task: 7fe8306e-d29e-4626-8b1a-06494e846287</span>\n", "    <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">   Status: </span><span style=\"color: #bfbf7f; text-decoration-color: #bfbf7f\">Executing Task...</span>\n", "    └── <span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">🤖 Agent: </span><span style=\"color: #008000; text-decoration-color: #008000\">HR Assistant</span>\n", "        <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">    Status: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">In Progress</span>\n", "</pre>\n"], "text/plain": ["\u001b[1;36m🚀 Crew: \u001b[0m\u001b[1;36mcrew\u001b[0m\n", "└── \u001b[1;33m📋 Task: 7fe8306e-d29e-4626-8b1a-06494e846287\u001b[0m\n", "    \u001b[37m   Status: \u001b[0m\u001b[2;33mExecuting Task...\u001b[0m\n", "    └── \u001b[1;32m🤖 Agent: \u001b[0m\u001b[32mHR Assistant\u001b[0m\n", "        \u001b[37m    Status: \u001b[0m\u001b[1;32mIn Progress\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">\n", "</pre>\n"], "text/plain": ["\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m\u001b[95m# Agent:\u001b[00m \u001b[1m\u001b[92mHR Assistant\u001b[00m\n", "\u001b[95m## Task:\u001b[00m \u001b[92mAnswer this onboarding question: What are the working hours and how do I get reimbursed?\u001b[00m\n", "\n", "\n", "\u001b[1m\u001b[95m# Agent:\u001b[00m \u001b[1m\u001b[92mHR Assistant\u001b[00m\n", "\u001b[95m## Final Answer:\u001b[00m \u001b[92m\n", "The standard work hours are from 9am to 5pm, Monday to Friday. To get reimbursed for any expenses, you must submit your reimbursement requests by the 5th of the following month. If you encounter any IT-related issues while accessing the reimbursement system, <NAME_EMAIL> for assistance. You can access your email and further resources via portal.company.com using your employee credentials.\u001b[00m\n", "\n", "\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">🚀 Crew: crew</span>\n", "└── <span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">📋 Task: 7fe8306e-d29e-4626-8b1a-06494e846287</span>\n", "    <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">   Status: </span><span style=\"color: #bfbf7f; text-decoration-color: #bfbf7f\">Executing Task...</span>\n", "    └── <span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">🤖 Agent: </span><span style=\"color: #008000; text-decoration-color: #008000\">HR Assistant</span>\n", "        <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">    Status: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">✅ Completed</span>\n", "</pre>\n"], "text/plain": ["\u001b[1;36m🚀 Crew: \u001b[0m\u001b[1;36mcrew\u001b[0m\n", "└── \u001b[1;33m📋 Task: 7fe8306e-d29e-4626-8b1a-06494e846287\u001b[0m\n", "    \u001b[37m   Status: \u001b[0m\u001b[2;33mExecuting Task...\u001b[0m\n", "    └── \u001b[1;32m🤖 Agent: \u001b[0m\u001b[32mHR Assistant\u001b[0m\n", "        \u001b[37m    Status: \u001b[0m\u001b[1;32m✅ Completed\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">\n", "</pre>\n"], "text/plain": ["\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">🚀 Crew: crew</span>\n", "└── <span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">📋 Task: 7fe8306e-d29e-4626-8b1a-06494e846287</span>\n", "    <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">   Assigned to: </span><span style=\"color: #008000; text-decoration-color: #008000\">HR Assistant</span>\n", "    <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">   Status: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">✅ Completed</span>\n", "    └── <span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">🤖 Agent: </span><span style=\"color: #008000; text-decoration-color: #008000\">HR Assistant</span>\n", "        <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">    Status: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">✅ Completed</span>\n", "</pre>\n"], "text/plain": ["\u001b[1;36m🚀 Crew: \u001b[0m\u001b[1;36mcrew\u001b[0m\n", "└── \u001b[1;32m📋 Task: 7fe8306e-d29e-4626-8b1a-06494e846287\u001b[0m\n", "    \u001b[37m   Assigned to: \u001b[0m\u001b[32mHR Assistant\u001b[0m\n", "    \u001b[37m   Status: \u001b[0m\u001b[1;32m✅ Completed\u001b[0m\n", "    └── \u001b[1;32m🤖 Agent: \u001b[0m\u001b[32mHR Assistant\u001b[0m\n", "        \u001b[37m    Status: \u001b[0m\u001b[1;32m✅ Completed\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000\">╭──────────────────────────────────────────────── Task Completion ────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│</span>                                                                                                                 <span style=\"color: #008000; text-decoration-color: #008000\">│</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│</span>  <span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">Task Completed</span>                                                                                                 <span style=\"color: #008000; text-decoration-color: #008000\">│</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│</span>  <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">Name: </span><span style=\"color: #008000; text-decoration-color: #008000\">7fe8306e-d29e-4626-8b1a-06494e846287</span>                                                                     <span style=\"color: #008000; text-decoration-color: #008000\">│</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│</span>  <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">Agent: </span><span style=\"color: #008000; text-decoration-color: #008000\">HR Assistant</span>                                                                                            <span style=\"color: #008000; text-decoration-color: #008000\">│</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│</span>                                                                                                                 <span style=\"color: #008000; text-decoration-color: #008000\">│</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│</span>                                                                                                                 <span style=\"color: #008000; text-decoration-color: #008000\">│</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[32m╭─\u001b[0m\u001b[32m───────────────────────────────────────────────\u001b[0m\u001b[32m Task Completion \u001b[0m\u001b[32m───────────────────────────────────────────────\u001b[0m\u001b[32m─╮\u001b[0m\n", "\u001b[32m│\u001b[0m                                                                                                                 \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m  \u001b[1;32mTask Completed\u001b[0m                                                                                                 \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m  \u001b[37mName: \u001b[0m\u001b[32m7fe8306e-d29e-4626-8b1a-06494e846287\u001b[0m                                                                     \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m  \u001b[37mAgent: \u001b[0m\u001b[32mHR Assistant\u001b[0m                                                                                            \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m                                                                                                                 \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m                                                                                                                 \u001b[32m│\u001b[0m\n", "\u001b[32m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">\n", "</pre>\n"], "text/plain": ["\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000\">╭──────────────────────────────────────────────── Crew Completion ────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│</span>                                                                                                                 <span style=\"color: #008000; text-decoration-color: #008000\">│</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│</span>  <span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">Crew Execution Completed</span>                                                                                       <span style=\"color: #008000; text-decoration-color: #008000\">│</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│</span>  <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">Name: </span><span style=\"color: #008000; text-decoration-color: #008000\">crew</span>                                                                                                     <span style=\"color: #008000; text-decoration-color: #008000\">│</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│</span>  <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">ID: </span><span style=\"color: #008000; text-decoration-color: #008000\">5e6a30f1-7a2a-4ec6-933c-ea64d4aebb2a</span>                                                                       <span style=\"color: #008000; text-decoration-color: #008000\">│</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│</span>                                                                                                                 <span style=\"color: #008000; text-decoration-color: #008000\">│</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│</span>                                                                                                                 <span style=\"color: #008000; text-decoration-color: #008000\">│</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[32m╭─\u001b[0m\u001b[32m───────────────────────────────────────────────\u001b[0m\u001b[32m Crew Completion \u001b[0m\u001b[32m───────────────────────────────────────────────\u001b[0m\u001b[32m─╮\u001b[0m\n", "\u001b[32m│\u001b[0m                                                                                                                 \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m  \u001b[1;32mCrew Execution Completed\u001b[0m                                                                                       \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m  \u001b[37mName: \u001b[0m\u001b[32mcrew\u001b[0m                                                                                                     \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m  \u001b[37mID: \u001b[0m\u001b[32m5e6a30f1-7a2a-4ec6-933c-ea64d4aebb2a\u001b[0m                                                                       \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m                                                                                                                 \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m                                                                                                                 \u001b[32m│\u001b[0m\n", "\u001b[32m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">\n", "</pre>\n"], "text/plain": ["\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["('The standard work hours are from 9am to 5pm, Monday to Friday. To get '\n", " 'reimbursed for any expenses, you must submit your reimbursement requests by '\n", " 'the 5th of the following month. If you encounter any IT-related issues while '\n", " 'accessing the reimbursement system, <NAME_EMAIL> for '\n", " 'assistance. You can access your email and further resources via '\n", " 'portal.company.com using your employee credentials.')\n"]}], "source": ["from crewai import Crew, Process\n", "\n", "crew = Crew(\n", "    agents=[hr_faq_agent],\n", "    tasks=[task],\n", "    #knowledge_sources=[faq_knowledge],\n", "    embedder=ollama_embedder,\n", "    process=Process.sequential,\n", "    verbose=True\n", ")\n", "\n", "result = crew.kickoff(inputs={\n", "    \"question\": \"What are the working hours and how do I get reimbursed?\"\n", "})\n", "\n", "from pprint import pprint\n", "pprint(result.raw)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Custom knowledge source"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from crewai.knowledge.source.base_knowledge_source import BaseKnowledgeSource\n", "from typing import Dict, Any\n", "from pydantic import Field\n", "import requests\n", "\n", "class WeatherKnowledgeSource(BaseKnowledgeSource):\n", "    \"\"\"Knowledge source that fetches weather data from an external API.\"\"\"\n", "\n", "    city: str = Field(description=\"City for which weather should be fetched\")\n", "\n", "    def load_content(self) -> Dict[Any, str]:\n", "        try:\n", "            print(f\"Fetching weather for {self.city}...\")\n", "\n", "            # Open-Meteo API (no key needed for basic data)\n", "            endpoint = \"https://api.open-meteo.com/v1/forecast\"\n", "            params = {\n", "                \"latitude\": 37.77,  # San Francisco by default\n", "                \"longitude\": -122.42,\n", "                \"current_weather\": True\n", "            }\n", "\n", "            response = requests.get(endpoint, params=params)\n", "            response.raise_for_status()\n", "\n", "            weather_data = response.json().get(\"current_weather\", {})\n", "            formatted = self.validate_content(weather_data)\n", "            return {self.city: formatted}\n", "\n", "        except Exception as e:\n", "            raise ValueError(f\"Failed to fetch weather data: {str(e)}\")\n", "\n", "    def validate_content(self, data: dict) -> str:\n", "        if not data:\n", "            return \"No weather data available.\"\n", "\n", "        return (\n", "            f\"Current weather in {self.city}:\\n\"\n", "            f\"- Temperature: {data.get('temperature')}°C\\n\"\n", "            f\"- Wind Speed: {data.get('windspeed')} km/h\\n\"\n", "            f\"- Weather Code: {data.get('weathercode')}\\n\"\n", "            f\"- Time: {data.get('time')}\"\n", "        )\n", "\n", "    def add(self) -> None:\n", "        \"\"\"Process and chunk the content.\"\"\"\n", "        content = self.load_content()\n", "        for _, text in content.items():\n", "            chunks = self._chunk_text(text)\n", "            self.chunks.extend(chunks)\n", "        self._save_documents()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from crewai import Agent, LLM\n", "\n", "weather_knowledge = WeatherKnowledgeSource(city=\"San Francisco\")\n", "\n", "weather_agent = Agent(\n", "    role=\"Weather Reporter\",\n", "    goal=\"Answer questions about the current weather forecast.\",\n", "    backstory=\"You are a friendly meteorologist who provides real-time weather updates.\",\n", "    knowledge_sources=[weather_knowledge],\n", "    llm=LLM(model=\"gpt-4o\", temperature=0.0),\n", "    verbose=True\n", ")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from crewai import Task, Crew, Process\n", "\n", "task = Task(\n", "    description=\"What is the current temperature and wind speed in San Francisco?\",\n", "    expected_output=\"A concise weather summary for San Francisco.\",\n", "    agent=weather_agent\n", ")\n", "\n", "crew = Crew(\n", "    agents=[weather_agent],\n", "    tasks=[task],\n", "    process=Process.sequential,\n", "    verbose=True\n", ")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result = crew.kickoff()\n", "print(result)\n"]}], "metadata": {"kernelspec": {"display_name": "crewai", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.0"}}, "nbformat": 4, "nbformat_minor": 2}