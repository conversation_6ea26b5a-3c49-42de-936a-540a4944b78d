# 🎬 Advanced AI Voice-Over System (Streamlit)

An enhanced AI-powered voice-over system built with Streamlit that extends the existing Azure AI voice-over functionality with advanced timeline editing, audio polishing, and synchronized preview capabilities.

## ✨ Features

### Core Functionality
- **🎬 Video Upload & Processing**: Support for multiple video formats with URL and file upload
- **🎵 Audio Timeline Extraction**: Precise timestamp tracking and segment creation
- **✨ AI Audio Polishing**: Advanced noise reduction, normalization, and speech enhancement
- **📝 Timeline-Based Transcript Editor**: Interactive editing with real-time synchronization
- **🎥 Synchronized Preview**: Frame-accurate video/audio playback with no lag
- **📤 Multi-Format Export**: Export in various formats with quality settings

### Advanced Features
- **Interactive Timeline**: Visual timeline with drag-and-drop editing
- **Real-time Waveform Display**: Audio visualization for precise editing
- **AI-Powered Enhancement**: Noise reduction, speech clarity, and audio normalization
- **Segment Management**: Split, merge, and edit audio segments
- **Project Management**: Save and load projects for later editing
- **Azure Integration**: Seamless integration with existing Azure AI services

## 🏗️ Architecture

```
AI_Voice_Over_Streamlit/
├── app.py                          # Main Streamlit application
├── core/                           # Core processing modules
│   ├── enhanced_audio_processor.py # Audio processing with timestamps
│   ├── ai_audio_polisher.py       # AI audio enhancement
│   ├── transcript_timeline.py     # Timeline management
│   └── sync_manager.py            # Video/audio synchronization
├── components/                     # Streamlit UI components
│   ├── timeline_editor.py         # Interactive timeline
│   ├── audio_player.py           # Custom audio player
│   └── video_player.py           # Video player component
├── utils/                         # Utility functions
├── config.py                      # Configuration settings
└── requirements.txt               # Dependencies
```

## 🚀 Quick Start

### Prerequisites
- Python 3.8 or higher
- Azure Speech Services account
- Azure Storage account
- FFmpeg installed on system

### Installation

1. **Clone or navigate to the project directory**:
   ```bash
   cd AI_Voice_Over_Streamlit
   ```

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure Azure credentials**:
   - The system automatically imports configuration from the existing `AI_Voice_Over_Azure` system
   - Ensure your Azure credentials are properly configured in the parent Azure system

4. **Run the Streamlit application**:
   ```bash
   streamlit run app.py
   ```

5. **Open your browser** and navigate to `http://localhost:8501`

## 📋 Usage Guide

### Step 1: Video Upload
- Upload a video file or provide a video URL
- Select your preferred AI voice from available Azure Neural Voices
- Click "Start Processing" to begin

### Step 2: Audio Timeline Extraction
- The system extracts audio with precise timestamps
- Audio is automatically segmented based on speech detection
- Timeline visualization shows all segments

### Step 3: AI Audio Polishing
- Configure enhancement settings:
  - **Noise Reduction**: Remove background noise (0-100%)
  - **Audio Normalization**: Standardize volume levels
  - **Speech Enhancement**: Improve speech clarity
  - **Dynamic Range Compression**: Balance audio levels
- Preview original vs. enhanced audio
- Apply polishing with one click

### Step 4: Transcript Editing
- View interactive timeline with audio segments
- Edit transcript text for each segment
- Use timeline scrubber for precise navigation
- Real-time synchronization with audio

### Step 5: Preview & Synchronization
- Preview synchronized video and audio
- Adjust sync offset if needed
- Ensure perfect alignment between video and audio

### Step 6: Export
- Choose output format (MP4, AVI, MOV)
- Select quality settings (High, Medium, Low)
- Export final video with AI voice-over

## 🔧 Configuration

### Audio Processing Settings
```python
AUDIO_SAMPLE_RATE = 16000  # Standard for speech
MIN_SEGMENT_LENGTH = 1.0   # Minimum segment duration
SILENCE_THRESHOLD = 0.01   # Silence detection threshold
```

### Enhancement Settings
```python
DEFAULT_ENHANCEMENT_SETTINGS = {
    'noise_reduction': 50,
    'normalize_audio': True,
    'enhance_speech': True,
    'apply_eq': True,
    'dynamic_range_compression': True
}
```

### Export Quality
```python
QUALITY_SETTINGS = {
    'high': {'video_bitrate': '5000k', 'audio_bitrate': '192k'},
    'medium': {'video_bitrate': '2500k', 'audio_bitrate': '128k'},
    'low': {'video_bitrate': '1000k', 'audio_bitrate': '96k'}
}
```

## 🎯 Key Improvements Over Basic System

### 1. Timeline-Based Editing
- **Before**: Simple audio replacement
- **After**: Precise segment-by-segment editing with visual timeline

### 2. AI Audio Enhancement
- **Before**: Basic Azure TTS
- **After**: Advanced noise reduction, normalization, and speech enhancement

### 3. User Interface
- **Before**: Command-line interface
- **After**: Interactive web-based interface with real-time preview

### 4. Synchronization
- **Before**: Basic audio replacement
- **After**: Frame-accurate synchronization with drift correction

### 5. Project Management
- **Before**: Single-use processing
- **After**: Save/load projects, iterative editing

## 🔄 Integration with Existing System

This Streamlit application seamlessly integrates with your existing Azure AI voice-over system:

- **Reuses Azure Configuration**: Imports settings from `AI_Voice_Over_Azure/config.py`
- **Extends Functionality**: Builds upon `AzureVoiceConverter` class
- **Preserves Compatibility**: Existing system remains unchanged
- **Shared Resources**: Uses same Azure Speech Services and Storage

## 🛠️ Technical Details

### Audio Processing Pipeline
1. **Extraction**: FFmpeg-based audio extraction with metadata
2. **Segmentation**: Energy-based speech detection and segmentation
3. **Enhancement**: Multi-stage AI audio polishing
4. **Transcription**: Azure Speech Services with timestamp alignment
5. **Synthesis**: Enhanced Azure TTS with quality optimization
6. **Synchronization**: Frame-accurate video/audio alignment

### Real-time Features
- **Live Preview**: Instant audio/video synchronization
- **Interactive Timeline**: Drag-and-drop segment editing
- **Waveform Display**: Visual audio representation
- **Auto-save**: Automatic project state preservation

## 📊 Performance Optimizations

- **Chunked Processing**: Large files processed in segments
- **Caching**: Intelligent caching of processed audio
- **Parallel Processing**: Multi-threaded audio enhancement
- **Memory Management**: Efficient handling of large audio files

## 🔍 Troubleshooting

### Common Issues

1. **Azure Connection Errors**:
   - Verify Azure credentials in parent system
   - Check network connectivity
   - Ensure sufficient Azure credits

2. **Audio Processing Errors**:
   - Install FFmpeg system-wide
   - Check file format compatibility
   - Verify sufficient disk space

3. **Timeline Sync Issues**:
   - Adjust sync offset in preview
   - Check audio sample rate consistency
   - Verify video frame rate

### Performance Tips

- Use shorter video segments for faster processing
- Enable caching for repeated operations
- Close unused browser tabs to free memory
- Use SSD storage for better I/O performance

## 🚀 Future Enhancements

- **Batch Processing**: Process multiple videos simultaneously
- **Cloud Deployment**: Deploy on cloud platforms
- **Advanced AI**: Integration with latest AI models
- **Collaboration**: Multi-user editing capabilities
- **API Integration**: RESTful API for programmatic access

## 📝 License

This project extends the existing Azure AI Voice-Over system and follows the same licensing terms.

## 🤝 Contributing

Contributions are welcome! Please ensure compatibility with the existing Azure system and follow the established code structure.

---

**Note**: This system is designed to work alongside your existing `AI_Voice_Over_Azure` system without any modifications to the original code.
