#!/usr/bin/env python3
"""
Advanced AI Voice-Over System - Streamlit Application
Main entry point for the enhanced voice-over system with timeline editing
"""

import streamlit as st
import os
import sys
import numpy as np
from pathlib import Path

# Add the parent directory to path to import from existing Azure system
parent_dir = Path(__file__).parent.parent
sys.path.append(str(parent_dir / "AI_Voice_Over_Azure"))

# Import existing Azure functionality
try:
    from azure_voice_converter import AzureVoiceConverter
    from config import AZURE_VOICE_OPTIONS
except ImportError as e:
    st.error(f"Could not import Azure modules: {e}")
    st.stop()

# Configure Streamlit page
st.set_page_config(
    page_title="Advanced AI Voice-Over System",
    page_icon="🎬",
    layout="wide",
    initial_sidebar_state="expanded"
)

def initialize_session_state():
    """Initialize session state variables"""
    if 'project_data' not in st.session_state:
        st.session_state.project_data = {
            'video_url': None,
            'video_file': None,
            'audio_segments': [],
            'transcript_data': {},
            'timeline_data': {},
            'polished_audio': None,
            'final_video': None,
            'current_step': 1
        }
    
    if 'azure_converter' not in st.session_state:
        try:
            st.session_state.azure_converter = AzureVoiceConverter()
        except Exception as e:
            st.error(f"Failed to initialize Azure converter: {e}")
            st.stop()

def main():
    """Main application function"""
    initialize_session_state()
    
    # Sidebar navigation
    st.sidebar.title("🎬 Advanced AI Voice-Over")
    st.sidebar.markdown("---")
    
    # Progress indicator
    steps = [
        "🎬 Video Upload",
        "🎵 Audio Timeline", 
        "✨ AI Polish",
        "📝 Transcript Editor",
        "🎥 Preview & Sync",
        "📤 Export"
    ]
    
    current_step = st.session_state.project_data.get('current_step', 1)
    
    st.sidebar.markdown("### Progress")
    for i, step in enumerate(steps, 1):
        if i < current_step:
            st.sidebar.markdown(f"✅ {step}")
        elif i == current_step:
            st.sidebar.markdown(f"🔄 **{step}**")
        else:
            st.sidebar.markdown(f"⏳ {step}")
    
    st.sidebar.markdown("---")
    
    # Project info
    if st.session_state.project_data['video_url']:
        st.sidebar.markdown("### Current Project")
        st.sidebar.text(f"Video: {st.session_state.project_data['video_url'][:50]}...")
    
    # Main content area
    st.title("🎬 Advanced AI Voice-Over System")
    st.markdown("Transform your videos with AI-powered voice-over and timeline editing")
    
    # Navigation tabs
    tab1, tab2, tab3, tab4, tab5, tab6 = st.tabs([
        "🎬 Upload", "🎵 Timeline", "✨ Polish", "📝 Edit", "🎥 Preview", "📤 Export"
    ])
    
    with tab1:
        show_video_upload()
    
    with tab2:
        show_audio_timeline()
    
    with tab3:
        show_ai_polish()
    
    with tab4:
        show_transcript_editor()
    
    with tab5:
        show_preview_sync()
    
    with tab6:
        show_export()

def show_video_upload():
    """Video upload and initial processing"""
    st.header("🎬 Video Upload & Processing")
    
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.subheader("Upload Video")
        
        # Video URL input
        video_url = st.text_input(
            "Video URL",
            value=st.session_state.project_data.get('video_url', ''),
            placeholder="https://example.com/video.mp4"
        )
        
        # File upload
        uploaded_file = st.file_uploader(
            "Or upload a video file",
            type=['mp4', 'avi', 'mov', 'mkv', 'webm'],
            help="Supported formats: MP4, AVI, MOV, MKV, WEBM"
        )
        
        # Voice selection
        st.subheader("AI Voice Selection")
        voice_key = st.selectbox(
            "Choose AI Voice",
            options=list(AZURE_VOICE_OPTIONS.keys()),
            format_func=lambda x: f"{x.title()} - {AZURE_VOICE_OPTIONS[x]}",
            index=0
        )
        
        if st.button("🚀 Start Processing", type="primary"):
            if video_url or uploaded_file:
                with st.spinner("Processing video..."):
                    # Store project data
                    st.session_state.project_data['video_url'] = video_url
                    st.session_state.project_data['video_file'] = uploaded_file
                    st.session_state.project_data['selected_voice'] = AZURE_VOICE_OPTIONS[voice_key]
                    st.session_state.project_data['current_step'] = 2
                    
                    st.success("✅ Video uploaded successfully!")
                    st.rerun()
            else:
                st.error("Please provide a video URL or upload a file")
    
    with col2:
        st.subheader("Available Voices")
        for key, voice_name in AZURE_VOICE_OPTIONS.items():
            st.text(f"🎤 {key.title()}: {voice_name}")

def show_audio_timeline():
    """Audio extraction with timeline"""
    st.header("🎵 Audio Timeline Extraction")

    if not st.session_state.project_data.get('video_url') and not st.session_state.project_data.get('video_file'):
        st.warning("Please upload a video first")
        return

    # Initialize audio processor if not exists
    if 'audio_processor' not in st.session_state:
        from core.enhanced_audio_processor import EnhancedAudioProcessor
        st.session_state.audio_processor = EnhancedAudioProcessor(st.session_state.azure_converter)

    col1, col2 = st.columns([2, 1])

    with col1:
        st.subheader("Audio Processing Settings")

        # Segmentation settings
        min_segment_length = st.slider("Minimum Segment Length (seconds)", 0.5, 5.0, 1.0, 0.1)
        silence_threshold = st.slider("Silence Threshold", 0.001, 0.1, 0.01, 0.001)

        # Processing options
        extract_with_transcription = st.checkbox("Include Transcription", True)

        if st.button("🎵 Extract Audio Timeline", type="primary"):
            with st.spinner("Extracting audio with precise timestamps..."):
                try:
                    # Create temporary directory for this project
                    import tempfile
                    import uuid
                    project_id = str(uuid.uuid4())[:8]
                    temp_dir = f"temp/project_{project_id}"
                    os.makedirs(temp_dir, exist_ok=True)

                    # Download video if URL provided
                    video_path = None
                    if st.session_state.project_data.get('video_url'):
                        video_path = f"{temp_dir}/input_video.mp4"
                        if st.session_state.azure_converter.download_video(
                            st.session_state.project_data['video_url'],
                            video_path
                        ):
                            st.success("✅ Video downloaded successfully")
                        else:
                            st.error("❌ Failed to download video")
                            return

                    # Extract audio with timestamps
                    if video_path:
                        result = st.session_state.audio_processor.extract_audio_with_timestamps(
                            video_path, temp_dir
                        )

                        # Store results in session state
                        st.session_state.project_data['audio_timeline'] = result
                        st.session_state.project_data['project_dir'] = temp_dir

                        # Transcribe if requested
                        if extract_with_transcription:
                            with st.spinner("Transcribing audio segments..."):
                                transcription = st.session_state.audio_processor.transcribe_segments_with_azure(temp_dir)
                                st.session_state.project_data['transcription'] = transcription

                        st.success(f"✅ Audio timeline extracted! Found {len(result['segments'])} segments")
                        st.session_state.project_data['current_step'] = 3
                        st.rerun()

                except Exception as e:
                    st.error(f"❌ Error extracting audio timeline: {str(e)}")

    with col2:
        st.subheader("Processing Info")
        st.info("📊 Audio will be processed with:")
        st.text(f"• Sample Rate: 16kHz")
        st.text(f"• Channels: Mono")
        st.text(f"• Format: WAV")
        st.text(f"• Min Segment: {min_segment_length}s")

    # Show existing timeline if available
    if st.session_state.project_data.get('audio_timeline'):
        st.subheader("📊 Extracted Timeline")
        timeline_data = st.session_state.project_data['audio_timeline']

        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("Total Duration", f"{timeline_data['total_duration']:.2f}s")
        with col2:
            st.metric("Segments Found", len(timeline_data['segments']))
        with col3:
            st.metric("Audio File", "✅ Ready")

        # Show segments preview
        if timeline_data['segments']:
            st.subheader("🎵 Audio Segments Preview")
            for i, segment in enumerate(timeline_data['segments'][:5]):  # Show first 5
                with st.expander(f"Segment {i+1}: {segment.start_time:.2f}s - {segment.end_time:.2f}s"):
                    st.text(f"Duration: {segment.duration:.2f} seconds")
                    st.text(f"Text: {segment.text}")

            if len(timeline_data['segments']) > 5:
                st.info(f"... and {len(timeline_data['segments']) - 5} more segments")

def show_ai_polish():
    """AI audio polishing"""
    st.header("✨ AI Audio Polishing")

    if st.session_state.project_data.get('current_step', 1) < 3:
        st.warning("Please complete audio timeline extraction first")
        return

    if not st.session_state.project_data.get('audio_timeline'):
        st.warning("No audio timeline data available")
        return

    # Initialize audio polisher if not exists
    if 'audio_polisher' not in st.session_state:
        from core.ai_audio_polisher import AudioPolisher
        st.session_state.audio_polisher = AudioPolisher()

    col1, col2 = st.columns(2)

    with col1:
        st.subheader("🎛️ Enhancement Settings")

        # Enhancement options
        noise_reduction = st.slider("Noise Reduction Strength", 0, 100, 50,
                                  help="Higher values remove more noise but may affect speech quality")

        audio_normalization = st.checkbox("Audio Normalization", True,
                                        help="Standardize volume levels across the audio")

        speech_enhancement = st.checkbox("Speech Clarity Enhancement", True,
                                       help="Enhance speech frequencies for better clarity")

        apply_eq = st.checkbox("Speech-Optimized EQ", True,
                             help="Apply equalization optimized for speech")

        dynamic_compression = st.checkbox("Dynamic Range Compression", True,
                                        help="Balance loud and quiet parts")

        remove_silence = st.checkbox("Remove Excessive Silence", False,
                                   help="Trim long silent periods")

        # Advanced settings
        with st.expander("🔧 Advanced Settings"):
            compression_level = st.selectbox("Compression Level",
                                           ["Light", "Medium", "Heavy"],
                                           index=1)

            eq_preset = st.selectbox("EQ Preset",
                                   ["Speech", "Podcast", "Broadcast"],
                                   index=0)

    with col2:
        st.subheader("📊 Audio Preview")

        # Show original audio info
        timeline_data = st.session_state.project_data['audio_timeline']
        audio_path = timeline_data.get('audio_path')

        if audio_path and os.path.exists(audio_path):
            st.text("🎵 Original Audio")
            st.audio(audio_path, format='audio/wav')

            # Show audio metrics
            try:
                import librosa
                audio_data, sr = librosa.load(audio_path, sr=16000)
                duration = len(audio_data) / sr
                rms = np.sqrt(np.mean(audio_data ** 2))

                st.metric("Duration", f"{duration:.2f}s")
                st.metric("RMS Level", f"{rms:.4f}")
                st.metric("Sample Rate", f"{sr} Hz")
            except:
                st.text("Audio metrics unavailable")

        # Show enhanced audio if available
        if st.session_state.project_data.get('polished_audio_path'):
            st.text("✨ Enhanced Audio")
            st.audio(st.session_state.project_data['polished_audio_path'], format='audio/wav')

    # Enhancement controls
    st.subheader("🚀 Apply Enhancement")

    col1, col2, col3 = st.columns(3)

    with col1:
        if st.button("🎨 Polish Audio", type="primary"):
            if audio_path and os.path.exists(audio_path):
                with st.spinner("Applying AI audio enhancement..."):
                    try:
                        # Prepare enhancement settings
                        enhancement_settings = {
                            'noise_reduction': noise_reduction,
                            'normalize_audio': audio_normalization,
                            'enhance_speech': speech_enhancement,
                            'apply_eq': apply_eq,
                            'dynamic_range_compression': dynamic_compression,
                            'remove_silence': remove_silence
                        }

                        # Create output path
                        project_dir = st.session_state.project_data.get('project_dir', 'temp')
                        enhanced_audio_path = f"{project_dir}/enhanced_audio.wav"

                        # Apply polishing
                        result = st.session_state.audio_polisher.polish_audio(
                            audio_path,
                            enhanced_audio_path,
                            enhancement_settings
                        )

                        # Store results
                        st.session_state.project_data['polished_audio_path'] = enhanced_audio_path
                        st.session_state.project_data['enhancement_results'] = result
                        st.session_state.project_data['current_step'] = 4

                        st.success("✅ Audio polished successfully!")

                        # Show enhancement metrics
                        if result.get('metrics'):
                            metrics = result['metrics']
                            st.subheader("📈 Enhancement Metrics")

                            col1, col2, col3 = st.columns(3)
                            with col1:
                                st.metric("SNR Improvement",
                                        f"{metrics.get('snr_improvement_db', 0):.2f} dB")
                            with col2:
                                st.metric("Dynamic Range",
                                        f"{metrics.get('enhanced_dynamic_range', 0):.3f}")
                            with col3:
                                st.metric("Spectral Centroid",
                                        f"{metrics.get('enhanced_spectral_centroid', 0):.0f} Hz")

                        st.rerun()

                    except Exception as e:
                        st.error(f"❌ Error polishing audio: {str(e)}")
            else:
                st.error("No audio file available for polishing")

    with col2:
        if st.button("🔄 Reset Settings"):
            st.rerun()

    with col3:
        if st.button("⏭️ Skip Polish"):
            st.session_state.project_data['current_step'] = 4
            st.rerun()

    # Show enhancement log if available
    if st.session_state.project_data.get('enhancement_results'):
        results = st.session_state.project_data['enhancement_results']
        if results.get('enhancement_log'):
            with st.expander("📋 Enhancement Log"):
                for log_entry in results['enhancement_log']:
                    st.text(f"✓ {log_entry}")

def show_transcript_editor():
    """Timeline-based transcript editor"""
    st.header("📝 Timeline-Based Transcript Editor")

    if st.session_state.project_data.get('current_step', 1) < 4:
        st.warning("Please complete AI audio polishing first")
        return

    # Check if we have timeline data
    if not st.session_state.project_data.get('audio_timeline'):
        st.warning("No audio timeline data available")
        return

    # Initialize timeline editor
    from components.timeline_editor import TimelineEditor
    if 'timeline_editor' not in st.session_state:
        st.session_state.timeline_editor = TimelineEditor()

    timeline_data = st.session_state.project_data['audio_timeline']
    segments = timeline_data.get('segments', [])

    if not segments:
        st.warning("No audio segments available for editing")
        return

    # Convert segments to dictionary format for timeline editor
    segment_dicts = []
    for segment in segments:
        if hasattr(segment, 'to_dict'):
            segment_dicts.append(segment.to_dict())
        else:
            # Handle case where segments are already dictionaries
            segment_dicts.append(segment)

    # Get current time from session state
    current_time = st.session_state.get('current_playback_time', 0.0)
    total_duration = timeline_data.get('total_duration', 0)

    # Render timeline editor
    timeline_result = st.session_state.timeline_editor.render_timeline(
        segment_dicts, total_duration, current_time
    )

    # Update current time if changed
    if timeline_result.get('current_time') != current_time:
        st.session_state.current_playback_time = timeline_result['current_time']

    # Audio player section
    st.subheader("🎵 Audio Playback")

    col1, col2 = st.columns([3, 1])

    with col1:
        # Show current audio file
        audio_path = st.session_state.project_data.get('polished_audio_path')
        if not audio_path:
            audio_path = timeline_data.get('audio_path')

        if audio_path and os.path.exists(audio_path):
            st.audio(audio_path, format='audio/wav')
        else:
            st.warning("Audio file not available")

    with col2:
        st.subheader("⏱️ Playback Info")
        st.metric("Current Time", f"{current_time:.2f}s")
        st.metric("Total Duration", f"{total_duration:.2f}s")

        # Find current segment
        current_segment = None
        for i, segment in enumerate(segment_dicts):
            if segment['start_time'] <= current_time <= segment['end_time']:
                current_segment = i + 1
                break

        if current_segment:
            st.metric("Current Segment", current_segment)

    # Segment list for detailed editing
    st.subheader("📋 Segment Editor")

    selected_segment_index = st.session_state.timeline_editor.render_segment_list(segment_dicts)

    if selected_segment_index is not None and 0 <= selected_segment_index < len(segment_dicts):
        st.subheader(f"✏️ Edit Segment {selected_segment_index + 1}")

        segment = segment_dicts[selected_segment_index]

        col1, col2 = st.columns(2)

        with col1:
            st.text(f"⏰ Time: {segment['start_time']:.2f}s - {segment['end_time']:.2f}s")
            st.text(f"⏱️ Duration: {segment['duration']:.2f}s")

        with col2:
            st.text(f"🎯 Confidence: {segment.get('confidence', 0):.2f}")
            st.text(f"📝 Modified: {'Yes' if segment.get('is_modified', False) else 'No'}")

        # Text editing
        current_text = segment.get('text', '')
        new_text = st.text_area(
            "Edit transcript text",
            value=current_text,
            height=100,
            key=f"segment_edit_{selected_segment_index}"
        )

        col1, col2, col3 = st.columns(3)

        with col1:
            if st.button("💾 Save Segment", key=f"save_{selected_segment_index}"):
                if new_text != current_text:
                    # Update segment in timeline data
                    if hasattr(segments[selected_segment_index], 'text'):
                        segments[selected_segment_index].text = new_text
                        segments[selected_segment_index].is_modified = True
                    else:
                        segment_dicts[selected_segment_index]['text'] = new_text
                        segment_dicts[selected_segment_index]['is_modified'] = True

                    st.success("✅ Segment updated!")
                    st.rerun()

        with col2:
            if st.button("🎵 Play Segment", key=f"play_{selected_segment_index}"):
                # Set current time to segment start
                st.session_state.current_playback_time = segment['start_time']
                st.rerun()

        with col3:
            if st.button("🔄 Reset Text", key=f"reset_{selected_segment_index}"):
                st.rerun()

    # Global actions
    st.subheader("💾 Save & Continue")

    col1, col2, col3 = st.columns(3)

    with col1:
        if st.button("💾 Save All Changes", type="primary"):
            # Export timeline data
            project_dir = st.session_state.project_data.get('project_dir', 'temp')
            timeline_export_path = f"{project_dir}/edited_timeline.json"

            if st.session_state.audio_processor.export_timeline_data(timeline_export_path):
                st.session_state.project_data['edited_timeline_path'] = timeline_export_path
                st.session_state.project_data['current_step'] = 5
                st.success("✅ All changes saved!")
                st.rerun()
            else:
                st.error("❌ Failed to save changes")

    with col2:
        if st.button("📤 Export Timeline"):
            timeline_json = st.session_state.timeline_editor.export_timeline_data(segment_dicts)
            st.download_button(
                label="📥 Download Timeline JSON",
                data=timeline_json,
                file_name="timeline_data.json",
                mime="application/json"
            )

    with col3:
        if st.button("⏭️ Continue to Preview"):
            st.session_state.project_data['current_step'] = 5
            st.rerun()

def show_preview_sync():
    """Synchronized preview"""
    st.header("🎥 Preview & Synchronization")
    
    if st.session_state.project_data.get('current_step', 1) < 5:
        st.warning("Please complete transcript editing first")
        return
    
    st.info("🔄 Preview synchronized video and audio")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("Video Preview")
        st.text("Video player will appear here")
    
    with col2:
        st.subheader("Audio Waveform")
        st.text("Audio waveform will appear here")
    
    # Sync controls
    st.subheader("Synchronization Controls")
    sync_offset = st.slider("Audio Sync Offset (ms)", -1000, 1000, 0)
    
    if st.button("🔄 Generate Preview"):
        with st.spinner("Generating synchronized preview..."):
            st.success("✅ Preview generated!")
            st.session_state.project_data['current_step'] = 6

def show_export():
    """Export final video"""
    st.header("📤 Export Final Video")
    
    if st.session_state.project_data.get('current_step', 1) < 6:
        st.warning("Please complete preview and synchronization first")
        return
    
    st.subheader("Export Settings")
    
    col1, col2 = st.columns(2)
    
    with col1:
        output_format = st.selectbox("Output Format", ["MP4", "AVI", "MOV"])
        quality = st.selectbox("Quality", ["High", "Medium", "Low"])
    
    with col2:
        resolution = st.selectbox("Resolution", ["Original", "1080p", "720p", "480p"])
        audio_bitrate = st.selectbox("Audio Bitrate", ["192 kbps", "128 kbps", "96 kbps"])
    
    if st.button("🎬 Export Final Video", type="primary"):
        with st.spinner("Exporting final video..."):
            # Use existing Azure converter for final processing
            converter = st.session_state.azure_converter
            # Final export logic here
            st.success("✅ Video exported successfully!")
            st.balloons()

if __name__ == "__main__":
    main()
