{"cells": [{"cell_type": "markdown", "metadata": {"id": "614gfiIu4zZo"}, "source": ["# Using Opik with CrewAI\n", "\n", "This notebook showcases how to use Opik with CrewAI. [CrewAI](https://github.com/crewAIInc/crewAI) is a cutting-edge framework for orchestrating autonomous AI agents.\n", "> CrewAI enables you to create AI teams where each agent has specific roles, tools, and goals, working together to accomplish complex tasks.\n", "\n", "> Think of it as assembling your dream team - each member (agent) brings unique skills and expertise, collaborating seamlessly to achieve your objectives.\n", "\n", "For this guide we will use CrewAI's quickstart example."]}, {"cell_type": "markdown", "metadata": {"id": "ZgYvo1v24zZq"}, "source": ["## Creating an account on Comet.com\n", "\n", "[Comet](https://www.comet.com/site?from=llm&utm_source=opik&utm_medium=colab&utm_content=llamaindex&utm_campaign=opik) provides a hosted version of the Opik platform, [simply create an account](https://www.comet.com/signup?from=llm&=opik&utm_medium=colab&utm_content=llamaindex&utm_campaign=opik) and grab your API Key.\n", "\n", "> You can also run the Opik platform locally, see the [installation guide](https://www.comet.com/docs/opik/self-host/overview/?from=llm&utm_source=opik&utm_medium=colab&utm_content=llamaindex&utm_campaign=opik) for more information."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "sPSJc8uN4zZq", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "cdfb44be-e8f9-45a9-eb04-53aee0e624ea"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting crewai\n", "  Downloading crewai-0.114.0-py3-none-any.whl.metadata (33 kB)\n", "Collecting crewai-tools\n", "  Downloading crewai_tools-0.40.1-py3-none-any.whl.metadata (5.5 kB)\n", "Collecting opik\n", "  Downloading opik-1.7.9-py3-none-any.whl.metadata (24 kB)\n", "Collecting appdirs>=1.4.4 (from crewai)\n", "  Downloading appdirs-1.4.4-py2.py3-none-any.whl.metadata (9.0 kB)\n", "Collecting auth0-python>=4.7.1 (from crew<PERSON>)\n", "  Downloading auth0_python-4.9.0-py3-none-any.whl.metadata (9.0 kB)\n", "Requirement already satisfied: blinker>=1.9.0 in /usr/local/lib/python3.11/dist-packages (from crewai) (1.9.0)\n", "Collecting chromadb>=0.5.23 (from crewai)\n", "  Downloading chromadb-1.0.5-cp39-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (6.9 kB)\n", "Requirement already satisfied: click>=8.1.7 in /usr/local/lib/python3.11/dist-packages (from crewai) (8.1.8)\n", "Collecting instructor>=1.3.3 (from crewai)\n", "  Downloading instructor-1.7.9-py3-none-any.whl.metadata (22 kB)\n", "Collecting json-repair>=0.25.2 (from crewai)\n", "  Downloading json_repair-0.41.1-py3-none-any.whl.metadata (11 kB)\n", "Collecting json5>=0.10.0 (from <PERSON><PERSON>)\n", "  Downloading json5-0.12.0-py3-none-any.whl.metadata (36 kB)\n", "Collecting jsonref>=1.1.0 (from crew<PERSON>)\n", "  Downloading jsonref-1.1.0-py3-none-any.whl.metadata (2.7 kB)\n", "Collecting litellm==1.60.2 (from crew<PERSON>)\n", "  Downloading litellm-1.60.2-py3-none-any.whl.metadata (36 kB)\n", "Requirement already satisfied: openai>=1.13.3 in /usr/local/lib/python3.11/dist-packages (from crewai) (1.75.0)\n", "Requirement already satisfied: openpyxl>=3.1.5 in /usr/local/lib/python3.11/dist-packages (from crewai) (3.1.5)\n", "Requirement already satisfied: opentelemetry-api>=1.30.0 in /usr/local/lib/python3.11/dist-packages (from crewai) (1.32.1)\n", "Collecting opentelemetry-exporter-otlp-proto-http>=1.30.0 (from crewai)\n", "  Downloading opentelemetry_exporter_otlp_proto_http-1.32.1-py3-none-any.whl.metadata (2.4 kB)\n", "Requirement already satisfied: opentelemetry-sdk>=1.30.0 in /usr/local/lib/python3.11/dist-packages (from crewai) (1.32.1)\n", "Collecting pdfplumber>=0.11.4 (from crew<PERSON>)\n", "  Downloading pdfplumber-0.11.6-py3-none-any.whl.metadata (42 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m42.8/42.8 kB\u001b[0m \u001b[31m4.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: pydantic>=2.4.2 in /usr/local/lib/python3.11/dist-packages (from crewai) (2.11.3)\n", "Collecting python-dotenv>=1.0.0 (from crew<PERSON>)\n", "  Downloading python_dotenv-1.1.0-py3-none-any.whl.metadata (24 kB)\n", "Collecting pyvis>=0.3.2 (from <PERSON><PERSON>)\n", "  Downloading pyvis-0.3.2-py3-none-any.whl.metadata (1.7 kB)\n", "Requirement already satisfied: regex>=2024.9.11 in /usr/local/lib/python3.11/dist-packages (from crewai) (2024.11.6)\n", "Collecting tomli-w>=1.1.0 (from crew<PERSON>)\n", "  Downloading tomli_w-1.2.0-py3-none-any.whl.metadata (5.7 kB)\n", "Collecting tomli>=2.0.2 (from crew<PERSON>)\n", "  Downloading tomli-2.2.1-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (11 kB)\n", "Collecting uv>=0.4.25 (from crewai)\n", "  Downloading uv-0.6.14-py3-none-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (11 kB)\n", "Requirement already satisfied: aiohttp in /usr/local/lib/python3.11/dist-packages (from litellm==1.60.2->crewai) (3.11.15)\n", "Collecting httpx<0.28.0,>=0.23.0 (from litellm==1.60.2->crewai)\n", "  Downloading httpx-0.27.2-py3-none-any.whl.metadata (7.1 kB)\n", "Requirement already satisfied: importlib-metadata>=6.8.0 in /usr/local/lib/python3.11/dist-packages (from litellm==1.60.2->crewai) (8.6.1)\n", "Requirement already satisfied: jinja2<4.0.0,>=3.1.2 in /usr/local/lib/python3.11/dist-packages (from litellm==1.60.2->crewai) (3.1.6)\n", "Requirement already satisfied: jsonschema<5.0.0,>=4.22.0 in /usr/local/lib/python3.11/dist-packages (from litellm==1.60.2->crewai) (4.23.0)\n", "Collecting tiktoken>=0.7.0 (from litellm==1.60.2->crewai)\n", "  Downloading tiktoken-0.9.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (6.7 kB)\n", "Requirement already satisfied: tokenizers in /usr/local/lib/python3.11/dist-packages (from litellm==1.60.2->crewai) (0.21.1)\n", "Collecting docker>=7.1.0 (from crewai-tools)\n", "  Downloading docker-7.1.0-py3-none-any.whl.metadata (3.8 kB)\n", "Collecting embedchain>=0.1.114 (from crewai-tools)\n", "  Downloading embedchain-0.1.128-py3-none-any.whl.metadata (9.2 kB)\n", "Collecting lancedb>=0.5.4 (from crewai-tools)\n", "  Downloading lancedb-0.21.2-cp39-abi3-manylinux_2_28_x86_64.whl.metadata (4.2 kB)\n", "Collecting pyright>=1.1.350 (from crewai-tools)\n", "  Downloading pyright-1.1.399-py3-none-any.whl.metadata (6.6 kB)\n", "Collecting pytube>=15.0.0 (from crewai-tools)\n", "  Downloading pytube-15.0.0-py3-none-any.whl.metadata (5.0 kB)\n", "Requirement already satisfied: requests>=2.31.0 in /usr/local/lib/python3.11/dist-packages (from crewai-tools) (2.32.3)\n", "Collecting boto3-stubs>=1.34.110 (from boto3-stubs[bedrock-runtime]>=1.34.110->opik)\n", "  Downloading boto3_stubs-1.37.37-py3-none-any.whl.metadata (149 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m149.3/149.3 kB\u001b[0m \u001b[31m10.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting levenshtein<1.0.0 (from opik)\n", "  Downloading levenshtein-0.27.1-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (3.6 kB)\n", "Collecting pydantic-settings!=2.9.0,<3.0.0,>=2.0.0 (from opik)\n", "  Downloading pydantic_settings-2.9.1-py3-none-any.whl.metadata (3.8 kB)\n", "Requirement already satisfied: pytest in /usr/local/lib/python3.11/dist-packages (from opik) (8.3.5)\n", "Requirement already satisfied: rich in /usr/local/lib/python3.11/dist-packages (from opik) (13.9.4)\n", "Requirement already satisfied: sentry_sdk>=2.0.0 in /usr/local/lib/python3.11/dist-packages (from opik) (2.26.1)\n", "Requirement already satisfied: tenacity in /usr/local/lib/python3.11/dist-packages (from opik) (9.1.2)\n", "Requirement already satisfied: tqdm in /usr/local/lib/python3.11/dist-packages (from opik) (4.67.1)\n", "Collecting uuid6 (from opik)\n", "  Downloading uuid6-2024.7.10-py3-none-any.whl.metadata (8.6 kB)\n", "Requirement already satisfied: cryptography>=43.0.1 in /usr/local/lib/python3.11/dist-packages (from auth0-python>=4.7.1->crewai) (43.0.3)\n", "Requirement already satisfied: pyjwt>=2.8.0 in /usr/local/lib/python3.11/dist-packages (from auth0-python>=4.7.1->crewai) (2.10.1)\n", "Requirement already satisfied: urllib3>=2.2.3 in /usr/local/lib/python3.11/dist-packages (from auth0-python>=4.7.1->crewai) (2.3.0)\n", "Collecting botocore-stubs (from boto3-stubs>=1.34.110->boto3-stubs[bedrock-runtime]>=1.34.110->opik)\n", "  Downloading botocore_stubs-1.37.37-py3-none-any.whl.metadata (4.7 kB)\n", "Collecting types-s3transfer (from boto3-stubs>=1.34.110->boto3-stubs[bedrock-runtime]>=1.34.110->opik)\n", "  Downloading types_s3transfer-0.11.5-py3-none-any.whl.metadata (5.0 kB)\n", "Requirement already satisfied: typing-extensions>=4.1.0 in /usr/local/lib/python3.11/dist-packages (from boto3-stubs>=1.34.110->boto3-stubs[bedrock-runtime]>=1.34.110->opik) (4.13.2)\n", "Collecting mypy-boto3-bedrock-runtime<1.38.0,>=1.37.0 (from boto3-stubs[bedrock-runtime]>=1.34.110->opik)\n", "  Downloading mypy_boto3_bedrock_runtime-1.37.30-py3-none-any.whl.metadata (15 kB)\n", "Collecting build>=1.0.3 (from chromadb>=0.5.23->crewai)\n", "  Downloading build-1.2.2.post1-py3-none-any.whl.metadata (6.5 kB)\n", "Collecting chroma-hnswlib==0.7.6 (from chromadb>=0.5.23->crewai)\n", "  Downloading chroma_hnswlib-0.7.6-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (252 bytes)\n", "Collecting fastapi==0.115.9 (from chromadb>=0.5.23->crewai)\n", "  Downloading fastapi-0.115.9-py3-none-any.whl.metadata (27 kB)\n", "Collecting uvicorn>=0.18.3 (from uvicorn[standard]>=0.18.3->chromadb>=0.5.23-><PERSON>ai)\n", "  Downloading uvicorn-0.34.2-py3-none-any.whl.metadata (6.5 kB)\n", "Requirement already satisfied: numpy>=1.22.5 in /usr/local/lib/python3.11/dist-packages (from chromadb>=0.5.23->crewai) (2.0.2)\n", "Collecting posthog>=2.4.0 (from chromadb>=0.5.23->crewai)\n", "  Downloading posthog-3.25.0-py2.py3-none-any.whl.metadata (3.0 kB)\n", "Collecting onnxruntime>=1.14.1 (from chromadb>=0.5.23->crewai)\n", "  Downloading onnxruntime-1.21.1-cp311-cp311-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl.metadata (4.5 kB)\n", "Collecting opentelemetry-exporter-otlp-proto-grpc>=1.2.0 (from chromadb>=0.5.23->crewai)\n", "  Downloading opentelemetry_exporter_otlp_proto_grpc-1.32.1-py3-none-any.whl.metadata (2.5 kB)\n", "Collecting opentelemetry-instrumentation-fastapi>=0.41b0 (from chromadb>=0.5.23->crewai)\n", "  Downloading opentelemetry_instrumentation_fastapi-0.53b1-py3-none-any.whl.metadata (2.2 kB)\n", "Collecting pypika>=0.48.9 (from chromadb>=0.5.23-><PERSON>ai)\n", "  Downloading PyPika-0.48.9.tar.gz (67 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m67.3/67.3 kB\u001b[0m \u001b[31m6.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h  Installing build dependencies ... \u001b[?25l\u001b[?25hdone\n", "  Getting requirements to build wheel ... \u001b[?25l\u001b[?25hdone\n", "  Preparing metadata (pyproject.toml) ... \u001b[?25l\u001b[?25hdone\n", "Collecting overrides>=7.3.1 (from chromadb>=0.5.23->crewai)\n", "  Downloading overrides-7.7.0-py3-none-any.whl.metadata (5.8 kB)\n", "Requirement already satisfied: importlib-resources in /usr/local/lib/python3.11/dist-packages (from chromadb>=0.5.23->crewai) (6.5.2)\n", "Requirement already satisfied: grpcio>=1.58.0 in /usr/local/lib/python3.11/dist-packages (from chromadb>=0.5.23->crewai) (1.71.0)\n", "Collecting bcrypt>=4.0.1 (from chromadb>=0.5.23-><PERSON><PERSON>)\n", "  Downloading bcrypt-4.3.0-cp39-abi3-manylinux_2_34_x86_64.whl.metadata (10 kB)\n", "Requirement already satisfied: typer>=0.9.0 in /usr/local/lib/python3.11/dist-packages (from chromadb>=0.5.23->crewai) (0.15.2)\n", "Collecting kubernetes>=28.1.0 (from chromadb>=0.5.23-><PERSON>ai)\n", "  Downloading kubernetes-32.0.1-py2.py3-none-any.whl.metadata (1.7 kB)\n", "Requirement already satisfied: pyyaml>=6.0.0 in /usr/local/lib/python3.11/dist-packages (from chromadb>=0.5.23->crewai) (6.0.2)\n", "Collecting mmh3>=4.0.1 (from chromadb>=0.5.23->crewai)\n", "  Downloading mmh3-5.1.0-cp311-cp311-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (16 kB)\n", "Requirement already satisfied: orjson>=3.9.12 in /usr/local/lib/python3.11/dist-packages (from chromadb>=0.5.23->crewai) (3.10.16)\n", "Collecting starlette<0.46.0,>=0.40.0 (from fastapi==0.115.9->chromadb>=0.5.23-><PERSON>ai)\n", "  Downloading starlette-0.45.3-py3-none-any.whl.metadata (6.3 kB)\n", "Collecting alembic<2.0.0,>=1.13.1 (from embedchain>=0.1.114->crewai-tools)\n", "  Downloading alembic-1.15.2-py3-none-any.whl.metadata (7.3 kB)\n", "Requirement already satisfied: beautifulsoup4<5.0.0,>=4.12.2 in /usr/local/lib/python3.11/dist-packages (from embedchain>=0.1.114->crewai-tools) (4.13.4)\n", "Collecting chromadb>=0.5.23 (from crewai)\n", "  Downloading chromadb-0.5.23-py3-none-any.whl.metadata (6.8 kB)\n", "Collecting gptcache<0.2.0,>=0.1.43 (from embedchain>=0.1.114->crewai-tools)\n", "  Downloading gptcache-0.1.44-py3-none-any.whl.metadata (24 kB)\n", "Requirement already satisfied: langchain<0.4.0,>=0.3.1 in /usr/local/lib/python3.11/dist-packages (from embedchain>=0.1.114->crewai-tools) (0.3.23)\n", "Collecting langchain-cohere<0.4.0,>=0.3.0 (from embedchain>=0.1.114->crewai-tools)\n", "  Downloading langchain_cohere-0.3.5-py3-none-any.whl.metadata (6.7 kB)\n", "Collecting langchain-community<0.4.0,>=0.3.1 (from embedchain>=0.1.114->crewai-tools)\n", "  Downloading langchain_community-0.3.21-py3-none-any.whl.metadata (2.4 kB)\n", "Collecting langchain-openai<0.3.0,>=0.2.1 (from embedchain>=0.1.114->crewai-tools)\n", "  Downloading langchain_openai-0.2.14-py3-none-any.whl.metadata (2.7 kB)\n", "Requirement already satisfied: langsmith<0.4.0,>=0.3.18 in /usr/local/lib/python3.11/dist-packages (from embedchain>=0.1.114->crewai-tools) (0.3.31)\n", "Collecting mem0ai<0.2.0,>=0.1.54 (from embedchain>=0.1.114->crewai-tools)\n", "  Downloading mem0ai-0.1.92-py3-none-any.whl.metadata (8.5 kB)\n", "Collecting pypdf<6.0.0,>=5.0.0 (from embedchain>=0.1.114->crewai-tools)\n", "  Downloading pypdf-5.4.0-py3-none-any.whl.metadata (7.3 kB)\n", "Collecting pysbd<0.4.0,>=0.3.4 (from embedchain>=0.1.114->crewai-tools)\n", "  Downloading pysbd-0.3.4-py3-none-any.whl.metadata (6.1 kB)\n", "Collecting schema<0.8.0,>=0.7.5 (from embedchain>=0.1.114->crewai-tools)\n", "  Downloading schema-0.7.7-py2.py3-none-any.whl.metadata (34 kB)\n", "Requirement already satisfied: sqlalchemy<3.0.0,>=2.0.27 in /usr/local/lib/python3.11/dist-packages (from embedchain>=0.1.114->crewai-tools) (2.0.40)\n", "Collecting fastapi>=0.95.2 (from chromadb>=0.5.23->crewai)\n", "  Downloading fastapi-0.115.12-py3-none-any.whl.metadata (27 kB)\n", "Collecting tokenizers (from litellm==1.60.2->crewai)\n", "  Downloading tokenizers-0.20.3-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (6.7 kB)\n", "Requirement already satisfied: anyio in /usr/local/lib/python3.11/dist-packages (from httpx<0.28.0,>=0.23.0->litellm==1.60.2->crewai) (4.9.0)\n", "Requirement already satisfied: certifi in /usr/local/lib/python3.11/dist-packages (from httpx<0.28.0,>=0.23.0->litellm==1.60.2->crewai) (2025.1.31)\n", "Requirement already satisfied: httpcore==1.* in /usr/local/lib/python3.11/dist-packages (from httpx<0.28.0,>=0.23.0->litellm==1.60.2->crewai) (1.0.8)\n", "Requirement already satisfied: idna in /usr/local/lib/python3.11/dist-packages (from httpx<0.28.0,>=0.23.0->litellm==1.60.2->crewai) (3.10)\n", "Requirement already satisfied: sniffio in /usr/local/lib/python3.11/dist-packages (from httpx<0.28.0,>=0.23.0->litellm==1.60.2->crewai) (1.3.1)\n", "Requirement already satisfied: h11<0.15,>=0.13 in /usr/local/lib/python3.11/dist-packages (from httpcore==1.*->httpx<0.28.0,>=0.23.0->litellm==1.60.2->crewai) (0.14.0)\n", "Requirement already satisfied: docstring-parser<1.0,>=0.16 in /usr/local/lib/python3.11/dist-packages (from instructor>=1.3.3->crewai) (0.16)\n", "Collecting jiter<0.9,>=0.6.1 (from instructor>=1.3.3->crewai)\n", "  Downloading jiter-0.8.2-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (5.2 kB)\n", "Requirement already satisfied: pydantic-core<3.0.0,>=2.18.0 in /usr/local/lib/python3.11/dist-packages (from instructor>=1.3.3->crewai) (2.33.1)\n", "Requirement already satisfied: MarkupSafe>=2.0 in /usr/local/lib/python3.11/dist-packages (from jinja2<4.0.0,>=3.1.2->litellm==1.60.2->crewai) (3.0.2)\n", "Collecting deprecation (from lancedb>=0.5.4->crewai-tools)\n", "  Downloading deprecation-2.1.0-py2.py3-none-any.whl.metadata (4.6 kB)\n", "Requirement already satisfied: pyarrow>=14 in /usr/local/lib/python3.11/dist-packages (from lancedb>=0.5.4->crewai-tools) (18.1.0)\n", "Requirement already satisfied: packaging in /usr/local/lib/python3.11/dist-packages (from lancedb>=0.5.4->crewai-tools) (24.2)\n", "Collecting rapidfuzz<4.0.0,>=3.9.0 (from levenshtein<1.0.0->opik)\n", "  Downloading rapidfuzz-3.13.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (12 kB)\n", "Requirement already satisfied: distro<2,>=1.7.0 in /usr/local/lib/python3.11/dist-packages (from openai>=1.13.3->crewai) (1.9.0)\n", "Requirement already satisfied: et-xmlfile in /usr/local/lib/python3.11/dist-packages (from openpyxl>=3.1.5->crewai) (2.0.0)\n", "Requirement already satisfied: deprecated>=1.2.6 in /usr/local/lib/python3.11/dist-packages (from opentelemetry-api>=1.30.0->crewai) (1.2.18)\n", "Requirement already satisfied: googleapis-common-protos~=1.52 in /usr/local/lib/python3.11/dist-packages (from opentelemetry-exporter-otlp-proto-http>=1.30.0->crewai) (1.70.0)\n", "Collecting opentelemetry-exporter-otlp-proto-common==1.32.1 (from opentelemetry-exporter-otlp-proto-http>=1.30.0->crewai)\n", "  Downloading opentelemetry_exporter_otlp_proto_common-1.32.1-py3-none-any.whl.metadata (1.9 kB)\n", "Collecting opentelemetry-proto==1.32.1 (from opentelemetry-exporter-otlp-proto-http>=1.30.0->crewai)\n", "  Downloading opentelemetry_proto-1.32.1-py3-none-any.whl.metadata (2.4 kB)\n", "Requirement already satisfied: protobuf<6.0,>=5.0 in /usr/local/lib/python3.11/dist-packages (from opentelemetry-proto==1.32.1->opentelemetry-exporter-otlp-proto-http>=1.30.0->crewai) (5.29.4)\n", "Requirement already satisfied: opentelemetry-semantic-conventions==0.53b1 in /usr/local/lib/python3.11/dist-packages (from opentelemetry-sdk>=1.30.0->crewai) (0.53b1)\n", "Collecting pdfminer.six==20250327 (from pdfplumber>=0.11.4->crewai)\n", "  Downloading pdfminer_six-20250327-py3-none-any.whl.metadata (4.1 kB)\n", "Requirement already satisfied: Pillow>=9.1 in /usr/local/lib/python3.11/dist-packages (from pdfplumber>=0.11.4->crewai) (11.1.0)\n", "Collecting pypdfium2>=4.18.0 (from pdfplumber>=0.11.4->crewai)\n", "  Downloading pypdfium2-4.30.1-py3-none-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (48 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m48.2/48.2 kB\u001b[0m \u001b[31m3.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: charset-normalizer>=2.0.0 in /usr/local/lib/python3.11/dist-packages (from pdfminer.six==20250327->pdfplumber>=0.11.4->crewai) (3.4.1)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /usr/local/lib/python3.11/dist-packages (from pydantic>=2.4.2->crewai) (0.7.0)\n", "Requirement already satisfied: typing-inspection>=0.4.0 in /usr/local/lib/python3.11/dist-packages (from pydantic>=2.4.2->crewai) (0.4.0)\n", "Collecting nodeenv>=1.6.0 (from pyright>=1.1.350->crewai-tools)\n", "  Downloading nodeenv-1.9.1-py2.py3-none-any.whl.metadata (21 kB)\n", "Requirement already satisfied: ipython>=5.3.0 in /usr/local/lib/python3.11/dist-packages (from pyvis>=0.3.2->crewai) (7.34.0)\n", "Requirement already satisfied: jsonpickle>=1.4.1 in /usr/local/lib/python3.11/dist-packages (from pyvis>=0.3.2->crewai) (4.0.5)\n", "Requirement already satisfied: networkx>=1.11 in /usr/local/lib/python3.11/dist-packages (from pyvis>=0.3.2->crewai) (3.4.2)\n", "Requirement already satisfied: markdown-it-py>=2.2.0 in /usr/local/lib/python3.11/dist-packages (from rich->opik) (3.0.0)\n", "Requirement already satisfied: pygments<3.0.0,>=2.13.0 in /usr/local/lib/python3.11/dist-packages (from rich->opik) (2.18.0)\n", "Requirement already satisfied: iniconfig in /usr/local/lib/python3.11/dist-packages (from pytest->opik) (2.1.0)\n", "Requirement already satisfied: pluggy<2,>=1.5 in /usr/local/lib/python3.11/dist-packages (from pytest->opik) (1.5.0)\n", "Requirement already satisfied: aiohappyeyeballs>=2.3.0 in /usr/local/lib/python3.11/dist-packages (from aiohttp->litellm==1.60.2->crewai) (2.6.1)\n", "Requirement already satisfied: aiosignal>=1.1.2 in /usr/local/lib/python3.11/dist-packages (from aiohttp->litellm==1.60.2->crewai) (1.3.2)\n", "Requirement already satisfied: attrs>=17.3.0 in /usr/local/lib/python3.11/dist-packages (from aiohttp->litellm==1.60.2->crewai) (25.3.0)\n", "Requirement already satisfied: frozenlist>=1.1.1 in /usr/local/lib/python3.11/dist-packages (from aiohttp->litellm==1.60.2->crewai) (1.5.0)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in /usr/local/lib/python3.11/dist-packages (from aiohttp->litellm==1.60.2->crewai) (6.4.3)\n", "Requirement already satisfied: propcache>=0.2.0 in /usr/local/lib/python3.11/dist-packages (from aiohttp->litellm==1.60.2->crewai) (0.3.1)\n", "Requirement already satisfied: yarl<2.0,>=1.17.0 in /usr/local/lib/python3.11/dist-packages (from aiohttp->litellm==1.60.2->crewai) (1.19.0)\n", "Requirement already satisfied: <PERSON><PERSON> in /usr/lib/python3/dist-packages (from alembic<2.0.0,>=1.13.1->embedchain>=0.1.114->crewai-tools) (1.1.3)\n", "Requirement already satisfied: soupsieve>1.2 in /usr/local/lib/python3.11/dist-packages (from beautifulsoup4<5.0.0,>=4.12.2->embedchain>=0.1.114->crewai-tools) (2.6)\n", "Collecting pyproject_hooks (from build>=1.0.3->chromadb>=0.5.23->crewai)\n", "  Downloading pyproject_hooks-1.2.0-py3-none-any.whl.metadata (1.3 kB)\n", "Requirement already satisfied: cffi>=1.12 in /usr/local/lib/python3.11/dist-packages (from cryptography>=43.0.1->auth0-python>=4.7.1->crewai) (1.17.1)\n", "Requirement already satisfied: wrapt<2,>=1.10 in /usr/local/lib/python3.11/dist-packages (from deprecated>=1.2.6->opentelemetry-api>=1.30.0->crewai) (1.17.2)\n", "Requirement already satisfied: cachetools in /usr/local/lib/python3.11/dist-packages (from gptcache<0.2.0,>=0.1.43->embedchain>=0.1.114->crewai-tools) (5.5.2)\n", "Requirement already satisfied: zipp>=3.20 in /usr/local/lib/python3.11/dist-packages (from importlib-metadata>=6.8.0->litellm==1.60.2->crewai) (3.21.0)\n", "Requirement already satisfied: setuptools>=18.5 in /usr/local/lib/python3.11/dist-packages (from ipython>=5.3.0->pyvis>=0.3.2->crewai) (75.2.0)\n", "Collecting jedi>=0.16 (from ipython>=5.3.0->pyvis>=0.3.2-><PERSON><PERSON>)\n", "  Downloading jedi-0.19.2-py2.py3-none-any.whl.metadata (22 kB)\n", "Requirement already satisfied: decorator in /usr/local/lib/python3.11/dist-packages (from ipython>=5.3.0->pyvis>=0.3.2->crewai) (4.4.2)\n", "Requirement already satisfied: pickleshare in /usr/local/lib/python3.11/dist-packages (from ipython>=5.3.0->pyvis>=0.3.2->crewai) (0.7.5)\n", "Requirement already satisfied: traitlets>=4.2 in /usr/local/lib/python3.11/dist-packages (from ipython>=5.3.0->pyvis>=0.3.2->crewai) (5.7.1)\n", "Requirement already satisfied: prompt-toolkit!=3.0.0,!=3.0.1,<3.1.0,>=2.0.0 in /usr/local/lib/python3.11/dist-packages (from ipython>=5.3.0->pyvis>=0.3.2->crewai) (3.0.51)\n", "Requirement already satisfied: backcall in /usr/local/lib/python3.11/dist-packages (from ipython>=5.3.0->pyvis>=0.3.2->crewai) (0.2.0)\n", "Requirement already satisfied: matplotlib-inline in /usr/local/lib/python3.11/dist-packages (from ipython>=5.3.0->pyvis>=0.3.2->crewai) (0.1.7)\n", "Requirement already satisfied: pexpect>4.3 in /usr/local/lib/python3.11/dist-packages (from ipython>=5.3.0->pyvis>=0.3.2->crewai) (4.9.0)\n", "Requirement already satisfied: jsonschema-specifications>=2023.03.6 in /usr/local/lib/python3.11/dist-packages (from jsonschema<5.0.0,>=4.22.0->litellm==1.60.2->crewai) (2024.10.1)\n", "Requirement already satisfied: referencing>=0.28.4 in /usr/local/lib/python3.11/dist-packages (from jsonschema<5.0.0,>=4.22.0->litellm==1.60.2->crewai) (0.36.2)\n", "Requirement already satisfied: rpds-py>=0.7.1 in /usr/local/lib/python3.11/dist-packages (from jsonschema<5.0.0,>=4.22.0->litellm==1.60.2->crewai) (0.24.0)\n", "Requirement already satisfied: six>=1.9.0 in /usr/local/lib/python3.11/dist-packages (from kubernetes>=28.1.0->chromadb>=0.5.23->crewai) (1.17.0)\n", "Requirement already satisfied: python-dateutil>=2.5.3 in /usr/local/lib/python3.11/dist-packages (from kubernetes>=28.1.0->chromadb>=0.5.23->crewai) (2.8.2)\n", "Requirement already satisfied: google-auth>=1.0.1 in /usr/local/lib/python3.11/dist-packages (from kubernetes>=28.1.0->chromadb>=0.5.23->crewai) (2.38.0)\n", "Requirement already satisfied: websocket-client!=0.40.0,!=0.41.*,!=0.42.*,>=0.32.0 in /usr/local/lib/python3.11/dist-packages (from kubernetes>=28.1.0->chromadb>=0.5.23->crewai) (1.8.0)\n", "Requirement already satisfied: requests-oauthlib in /usr/local/lib/python3.11/dist-packages (from kubernetes>=28.1.0->chromadb>=0.5.23->crewai) (2.0.0)\n", "Requirement already satisfied: oauthlib>=3.2.2 in /usr/local/lib/python3.11/dist-packages (from kubernetes>=28.1.0->chromadb>=0.5.23->crewai) (3.2.2)\n", "Collecting durationpy>=0.7 (from kubernetes>=28.1.0->chromadb>=0.5.23->crewai)\n", "  Downloading durationpy-0.9-py3-none-any.whl.metadata (338 bytes)\n", "Requirement already satisfied: langchain-core<1.0.0,>=0.3.51 in /usr/local/lib/python3.11/dist-packages (from langchain<0.4.0,>=0.3.1->embedchain>=0.1.114->crewai-tools) (0.3.52)\n", "Requirement already satisfied: langchain-text-splitters<1.0.0,>=0.3.8 in /usr/local/lib/python3.11/dist-packages (from langchain<0.4.0,>=0.3.1->embedchain>=0.1.114->crewai-tools) (0.3.8)\n", "Collecting cohere<6.0,>=5.5.6 (from langchain-cohere<0.4.0,>=0.3.0->embedchain>=0.1.114->crewai-tools)\n", "  Downloading cohere-5.15.0-py3-none-any.whl.metadata (3.4 kB)\n", "Collecting langchain-experimental<0.4.0,>=0.3.0 (from langchain-cohere<0.4.0,>=0.3.0->embedchain>=0.1.114->crewai-tools)\n", "  Downloading langchain_experimental-0.3.4-py3-none-any.whl.metadata (1.7 kB)\n", "Requirement already satisfied: pandas>=1.4.3 in /usr/local/lib/python3.11/dist-packages (from langchain-cohere<0.4.0,>=0.3.0->embedchain>=0.1.114->crewai-tools) (2.2.2)\n", "Requirement already satisfied: tabulate<0.10.0,>=0.9.0 in /usr/local/lib/python3.11/dist-packages (from langchain-cohere<0.4.0,>=0.3.0->embedchain>=0.1.114->crewai-tools) (0.9.0)\n", "Collecting dataclasses-json<0.7,>=0.5.7 (from langchain-community<0.4.0,>=0.3.1->embedchain>=0.1.114->crewai-tools)\n", "  Downloading dataclasses_json-0.6.7-py3-none-any.whl.metadata (25 kB)\n", "Collecting httpx-sse<1.0.0,>=0.4.0 (from langchain-community<0.4.0,>=0.3.1->embedchain>=0.1.114->crewai-tools)\n", "  Downloading httpx_sse-0.4.0-py3-none-any.whl.metadata (9.0 kB)\n", "Requirement already satisfied: requests-toolbelt<2.0.0,>=1.0.0 in /usr/local/lib/python3.11/dist-packages (from langsmith<0.4.0,>=0.3.18->embedchain>=0.1.114->crewai-tools) (1.0.0)\n", "Requirement already satisfied: zstandard<0.24.0,>=0.23.0 in /usr/local/lib/python3.11/dist-packages (from langsmith<0.4.0,>=0.3.18->embedchain>=0.1.114->crewai-tools) (0.23.0)\n", "Requirement already satisfied: mdurl~=0.1 in /usr/local/lib/python3.11/dist-packages (from markdown-it-py>=2.2.0->rich->opik) (0.1.2)\n", "Collecting psycopg2-binary<3.0.0,>=2.9.10 (from mem0ai<0.2.0,>=0.1.54->embedchain>=0.1.114->crewai-tools)\n", "  Downloading psycopg2_binary-2.9.10-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (4.9 kB)\n", "Collecting pytz<2025.0,>=2024.1 (from mem0ai<0.2.0,>=0.1.54->embedchain>=0.1.114->crewai-tools)\n", "  Downloading pytz-2024.2-py2.py3-none-any.whl.metadata (22 kB)\n", "Collecting qdrant-client<2.0.0,>=1.9.1 (from mem0ai<0.2.0,>=0.1.54->embedchain>=0.1.114->crewai-tools)\n", "  Downloading qdrant_client-1.13.3-py3-none-any.whl.metadata (10 kB)\n", "Collecting coloredlogs (from onnxruntime>=1.14.1->chromadb>=0.5.23->crewai)\n", "  Downloading coloredlogs-15.0.1-py2.py3-none-any.whl.metadata (12 kB)\n", "Requirement already satisfied: flatbuffers in /usr/local/lib/python3.11/dist-packages (from onnxruntime>=1.14.1->chromadb>=0.5.23->crewai) (25.2.10)\n", "Requirement already satisfied: sympy in /usr/local/lib/python3.11/dist-packages (from onnxruntime>=1.14.1->chromadb>=0.5.23->crewai) (1.13.1)\n", "Collecting opentelemetry-instrumentation-asgi==0.53b1 (from opentelemetry-instrumentation-fastapi>=0.41b0->chromadb>=0.5.23->crewai)\n", "  Downloading opentelemetry_instrumentation_asgi-0.53b1-py3-none-any.whl.metadata (2.1 kB)\n", "Collecting opentelemetry-instrumentation==0.53b1 (from opentelemetry-instrumentation-fastapi>=0.41b0->chromadb>=0.5.23->crewai)\n", "  Downloading opentelemetry_instrumentation-0.53b1-py3-none-any.whl.metadata (6.8 kB)\n", "Collecting opentelemetry-util-http==0.53b1 (from opentelemetry-instrumentation-fastapi>=0.41b0->chromadb>=0.5.23->crewai)\n", "  Downloading opentelemetry_util_http-0.53b1-py3-none-any.whl.metadata (2.6 kB)\n", "Collecting asgiref~=3.0 (from opentelemetry-instrumentation-asgi==0.53b1->opentelemetry-instrumentation-fastapi>=0.41b0->chromadb>=0.5.23->crewai)\n", "  Downloading asgiref-3.8.1-py3-none-any.whl.metadata (9.3 kB)\n", "Collecting monotonic>=1.5 (from posthog>=2.4.0->chromadb>=0.5.23->crewai)\n", "  Downloading monotonic-1.6-py2.py3-none-any.whl.metadata (1.5 kB)\n", "Collecting backoff>=1.10.0 (from posthog>=2.4.0->chromadb>=0.5.23->crewai)\n", "  Downloading backoff-2.2.1-py3-none-any.whl.metadata (14 kB)\n", "Requirement already satisfied: greenlet>=1 in /usr/local/lib/python3.11/dist-packages (from sqlalchemy<3.0.0,>=2.0.27->embedchain>=0.1.114->crewai-tools) (3.2.0)\n", "Requirement already satisfied: huggingface-hub<1.0,>=0.16.4 in /usr/local/lib/python3.11/dist-packages (from tokenizers->litellm==1.60.2->crewai) (0.30.2)\n", "Requirement already satisfied: shellingham>=1.3.0 in /usr/local/lib/python3.11/dist-packages (from typer>=0.9.0->chromadb>=0.5.23->crewai) (1.5.4)\n", "Collecting httptools>=0.6.3 (from uvicorn[standard]>=0.18.3->chromadb>=0.5.23-><PERSON>ai)\n", "  Downloading httptools-0.6.4-cp311-cp311-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (3.6 kB)\n", "Collecting uvloop!=0.15.0,!=0.15.1,>=0.14.0 (from uvicorn[standard]>=0.18.3->chromadb>=0.5.23->crewai)\n", "  Downloading uvloop-0.21.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (4.9 kB)\n", "Collecting watchfiles>=0.13 (from uvicorn[standard]>=0.18.3->chromadb>=0.5.23->crewai)\n", "  Downloading watchfiles-1.0.5-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (4.9 kB)\n", "Requirement already satisfied: websockets>=10.4 in /usr/local/lib/python3.11/dist-packages (from uvicorn[standard]>=0.18.3->chromadb>=0.5.23->crewai) (15.0.1)\n", "Collecting types-awscrt (from botocore-stubs->boto3-stubs>=1.34.110->boto3-stubs[bedrock-runtime]>=1.34.110->opik)\n", "  Downloading types_awscrt-0.26.1-py3-none-any.whl.metadata (4.9 kB)\n", "Requirement already satisfied: pycparser in /usr/local/lib/python3.11/dist-packages (from cffi>=1.12->cryptography>=43.0.1->auth0-python>=4.7.1->crewai) (2.22)\n", "Collecting fastavro<2.0.0,>=1.9.4 (from cohere<6.0,>=5.5.6->langchain-cohere<0.4.0,>=0.3.0->embedchain>=0.1.114->crewai-tools)\n", "  Downloading fastavro-1.10.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (5.5 kB)\n", "Collecting types-requests<3.0.0,>=2.0.0 (from cohere<6.0,>=5.5.6->langchain-cohere<0.4.0,>=0.3.0->embedchain>=0.1.114->crewai-tools)\n", "  Downloading types_requests-2.32.0.********-py3-none-any.whl.metadata (2.3 kB)\n", "Collecting marshmallow<4.0.0,>=3.18.0 (from dataclasses-json<0.7,>=0.5.7->langchain-community<0.4.0,>=0.3.1->embedchain>=0.1.114->crewai-tools)\n", "  Downloading marshmallow-3.26.1-py3-none-any.whl.metadata (7.3 kB)\n", "Collecting typing-inspect<1,>=0.4.0 (from dataclasses-json<0.7,>=0.5.7->langchain-community<0.4.0,>=0.3.1->embedchain>=0.1.114->crewai-tools)\n", "  Downloading typing_inspect-0.9.0-py3-none-any.whl.metadata (1.5 kB)\n", "Requirement already satisfied: pyasn1-modules>=0.2.1 in /usr/local/lib/python3.11/dist-packages (from google-auth>=1.0.1->kubernetes>=28.1.0->chromadb>=0.5.23->crewai) (0.4.2)\n", "Requirement already satisfied: rsa<5,>=3.1.4 in /usr/local/lib/python3.11/dist-packages (from google-auth>=1.0.1->kubernetes>=28.1.0->chromadb>=0.5.23->crewai) (4.9.1)\n", "Requirement already satisfied: filelock in /usr/local/lib/python3.11/dist-packages (from huggingface-hub<1.0,>=0.16.4->tokenizers->litellm==1.60.2->crewai) (3.18.0)\n", "Requirement already satisfied: fsspec>=2023.5.0 in /usr/local/lib/python3.11/dist-packages (from huggingface-hub<1.0,>=0.16.4->tokenizers->litellm==1.60.2->crewai) (2025.3.2)\n", "Requirement already satisfied: parso<0.9.0,>=0.8.4 in /usr/local/lib/python3.11/dist-packages (from jedi>=0.16->ipython>=5.3.0->pyvis>=0.3.2->crewai) (0.8.4)\n", "Requirement already satisfied: jsonpatch<2.0,>=1.33 in /usr/local/lib/python3.11/dist-packages (from langchain-core<1.0.0,>=0.3.51->langchain<0.4.0,>=0.3.1->embedchain>=0.1.114->crewai-tools) (1.33)\n", "Requirement already satisfied: tzdata>=2022.7 in /usr/local/lib/python3.11/dist-packages (from pandas>=1.4.3->langchain-cohere<0.4.0,>=0.3.0->embedchain>=0.1.114->crewai-tools) (2025.2)\n", "Requirement already satisfied: ptyprocess>=0.5 in /usr/local/lib/python3.11/dist-packages (from pexpect>4.3->ipython>=5.3.0->pyvis>=0.3.2->crewai) (0.7.0)\n", "Requirement already satisfied: wcwidth in /usr/local/lib/python3.11/dist-packages (from prompt-toolkit!=3.0.0,!=3.0.1,<3.1.0,>=2.0.0->ipython>=5.3.0->pyvis>=0.3.2->crewai) (0.2.13)\n", "Collecting grpcio-tools>=1.41.0 (from qdrant-client<2.0.0,>=1.9.1->mem0ai<0.2.0,>=0.1.54->embedchain>=0.1.114->crewai-tools)\n", "  Downloading grpcio_tools-1.71.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (5.3 kB)\n", "Collecting portalocker<3.0.0,>=2.7.0 (from qdrant-client<2.0.0,>=1.9.1->mem0ai<0.2.0,>=0.1.54->embedchain>=0.1.114->crewai-tools)\n", "  Downloading portalocker-2.10.1-py3-none-any.whl.metadata (8.5 kB)\n", "Collecting humanfriendly>=9.1 (from coloredlogs->onnxruntime>=1.14.1->chromadb>=0.5.23->crewai)\n", "  Downloading humanfriendly-10.0-py2.py3-none-any.whl.metadata (9.2 kB)\n", "Requirement already satisfied: mpmath<1.4,>=1.1.0 in /usr/local/lib/python3.11/dist-packages (from sympy->onnxruntime>=1.14.1->chromadb>=0.5.23->crewai) (1.3.0)\n", "Requirement already satisfied: h2<5,>=3 in /usr/local/lib/python3.11/dist-packages (from httpx[http2]>=0.20.0->qdrant-client<2.0.0,>=1.9.1->mem0ai<0.2.0,>=0.1.54->embedchain>=0.1.114->crewai-tools) (4.2.0)\n", "Requirement already satisfied: jsonpointer>=1.9 in /usr/local/lib/python3.11/dist-packages (from jsonpatch<2.0,>=1.33->langchain-core<1.0.0,>=0.3.51->langchain<0.4.0,>=0.3.1->embedchain>=0.1.114->crewai-tools) (3.0.0)\n", "Requirement already satisfied: pyasn1<0.7.0,>=0.6.1 in /usr/local/lib/python3.11/dist-packages (from pyasn1-modules>=0.2.1->google-auth>=1.0.1->kubernetes>=28.1.0->chromadb>=0.5.23->crewai) (0.6.1)\n", "Collecting mypy-extensions>=0.3.0 (from typing-inspect<1,>=0.4.0->dataclasses-json<0.7,>=0.5.7->langchain-community<0.4.0,>=0.3.1->embedchain>=0.1.114->crewai-tools)\n", "  Downloading mypy_extensions-1.0.0-py3-none-any.whl.metadata (1.1 kB)\n", "Requirement already satisfied: hyperframe<7,>=6.1 in /usr/local/lib/python3.11/dist-packages (from h2<5,>=3->httpx[http2]>=0.20.0->qdrant-client<2.0.0,>=1.9.1->mem0ai<0.2.0,>=0.1.54->embedchain>=0.1.114->crewai-tools) (6.1.0)\n", "Requirement already satisfied: hpack<5,>=4.1 in /usr/local/lib/python3.11/dist-packages (from h2<5,>=3->httpx[http2]>=0.20.0->qdrant-client<2.0.0,>=1.9.1->mem0ai<0.2.0,>=0.1.54->embedchain>=0.1.114->crewai-tools) (4.1.0)\n", "Downloading crewai-0.114.0-py3-none-any.whl (285 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m285.5/285.5 kB\u001b[0m \u001b[31m18.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading litellm-1.60.2-py3-none-any.whl (6.7 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m6.7/6.7 MB\u001b[0m \u001b[31m124.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading crewai_tools-0.40.1-py3-none-any.whl (573 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m573.3/573.3 kB\u001b[0m \u001b[31m51.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading opik-1.7.9-py3-none-any.whl (467 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m467.8/467.8 kB\u001b[0m \u001b[31m31.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading appdirs-1.4.4-py2.py3-none-any.whl (9.6 kB)\n", "Downloading auth0_python-4.9.0-py3-none-any.whl (135 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m135.3/135.3 kB\u001b[0m \u001b[31m13.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading boto3_stubs-1.37.37-py3-none-any.whl (68 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m68.7/68.7 kB\u001b[0m \u001b[31m7.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading chroma_hnswlib-0.7.6-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (2.4 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.4/2.4 MB\u001b[0m \u001b[31m113.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading fastapi-0.115.9-py3-none-any.whl (94 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m94.9/94.9 kB\u001b[0m \u001b[31m9.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading docker-7.1.0-py3-none-any.whl (147 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m147.8/147.8 kB\u001b[0m \u001b[31m14.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading embedchain-0.1.128-py3-none-any.whl (211 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m211.3/211.3 kB\u001b[0m \u001b[31m21.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading chromadb-0.5.23-py3-none-any.whl (628 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m628.3/628.3 kB\u001b[0m \u001b[31m52.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading httpx-0.27.2-py3-none-any.whl (76 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m76.4/76.4 kB\u001b[0m \u001b[31m8.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading instructor-1.7.9-py3-none-any.whl (86 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m86.0/86.0 kB\u001b[0m \u001b[31m9.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading json_repair-0.41.1-py3-none-any.whl (21 kB)\n", "Downloading json5-0.12.0-py3-none-any.whl (36 kB)\n", "Downloading jsonref-1.1.0-py3-none-any.whl (9.4 kB)\n", "Downloading lancedb-0.21.2-cp39-abi3-manylinux_2_28_x86_64.whl (32.9 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m32.9/32.9 MB\u001b[0m \u001b[31m71.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading levenshtein-0.27.1-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (161 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m161.7/161.7 kB\u001b[0m \u001b[31m15.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading opentelemetry_exporter_otlp_proto_http-1.32.1-py3-none-any.whl (17 kB)\n", "Downloading opentelemetry_exporter_otlp_proto_common-1.32.1-py3-none-any.whl (18 kB)\n", "Downloading opentelemetry_proto-1.32.1-py3-none-any.whl (55 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m55.9/55.9 kB\u001b[0m \u001b[31m5.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading pdfplumber-0.11.6-py3-none-any.whl (60 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m60.2/60.2 kB\u001b[0m \u001b[31m6.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading pdfminer_six-20250327-py3-none-any.whl (5.6 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m5.6/5.6 MB\u001b[0m \u001b[31m143.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading pydantic_settings-2.9.1-py3-none-any.whl (44 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m44.4/44.4 kB\u001b[0m \u001b[31m4.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading pyright-1.1.399-py3-none-any.whl (5.6 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m5.6/5.6 MB\u001b[0m \u001b[31m116.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading python_dotenv-1.1.0-py3-none-any.whl (20 kB)\n", "Downloading pytube-15.0.0-py3-none-any.whl (57 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m57.6/57.6 kB\u001b[0m \u001b[31m4.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading pyvis-0.3.2-py3-none-any.whl (756 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m756.0/756.0 kB\u001b[0m \u001b[31m53.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading tomli-2.2.1-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (236 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m236.0/236.0 kB\u001b[0m \u001b[31m22.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading tomli_w-1.2.0-py3-none-any.whl (6.7 kB)\n", "Downloading uv-0.6.14-py3-none-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (16.9 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m16.9/16.9 MB\u001b[0m \u001b[31m128.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading uuid6-2024.7.10-py3-none-any.whl (6.4 kB)\n", "Downloading alembic-1.15.2-py3-none-any.whl (231 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m231.9/231.9 kB\u001b[0m \u001b[31m22.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading bcrypt-4.3.0-cp39-abi3-manylinux_2_34_x86_64.whl (284 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m284.2/284.2 kB\u001b[0m \u001b[31m24.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading build-1.2.2.post1-py3-none-any.whl (22 kB)\n", "Downloading gptcache-0.1.44-py3-none-any.whl (131 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m131.6/131.6 kB\u001b[0m \u001b[31m13.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading jiter-0.8.2-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (345 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m345.6/345.6 kB\u001b[0m \u001b[31m31.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading kubernetes-32.0.1-py2.py3-none-any.whl (2.0 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.0/2.0 MB\u001b[0m \u001b[31m92.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading langchain_cohere-0.3.5-py3-none-any.whl (45 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m45.1/45.1 kB\u001b[0m \u001b[31m4.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading langchain_community-0.3.21-py3-none-any.whl (2.5 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.5/2.5 MB\u001b[0m \u001b[31m98.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading langchain_openai-0.2.14-py3-none-any.whl (50 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m50.9/50.9 kB\u001b[0m \u001b[31m4.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading mem0ai-0.1.92-py3-none-any.whl (136 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m136.7/136.7 kB\u001b[0m \u001b[31m13.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading mmh3-5.1.0-cp311-cp311-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl (101 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m101.6/101.6 kB\u001b[0m \u001b[31m10.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading mypy_boto3_bedrock_runtime-1.37.30-py3-none-any.whl (31 kB)\n", "Downloading nodeenv-1.9.1-py2.py3-none-any.whl (22 kB)\n", "Downloading onnxruntime-1.21.1-cp311-cp311-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl (16.0 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m16.0/16.0 MB\u001b[0m \u001b[31m30.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading opentelemetry_exporter_otlp_proto_grpc-1.32.1-py3-none-any.whl (18 kB)\n", "Downloading opentelemetry_instrumentation_fastapi-0.53b1-py3-none-any.whl (12 kB)\n", "Downloading opentelemetry_instrumentation-0.53b1-py3-none-any.whl (30 kB)\n", "Downloading opentelemetry_instrumentation_asgi-0.53b1-py3-none-any.whl (16 kB)\n", "Downloading opentelemetry_util_http-0.53b1-py3-none-any.whl (7.3 kB)\n", "Downloading overrides-7.7.0-py3-none-any.whl (17 kB)\n", "Downloading posthog-3.25.0-py2.py3-none-any.whl (89 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m89.1/89.1 kB\u001b[0m \u001b[31m9.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading pypdf-5.4.0-py3-none-any.whl (302 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m302.3/302.3 kB\u001b[0m \u001b[31m27.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading pypdfium2-4.30.1-py3-none-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (2.9 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.9/2.9 MB\u001b[0m \u001b[31m103.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading pysbd-0.3.4-py3-none-any.whl (71 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m71.1/71.1 kB\u001b[0m \u001b[31m7.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading rapidfuzz-3.13.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (3.1 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m3.1/3.1 MB\u001b[0m \u001b[31m108.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading schema-0.7.7-py2.py3-none-any.whl (18 kB)\n", "Downloading tiktoken-0.9.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (1.2 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.2/1.2 MB\u001b[0m \u001b[31m80.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading tokenizers-0.20.3-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (3.0 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m3.0/3.0 MB\u001b[0m \u001b[31m118.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading uvicorn-0.34.2-py3-none-any.whl (62 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m62.5/62.5 kB\u001b[0m \u001b[31m6.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading botocore_stubs-1.37.37-py3-none-any.whl (65 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m65.6/65.6 kB\u001b[0m \u001b[31m6.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading deprecation-2.1.0-py2.py3-none-any.whl (11 kB)\n", "Downloading types_s3transfer-0.11.5-py3-none-any.whl (19 kB)\n", "Downloading backoff-2.2.1-py3-none-any.whl (15 kB)\n", "Downloading cohere-5.15.0-py3-none-any.whl (259 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m259.5/259.5 kB\u001b[0m \u001b[31m26.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading httpx_sse-0.4.0-py3-none-any.whl (7.8 kB)\n", "Downloading dataclasses_json-0.6.7-py3-none-any.whl (28 kB)\n", "Downloading durationpy-0.9-py3-none-any.whl (3.5 kB)\n", "Downloading httptools-0.6.4-cp311-cp311-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl (459 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m459.8/459.8 kB\u001b[0m \u001b[31m40.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading jedi-0.19.2-py2.py3-none-any.whl (1.6 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.6/1.6 MB\u001b[0m \u001b[31m87.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading langchain_experimental-0.3.4-py3-none-any.whl (209 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m209.2/209.2 kB\u001b[0m \u001b[31m21.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading monotonic-1.6-py2.py3-none-any.whl (8.2 kB)\n", "Downloading psycopg2_binary-2.9.10-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (3.0 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m3.0/3.0 MB\u001b[0m \u001b[31m123.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading pytz-2024.2-py2.py3-none-any.whl (508 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m508.0/508.0 kB\u001b[0m \u001b[31m47.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading qdrant_client-1.13.3-py3-none-any.whl (306 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m306.7/306.7 kB\u001b[0m \u001b[31m32.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading starlette-0.45.3-py3-none-any.whl (71 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m71.5/71.5 kB\u001b[0m \u001b[31m7.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading uvloop-0.21.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (4.0 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m4.0/4.0 MB\u001b[0m \u001b[31m129.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading watchfiles-1.0.5-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (454 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m454.8/454.8 kB\u001b[0m \u001b[31m39.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading coloredlogs-15.0.1-py2.py3-none-any.whl (46 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m46.0/46.0 kB\u001b[0m \u001b[31m4.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading pyproject_hooks-1.2.0-py3-none-any.whl (10 kB)\n", "Downloading types_awscrt-0.26.1-py3-none-any.whl (19 kB)\n", "Downloading asgiref-3.8.1-py3-none-any.whl (23 kB)\n", "Downloading fastavro-1.10.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (3.3 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m3.3/3.3 MB\u001b[0m \u001b[31m128.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading grpcio_tools-1.71.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (2.5 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.5/2.5 MB\u001b[0m \u001b[31m100.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading humanfriendly-10.0-py2.py3-none-any.whl (86 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m86.8/86.8 kB\u001b[0m \u001b[31m9.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading marshmallow-3.26.1-py3-none-any.whl (50 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m50.9/50.9 kB\u001b[0m \u001b[31m4.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading portalocker-2.10.1-py3-none-any.whl (18 kB)\n", "Downloading types_requests-2.32.0.********-py3-none-any.whl (20 kB)\n", "Downloading typing_inspect-0.9.0-py3-none-any.whl (8.8 kB)\n", "Downloading mypy_extensions-1.0.0-py3-none-any.whl (4.7 kB)\n", "Building wheels for collected packages: pypika\n", "  Building wheel for pypika (pyproject.toml) ... \u001b[?25l\u001b[?25hdone\n", "  Created wheel for pypika: filename=pypika-0.48.9-py2.py3-none-any.whl size=53800 sha256=b911de12c743137bda48145d57333dd6a97a221c296f1cae60718a592ad47d88\n", "  Stored in directory: /root/.cache/pip/wheels/a3/01/bd/4c40ceb9d5354160cb186dcc153360f4ab7eb23e2b24daf96d\n", "Successfully built pypika\n", "Installing collected packages: schema, pytz, pypika, monotonic, durationpy, appdirs, uvloop, uvicorn, uv, uuid6, types-s3transfer, types-requests, types-awscrt, tomli-w, tomli, rapidfuzz, pytube, python-dotenv, pysbd, pyproject_hooks, pypdfium2, pypdf, psycopg2-binary, portalocker, overrides, opentelemetry-util-http, opentelemetry-proto, nodeenv, mypy-extensions, mypy-boto3-bedrock-runtime, mmh3, marshmallow, jsonref, json5, json-repair, jiter, jedi, humanfriendly, httpx-sse, httptools, grpcio-tools, fastavro, deprecation, chroma-hnswlib, bcrypt, backoff, asgiref, watchfiles, typing-inspect, tiktoken, starlette, pyright, posthog, opentelemetry-exporter-otlp-proto-common, levenshtein, httpx, gptcache, docker, coloredlogs, build, botocore-stubs, alembic, tokenizers, pyvis, pydantic-settings, pdfminer.six, onnxruntime, lancedb, kubernetes, fastapi, dataclasses-json, boto3-stubs, auth0-python, qdrant-client, pdfplumber, opentelemetry-instrumentation, litellm, instructor, cohere, opik, opentelemetry-instrumentation-asgi, opentelemetry-exporter-otlp-proto-http, opentelemetry-exporter-otlp-proto-grpc, mem0ai, langchain-openai, opentelemetry-instrumentation-fastapi, langchain-community, chromadb, langchain-experimental, crewai, langchain-cohere, embedchain, crewai-tools\n", "  Attempting uninstall: pytz\n", "    Found existing installation: pytz 2025.2\n", "    Uninstalling pytz-2025.2:\n", "      Successfully uninstalled pytz-2025.2\n", "  Attempting uninstall: jiter\n", "    Found existing installation: jiter 0.9.0\n", "    Uninstalling jiter-0.9.0:\n", "      Successfully uninstalled jiter-0.9.0\n", "  Attempting uninstall: httpx\n", "    Found existing installation: httpx 0.28.1\n", "    Uninstalling httpx-0.28.1:\n", "      Successfully uninstalled httpx-0.28.1\n", "  Attempting uninstall: tokenizers\n", "    Found existing installation: tokenizers 0.21.1\n", "    Uninstalling tokenizers-0.21.1:\n", "      Successfully uninstalled tokenizers-0.21.1\n", "\u001b[31mERROR: pip's dependency resolver does not currently take into account all the packages that are installed. This behaviour is the source of the following dependency conflicts.\n", "transformers 4.51.3 requires tokenizers<0.22,>=0.21, but you have tokenizers 0.20.3 which is incompatible.\n", "google-genai 1.10.0 requires httpx<1.0.0,>=0.28.1, but you have httpx 0.27.2 which is incompatible.\u001b[0m\u001b[31m\n", "\u001b[0mSuccessfully installed alembic-1.15.2 appdirs-1.4.4 asgiref-3.8.1 auth0-python-4.9.0 backoff-2.2.1 bcrypt-4.3.0 boto3-stubs-1.37.37 botocore-stubs-1.37.37 build-1.2.2.post1 chroma-hnswlib-0.7.6 chromadb-0.5.23 cohere-5.15.0 coloredlogs-15.0.1 crewai-0.114.0 crewai-tools-0.40.1 dataclasses-json-0.6.7 deprecation-2.1.0 docker-7.1.0 durationpy-0.9 embedchain-0.1.128 fastapi-0.115.9 fastavro-1.10.0 gptcache-0.1.44 grpcio-tools-1.71.0 httptools-0.6.4 httpx-0.27.2 httpx-sse-0.4.0 humanfriendly-10.0 instructor-1.7.9 jedi-0.19.2 jiter-0.8.2 json-repair-0.41.1 json5-0.12.0 jsonref-1.1.0 kubernetes-32.0.1 lancedb-0.21.2 langchain-cohere-0.3.5 langchain-community-0.3.21 langchain-experimental-0.3.4 langchain-openai-0.2.14 levenshtein-0.27.1 litellm-1.60.2 marshmallow-3.26.1 mem0ai-0.1.92 mmh3-5.1.0 monotonic-1.6 mypy-boto3-bedrock-runtime-1.37.30 mypy-extensions-1.0.0 nodeenv-1.9.1 onnxruntime-1.21.1 opentelemetry-exporter-otlp-proto-common-1.32.1 opentelemetry-exporter-otlp-proto-grpc-1.32.1 opentelemetry-exporter-otlp-proto-http-1.32.1 opentelemetry-instrumentation-0.53b1 opentelemetry-instrumentation-asgi-0.53b1 opentelemetry-instrumentation-fastapi-0.53b1 opentelemetry-proto-1.32.1 opentelemetry-util-http-0.53b1 opik-1.7.9 overrides-7.7.0 pdfminer.six-20250327 pdfplumber-0.11.6 portalocker-2.10.1 posthog-3.25.0 psycopg2-binary-2.9.10 pydantic-settings-2.9.1 pypdf-5.4.0 pypdfium2-4.30.1 pypika-0.48.9 pyproject_hooks-1.2.0 pyright-1.1.399 pysbd-0.3.4 python-dotenv-1.1.0 pytube-15.0.0 pytz-2024.2 pyvis-0.3.2 qdrant-client-1.13.3 rapidfuzz-3.13.0 schema-0.7.7 starlette-0.45.3 tiktoken-0.9.0 tokenizers-0.20.3 tomli-2.2.1 tomli-w-1.2.0 types-awscrt-0.26.1 types-requests-2.32.0.******** types-s3transfer-0.11.5 typing-inspect-0.9.0 uuid6-2024.7.10 uv-0.6.14 uvicorn-0.34.2 uvloop-0.21.0 watchfiles-1.0.5\n"]}], "source": ["%pip install crewai crewai-tools opik --upgrade"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "CGafJaW54zZr", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "********-ac0f-48e9-f7e9-eeeccbec7e25"}, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["OPIK: Your Opik API key is available in your account settings, can be found at https://www.comet.com/api/my/settings/ for Opik cloud\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Please enter your Opik API key:··········\n", "Do you want to use \"sourangshupal\" workspace? (Y/n)Y\n"]}, {"output_type": "stream", "name": "stderr", "text": ["OPIK: Configuration saved to file: /root/.opik.config\n"]}], "source": ["import opik\n", "\n", "opik.configure(use_local=False)"]}, {"cell_type": "markdown", "metadata": {"id": "_btdc6w64zZr"}, "source": ["## Preparing our environment\n", "\n", "First, we set up our API keys for our LLM-provider as environment variables:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2025-01-13T22:43:44.846076Z", "start_time": "2025-01-13T22:43:42.594413Z"}, "id": "y3yff3nq4zZr", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "5da8121f-54f8-4281-8e93-b25b22d747b0"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Enter your OpenAI API key: ··········\n"]}], "source": ["import os\n", "import getpass\n", "\n", "if \"OPENAI_API_KEY\" not in os.environ:\n", "    os.environ[\"OPENAI_API_KEY\"] = getpass.getpass(\"Enter your OpenAI API key: \")"]}, {"cell_type": "markdown", "metadata": {"id": "q8F9QyzN4zZs"}, "source": ["## Using CrewAI\n", "The first step is to create our project. We will use an example from CrewAI's documentation:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2025-01-13T22:43:24.541363Z", "start_time": "2025-01-13T22:43:21.919368Z"}, "id": "ufZmSfin4zZs"}, "outputs": [], "source": ["from crewai import Agent, Crew, Task, Process\n", "\n", "\n", "class YourCrewName:\n", "    def agent_one(self) -> Agent:\n", "        return Agent(\n", "            role=\"Data Analyst\",\n", "            goal=\"Analyze data trends in the market\",\n", "            backstory=\"An experienced data analyst with a background in economics\",\n", "            verbose=True,\n", "        )\n", "\n", "    def agent_two(self) -> Agent:\n", "        return Agent(\n", "            role=\"Market Researcher\",\n", "            goal=\"Gather information on market dynamics\",\n", "            backstory=\"A diligent researcher with a keen eye for detail\",\n", "            verbose=True,\n", "        )\n", "\n", "    def task_one(self) -> Task:\n", "        return Task(\n", "            name=\"Collect Data Task\",\n", "            description=\"Collect recent market data and identify trends.\",\n", "            expected_output=\"A report summarizing key trends in the market.\",\n", "            agent=self.agent_one(),\n", "        )\n", "\n", "    def task_two(self) -> Task:\n", "        return Task(\n", "            name=\"Market Research Task\",\n", "            description=\"Research factors affecting market dynamics.\",\n", "            expected_output=\"An analysis of factors influencing the market.\",\n", "            agent=self.agent_two(),\n", "        )\n", "\n", "    def crew(self) -> Crew:\n", "        return Crew(\n", "            agents=[self.agent_one(), self.agent_two()],\n", "            tasks=[self.task_one(), self.task_two()],\n", "            process=Process.sequential,\n", "            verbose=True,\n", "        )"]}, {"cell_type": "markdown", "metadata": {"id": "R55rfFIx4zZs"}, "source": ["Now we can import <PERSON><PERSON>'s tracker and run our `crew`:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2025-01-13T22:44:15.997951Z", "start_time": "2025-01-13T22:43:48.508770Z"}, "id": "xw2ajuPP4zZs", "outputId": "7e48b37d-8a2a-4d5b-fb2d-364d5c480469", "colab": {"base_uri": "https://localhost:8080/", "height": 1000}}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["\u001b[36m╭─\u001b[0m\u001b[36m───────────────────────────────────────────\u001b[0m\u001b[36m Crew Execution Started \u001b[0m\u001b[36m────────────────────────────────────────────\u001b[0m\u001b[36m─╮\u001b[0m\n", "\u001b[36m│\u001b[0m                                                                                                                 \u001b[36m│\u001b[0m\n", "\u001b[36m│\u001b[0m  \u001b[1;36mCrew Execution Started\u001b[0m                                                                                         \u001b[36m│\u001b[0m\n", "\u001b[36m│\u001b[0m  \u001b[37mName: \u001b[0m\u001b[36mcrew\u001b[0m                                                                                                     \u001b[36m│\u001b[0m\n", "\u001b[36m│\u001b[0m  \u001b[37mID: \u001b[0m\u001b[36m03bc2b16-db94-4c32-8d0c-70c68ab86e51\u001b[0m                                                                       \u001b[36m│\u001b[0m\n", "\u001b[36m│\u001b[0m                                                                                                                 \u001b[36m│\u001b[0m\n", "\u001b[36m│\u001b[0m                                                                                                                 \u001b[36m│\u001b[0m\n", "\u001b[36m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080\">╭──────────────────────────────────────────── Crew Execution Started ─────────────────────────────────────────────╮</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080\">│</span>                                                                                                                 <span style=\"color: #008080; text-decoration-color: #008080\">│</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080\">│</span>  <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">Crew Execution Started</span>                                                                                         <span style=\"color: #008080; text-decoration-color: #008080\">│</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080\">│</span>  <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">Name: </span><span style=\"color: #008080; text-decoration-color: #008080\">crew</span>                                                                                                     <span style=\"color: #008080; text-decoration-color: #008080\">│</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080\">│</span>  <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">ID: </span><span style=\"color: #008080; text-decoration-color: #008080\">03bc2b16-db94-4c32-8d0c-70c68ab86e51</span>                                                                       <span style=\"color: #008080; text-decoration-color: #008080\">│</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080\">│</span>                                                                                                                 <span style=\"color: #008080; text-decoration-color: #008080\">│</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080\">│</span>                                                                                                                 <span style=\"color: #008080; text-decoration-color: #008080\">│</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"]}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["\n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">\n", "</pre>\n"]}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["\u001b[1;36m🚀 Crew: \u001b[0m\u001b[1;36mcrew\u001b[0m\n", "└── \u001b[1;33m📋 Task: a2a73614-d94b-407a-9564-6cc967f05215\u001b[0m\n", "    \u001b[37m   Status: \u001b[0m\u001b[2;33mExecuting Task...\u001b[0m\n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">🚀 Crew: crew</span>\n", "└── <span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">📋 Task: a2a73614-d94b-407a-9564-6cc967f05215</span>\n", "    <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">   Status: </span><span style=\"color: #bfbf7f; text-decoration-color: #bfbf7f\">Executing Task...</span>\n", "</pre>\n"]}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["\n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">\n", "</pre>\n"]}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["\u001b[1;36m🚀 Crew: \u001b[0m\u001b[1;36mcrew\u001b[0m\n", "└── \u001b[1;33m📋 Task: a2a73614-d94b-407a-9564-6cc967f05215\u001b[0m\n", "    \u001b[37m   Status: \u001b[0m\u001b[2;33mExecuting Task...\u001b[0m\n", "    └── \u001b[1;32m🤖 Agent: \u001b[0m\u001b[32mData Analyst\u001b[0m\n", "        \u001b[37m    Status: \u001b[0m\u001b[1;32mIn Progress\u001b[0m\n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">🚀 Crew: crew</span>\n", "└── <span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">📋 Task: a2a73614-d94b-407a-9564-6cc967f05215</span>\n", "    <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">   Status: </span><span style=\"color: #bfbf7f; text-decoration-color: #bfbf7f\">Executing Task...</span>\n", "    └── <span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">🤖 Agent: </span><span style=\"color: #008000; text-decoration-color: #008000\">Data Analyst</span>\n", "        <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">    Status: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">In Progress</span>\n", "</pre>\n"]}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["\n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">\n", "</pre>\n"]}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["\u001b[1m\u001b[95m# Agent:\u001b[00m \u001b[1m\u001b[92mData Analyst\u001b[00m\n", "\u001b[95m## Task:\u001b[00m \u001b[92mCollect recent market data and identify trends.\u001b[00m\n"]}, {"output_type": "display_data", "data": {"text/plain": ["\u001b[1;32m🤖 Agent: \u001b[0m\u001b[32mData Analyst\u001b[0m\n", "\u001b[37m    Status: \u001b[0m\u001b[1;32mIn Progress\u001b[0m\n", "└── \u001b[1;34m🧠 \u001b[0m\u001b[34mThinking...\u001b[0m\n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">🤖 Agent: </span><span style=\"color: #008000; text-decoration-color: #008000\">Data Analyst</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">    Status: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">In Progress</span>\n", "└── <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">🧠 </span><span style=\"color: #000080; text-decoration-color: #000080\">Thinking...</span>\n", "</pre>\n"]}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["\n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">\n", "</pre>\n"]}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["\u001b[1;32m🤖 Agent: \u001b[0m\u001b[32mData Analyst\u001b[0m\n", "\u001b[37m    Status: \u001b[0m\u001b[1;32mIn Progress\u001b[0m\n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">🤖 Agent: </span><span style=\"color: #008000; text-decoration-color: #008000\">Data Analyst</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">    Status: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">In Progress</span>\n", "</pre>\n"]}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["\n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">\n", "</pre>\n"]}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["\n", "\n", "\u001b[1m\u001b[95m# Agent:\u001b[00m \u001b[1m\u001b[92mData Analyst\u001b[00m\n", "\u001b[95m## Final Answer:\u001b[00m \u001b[92m\n", "**Market Data Trends Report - October 2023**\n", "\n", "**1. Overview of Market Performance:**\n", "- The overall market has shown mixed results across different sectors in Q3 2023. Stock indices such as the S&P 500 and Dow Jones Industrial Average experienced moderate growth, with the S&P 500 increasing by approximately 5%, while the Dow Jones rose by 3%.\n", "\n", "**2. Key Sectors:**\n", "   - **Technology**: The technology sector continues to dominate the market with significant growth, driven by strong earnings reports from major players like Apple, Microsoft, and Tesla. The sector has increased by about 8% in Q3, reflecting a robust demand for cloud computing and AI-related solutions.\n", "   \n", "   - **Healthcare**: The healthcare sector has also shown resilience, rising around 4%. This trend is attributed to advancements in biotechnology and a continuous focus on healthcare services post-pandemic. Noteworthy developments in telehealth and drug development have piqued investor interest.\n", "   \n", "   - **Energy**: In contrast, the energy sector has faced challenges due to fluctuating oil prices and regulatory pressures. The sector decreased by 2% as global oil prices fell by 10% over the quarter, influenced by concerns over economic growth in China and higher production levels from OPEC countries.\n", "   \n", "   - **Consumer Discretionary**: This sector has shown signs of recovery, increasing by 6%. The resurgence in consumer spending, boosted by lower inflation rates, has played a pivotal role in this growth. Retail brands have reported positive sales growth, particularly in e-commerce.\n", "\n", "**3. Economic Indicators:**\n", "- **Inflation Control**: Recent government reports show that inflation rates have stabilized around 3.2%, prompting the Federal Reserve to maintain interest rates steady in light of current growth indicators. This steadying of inflation has helped improve consumer confidence.\n", "  \n", "- **Unemployment Rates**: Unemployment remains low at approximately 4.1%, indicating a healthy labor market. Job creation reports show strong hiring in the service sector, which has helped buoy spending levels.\n", "\n", "**4. Consumer Behavior:**\n", "- There has been a notable shift toward sustainable products and services, as consumers increasingly prioritize environmentally-friendly options. Companies that have incorporated sustainability into their business models are attracting greater attention and sales.\n", "  \n", "- E-commerce continues to rise, accounting for 18% of total retail sales in Q3, reflecting shifting consumer preferences amplified by the pandemic.\n", "\n", "**5. Geopolitical Influences:**\n", "- Ongoing geopolitical tensions, particularly concerning trade relations between the US and China, have created a backdrop of uncertainty for investors. Supply chain disruptions continue to pose challenges, especially for the manufacturing sector.\n", "\n", "**6. Future Outlook:**\n", "- Analysts predict that technology and consumer discretionary sectors will continue to lead growth through the end of 2023 and into 2024 as businesses invest in transformative technologies such as artificial intelligence and automation.\n", "  \n", "- However, potential risks from geopolitical uncertainties and economic slowdowns in key markets such as Europe and Asia should be monitored closely as they may influence market stability.\n", "\n", "**7. Conclusion:**\n", "In conclusion, the Q3 2023 market displays resilience with significant growth in select sectors amid macroeconomic uncertainties. The focus on technology, sustainability, and consumer confidence will likely drive trends as we move into the next quarter. Stakeholders should remain vigilant and adaptable, positioning themselves to leverage emerging opportunities while mitigating associated risks.\n", "\n", "This report provides a comprehensive view of current market trends and serves as a basis for strategic decision-making moving forward.\u001b[00m\n", "\n", "\n"]}, {"output_type": "display_data", "data": {"text/plain": ["\u001b[1;36m🚀 Crew: \u001b[0m\u001b[1;36mcrew\u001b[0m\n", "└── \u001b[1;33m📋 Task: a2a73614-d94b-407a-9564-6cc967f05215\u001b[0m\n", "    \u001b[37m   Status: \u001b[0m\u001b[2;33mExecuting Task...\u001b[0m\n", "    └── \u001b[1;32m🤖 Agent: \u001b[0m\u001b[32mData Analyst\u001b[0m\n", "        \u001b[37m    Status: \u001b[0m\u001b[1;32m✅ Completed\u001b[0m\n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">🚀 Crew: crew</span>\n", "└── <span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">📋 Task: a2a73614-d94b-407a-9564-6cc967f05215</span>\n", "    <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">   Status: </span><span style=\"color: #bfbf7f; text-decoration-color: #bfbf7f\">Executing Task...</span>\n", "    └── <span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">🤖 Agent: </span><span style=\"color: #008000; text-decoration-color: #008000\">Data Analyst</span>\n", "        <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">    Status: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">✅ Completed</span>\n", "</pre>\n"]}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["\n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">\n", "</pre>\n"]}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["\u001b[1;36m🚀 Crew: \u001b[0m\u001b[1;36mcrew\u001b[0m\n", "└── \u001b[1;32m📋 Task: a2a73614-d94b-407a-9564-6cc967f05215\u001b[0m\n", "    \u001b[37m   Assigned to: \u001b[0m\u001b[32mData Analyst\u001b[0m\n", "    \u001b[37m   Status: \u001b[0m\u001b[1;32m✅ Completed\u001b[0m\n", "    └── \u001b[1;32m🤖 Agent: \u001b[0m\u001b[32mData Analyst\u001b[0m\n", "        \u001b[37m    Status: \u001b[0m\u001b[1;32m✅ Completed\u001b[0m\n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">🚀 Crew: crew</span>\n", "└── <span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">📋 Task: a2a73614-d94b-407a-9564-6cc967f05215</span>\n", "    <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">   Assigned to: </span><span style=\"color: #008000; text-decoration-color: #008000\">Data Analyst</span>\n", "    <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">   Status: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">✅ Completed</span>\n", "    └── <span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">🤖 Agent: </span><span style=\"color: #008000; text-decoration-color: #008000\">Data Analyst</span>\n", "        <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">    Status: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">✅ Completed</span>\n", "</pre>\n"]}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["\u001b[32m╭─\u001b[0m\u001b[32m───────────────────────────────────────────────\u001b[0m\u001b[32m Task Completion \u001b[0m\u001b[32m───────────────────────────────────────────────\u001b[0m\u001b[32m─╮\u001b[0m\n", "\u001b[32m│\u001b[0m                                                                                                                 \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m  \u001b[1;32mTask Completed\u001b[0m                                                                                                 \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m  \u001b[37mName: \u001b[0m\u001b[32ma2a73614-d94b-407a-9564-6cc967f05215\u001b[0m                                                                     \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m  \u001b[37mAgent: \u001b[0m\u001b[32mData Analyst\u001b[0m                                                                                            \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m                                                                                                                 \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m                                                                                                                 \u001b[32m│\u001b[0m\n", "\u001b[32m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000\">╭──────────────────────────────────────────────── Task Completion ────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│</span>                                                                                                                 <span style=\"color: #008000; text-decoration-color: #008000\">│</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│</span>  <span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">Task Completed</span>                                                                                                 <span style=\"color: #008000; text-decoration-color: #008000\">│</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│</span>  <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">Name: </span><span style=\"color: #008000; text-decoration-color: #008000\">a2a73614-d94b-407a-9564-6cc967f05215</span>                                                                     <span style=\"color: #008000; text-decoration-color: #008000\">│</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│</span>  <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">Agent: </span><span style=\"color: #008000; text-decoration-color: #008000\">Data Analyst</span>                                                                                            <span style=\"color: #008000; text-decoration-color: #008000\">│</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│</span>                                                                                                                 <span style=\"color: #008000; text-decoration-color: #008000\">│</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│</span>                                                                                                                 <span style=\"color: #008000; text-decoration-color: #008000\">│</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"]}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["\n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">\n", "</pre>\n"]}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["\u001b[1;36m🚀 Crew: \u001b[0m\u001b[1;36mcrew\u001b[0m\n", "├── \u001b[1;32m📋 Task: a2a73614-d94b-407a-9564-6cc967f05215\u001b[0m\n", "│   \u001b[37m   Assigned to: \u001b[0m\u001b[32mData Analyst\u001b[0m\n", "│   \u001b[37m   Status: \u001b[0m\u001b[1;32m✅ Completed\u001b[0m\n", "│   └── \u001b[1;32m🤖 Agent: \u001b[0m\u001b[32mData Analyst\u001b[0m\n", "│       \u001b[37m    Status: \u001b[0m\u001b[1;32m✅ Completed\u001b[0m\n", "└── \u001b[1;33m📋 Task: 53df899d-9720-4c7f-911f-3b2fa479f5df\u001b[0m\n", "    \u001b[37m   Status: \u001b[0m\u001b[2;33mExecuting Task...\u001b[0m\n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">🚀 Crew: crew</span>\n", "├── <span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">📋 Task: a2a73614-d94b-407a-9564-6cc967f05215</span>\n", "│   <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">   Assigned to: </span><span style=\"color: #008000; text-decoration-color: #008000\">Data Analyst</span>\n", "│   <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">   Status: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">✅ Completed</span>\n", "│   └── <span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">🤖 Agent: </span><span style=\"color: #008000; text-decoration-color: #008000\">Data Analyst</span>\n", "│       <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">    Status: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">✅ Completed</span>\n", "└── <span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">📋 Task: 53df899d-9720-4c7f-911f-3b2fa479f5df</span>\n", "    <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">   Status: </span><span style=\"color: #bfbf7f; text-decoration-color: #bfbf7f\">Executing Task...</span>\n", "</pre>\n"]}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["\n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">\n", "</pre>\n"]}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["\u001b[1;36m🚀 Crew: \u001b[0m\u001b[1;36mcrew\u001b[0m\n", "├── \u001b[1;32m📋 Task: a2a73614-d94b-407a-9564-6cc967f05215\u001b[0m\n", "│   \u001b[37m   Assigned to: \u001b[0m\u001b[32mData Analyst\u001b[0m\n", "│   \u001b[37m   Status: \u001b[0m\u001b[1;32m✅ Completed\u001b[0m\n", "│   └── \u001b[1;32m🤖 Agent: \u001b[0m\u001b[32mData Analyst\u001b[0m\n", "│       \u001b[37m    Status: \u001b[0m\u001b[1;32m✅ Completed\u001b[0m\n", "└── \u001b[1;33m📋 Task: 53df899d-9720-4c7f-911f-3b2fa479f5df\u001b[0m\n", "    \u001b[37m   Status: \u001b[0m\u001b[2;33mExecuting Task...\u001b[0m\n", "    └── \u001b[1;32m🤖 Agent: \u001b[0m\u001b[32mMarket Researcher\u001b[0m\n", "        \u001b[37m    Status: \u001b[0m\u001b[1;32mIn Progress\u001b[0m\n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">🚀 Crew: crew</span>\n", "├── <span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">📋 Task: a2a73614-d94b-407a-9564-6cc967f05215</span>\n", "│   <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">   Assigned to: </span><span style=\"color: #008000; text-decoration-color: #008000\">Data Analyst</span>\n", "│   <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">   Status: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">✅ Completed</span>\n", "│   └── <span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">🤖 Agent: </span><span style=\"color: #008000; text-decoration-color: #008000\">Data Analyst</span>\n", "│       <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">    Status: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">✅ Completed</span>\n", "└── <span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">📋 Task: 53df899d-9720-4c7f-911f-3b2fa479f5df</span>\n", "    <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">   Status: </span><span style=\"color: #bfbf7f; text-decoration-color: #bfbf7f\">Executing Task...</span>\n", "    └── <span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">🤖 Agent: </span><span style=\"color: #008000; text-decoration-color: #008000\">Market Researcher</span>\n", "        <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">    Status: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">In Progress</span>\n", "</pre>\n"]}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["\n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">\n", "</pre>\n"]}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["\u001b[1m\u001b[95m# Agent:\u001b[00m \u001b[1m\u001b[92mMarket Researcher\u001b[00m\n", "\u001b[95m## Task:\u001b[00m \u001b[92mResearch factors affecting market dynamics.\u001b[00m\n", "\n", "\n", "\u001b[1m\u001b[95m# Agent:\u001b[00m \u001b[1m\u001b[92mMarket Researcher\u001b[00m\n", "\u001b[95m## Final Answer:\u001b[00m \u001b[92m\n", "The analysis of factors influencing market dynamics reveals a multifaceted landscape shaped by various economic, consumer, and geopolitical elements. \n", "\n", "**1. Economic Indicators**\n", "   - **Inflation and Interest Rates**: The stabilization of inflation at around 3.2% has allowed the Federal Reserve to maintain interest rates, contributing to enhanced consumer confidence. Stable inflation helps in reducing the cost of borrowing, thus fostering both consumer spending and business investments, which are vital for economic growth.\n", "   - **Employment Levels**: With unemployment rates at approximately 4.1%, the labor market appears robust. Strong job creation, particularly in the service sector, has the potential to increase disposable income, leading to higher consumer spending, particularly in discretionary sectors.\n", "\n", "**2. Sector Performance**\n", "   - **Technology Leadership**: The technology sector's growth of 8% in Q3, driven by earnings from major firms like Apple and Microsoft, underscores its pivotal role in the market. Demand for AI and cloud solutions is reshaping business operations, indicating an enduring reliance on technological advancements that propel growth and operational efficiency.\n", "   - **Healthcare Resilience**: The healthcare sector's recovery, rising by 4%, is attributed to ongoing innovation in biotechnology and continued demand for healthcare services. This sector's growth showcases the importance of health-related investments and the ongoing impact of pandemic-related shifts in consumer behavior.\n", "   - **Energy Sector Challenges**: In contrast, the energy sector's decline of 2% emphasizes vulnerability to external factors such as fluctuating global oil prices and regulatory challenges. The 10% drop in oil prices resonates with concerns about global economic slowdown, particularly in significant markets like China, making the energy sector susceptible to market volatility.\n", "   - **Consumer Discretionary Improvement**: The increase of 6% in the consumer discretionary sector signals a rebound in consumer expenditure, primarily due to falling inflation rates. This trend reflects a resurgence in retail, especially in e-commerce, which now constitutes 18% of total retail sales, highlighting significant shifts in consumer purchasing patterns.\n", "\n", "**3. Consumer Behavior Trends**\n", "   - **Sustainability Preferences**: A notable pivot towards sustainable goods arises from consumers' growing environmental consciousness. Enterprises integrating sustainable practices into their operations are seeing increased loyalty and sales, indicating that prioritizing eco-friendly options can enhance competitive advantage.\n", "   - **Rise of E-Commerce**: The pandemic has hastened the shift towards online shopping, a trend that continues to reshape how consumers engage with brands. Retailers that effectively leverage this trend are likely to significantly benefit from lasting changes in consumer purchasing habits.\n", "\n", "**4. Geopolitical Influences**\n", "   - **Trade Relations**: Ongoing tensions between the US and China introduce uncertainties that can affect investment decisions and market stability. Investors remain cautious due to potential supply chain disruptions that can adversely impact manufacturing, highlighting the need for strategic risk management in response to geopolitical developments.\n", "\n", "**5. Future Outlook and Strategic Implications**\n", "   - **Predicted Sector Growth**: Looking ahead, analysts forecast that sectors such as technology and consumer discretionary will remain at the forefront of market expansion as businesses invest in transformative technologies. Stakeholders should proactively position themselves to take advantage of these opportunities, recognizing that technological innovation and shifts in consumer preferences will play crucial roles.\n", "   - **Monitoring Economic and Geopolitical Risks**: However, vigilance is necessary concerning potential geopolitical uncertainties and economic fluctuations in crucial markets like Europe and Asia, as these could significantly affect market dynamics. Strategies should be devised to mitigate risks while capitalizing on emerging trends.\n", "\n", "In conclusion, the market dynamics of Q3 2023 illustrate resilience despite various challenges. A strong focus on technology and changing consumer behavior, particularly towards sustainability, will likely be driving forces leading into 2024. Stakeholders must remain adaptable and informed to navigate this evolving landscape effectively.\u001b[00m\n", "\n", "\n"]}, {"output_type": "display_data", "data": {"text/plain": ["\u001b[1;36m🚀 Crew: \u001b[0m\u001b[1;36mcrew\u001b[0m\n", "├── \u001b[1;32m📋 Task: a2a73614-d94b-407a-9564-6cc967f05215\u001b[0m\n", "│   \u001b[37m   Assigned to: \u001b[0m\u001b[32mData Analyst\u001b[0m\n", "│   \u001b[37m   Status: \u001b[0m\u001b[1;32m✅ Completed\u001b[0m\n", "│   └── \u001b[1;32m🤖 Agent: \u001b[0m\u001b[32mData Analyst\u001b[0m\n", "│       \u001b[37m    Status: \u001b[0m\u001b[1;32m✅ Completed\u001b[0m\n", "└── \u001b[1;33m📋 Task: 53df899d-9720-4c7f-911f-3b2fa479f5df\u001b[0m\n", "    \u001b[37m   Status: \u001b[0m\u001b[2;33mExecuting Task...\u001b[0m\n", "    └── \u001b[1;32m🤖 Agent: \u001b[0m\u001b[32mMarket Researcher\u001b[0m\n", "        \u001b[37m    Status: \u001b[0m\u001b[1;32m✅ Completed\u001b[0m\n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">🚀 Crew: crew</span>\n", "├── <span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">📋 Task: a2a73614-d94b-407a-9564-6cc967f05215</span>\n", "│   <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">   Assigned to: </span><span style=\"color: #008000; text-decoration-color: #008000\">Data Analyst</span>\n", "│   <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">   Status: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">✅ Completed</span>\n", "│   └── <span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">🤖 Agent: </span><span style=\"color: #008000; text-decoration-color: #008000\">Data Analyst</span>\n", "│       <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">    Status: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">✅ Completed</span>\n", "└── <span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">📋 Task: 53df899d-9720-4c7f-911f-3b2fa479f5df</span>\n", "    <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">   Status: </span><span style=\"color: #bfbf7f; text-decoration-color: #bfbf7f\">Executing Task...</span>\n", "    └── <span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">🤖 Agent: </span><span style=\"color: #008000; text-decoration-color: #008000\">Market Researcher</span>\n", "        <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">    Status: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">✅ Completed</span>\n", "</pre>\n"]}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["\n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">\n", "</pre>\n"]}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["\u001b[1;36m🚀 Crew: \u001b[0m\u001b[1;36mcrew\u001b[0m\n", "├── \u001b[1;32m📋 Task: a2a73614-d94b-407a-9564-6cc967f05215\u001b[0m\n", "│   \u001b[37m   Assigned to: \u001b[0m\u001b[32mData Analyst\u001b[0m\n", "│   \u001b[37m   Status: \u001b[0m\u001b[1;32m✅ Completed\u001b[0m\n", "│   └── \u001b[1;32m🤖 Agent: \u001b[0m\u001b[32mData Analyst\u001b[0m\n", "│       \u001b[37m    Status: \u001b[0m\u001b[1;32m✅ Completed\u001b[0m\n", "└── \u001b[1;32m📋 Task: 53df899d-9720-4c7f-911f-3b2fa479f5df\u001b[0m\n", "    \u001b[37m   Assigned to: \u001b[0m\u001b[32mMarket Researcher\u001b[0m\n", "    \u001b[37m   Status: \u001b[0m\u001b[1;32m✅ Completed\u001b[0m\n", "    └── \u001b[1;32m🤖 Agent: \u001b[0m\u001b[32mMarket Researcher\u001b[0m\n", "        \u001b[37m    Status: \u001b[0m\u001b[1;32m✅ Completed\u001b[0m\n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">🚀 Crew: crew</span>\n", "├── <span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">📋 Task: a2a73614-d94b-407a-9564-6cc967f05215</span>\n", "│   <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">   Assigned to: </span><span style=\"color: #008000; text-decoration-color: #008000\">Data Analyst</span>\n", "│   <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">   Status: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">✅ Completed</span>\n", "│   └── <span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">🤖 Agent: </span><span style=\"color: #008000; text-decoration-color: #008000\">Data Analyst</span>\n", "│       <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">    Status: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">✅ Completed</span>\n", "└── <span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">📋 Task: 53df899d-9720-4c7f-911f-3b2fa479f5df</span>\n", "    <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">   Assigned to: </span><span style=\"color: #008000; text-decoration-color: #008000\">Market Researcher</span>\n", "    <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">   Status: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">✅ Completed</span>\n", "    └── <span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">🤖 Agent: </span><span style=\"color: #008000; text-decoration-color: #008000\">Market Researcher</span>\n", "        <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">    Status: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">✅ Completed</span>\n", "</pre>\n"]}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["\u001b[32m╭─\u001b[0m\u001b[32m───────────────────────────────────────────────\u001b[0m\u001b[32m Task Completion \u001b[0m\u001b[32m───────────────────────────────────────────────\u001b[0m\u001b[32m─╮\u001b[0m\n", "\u001b[32m│\u001b[0m                                                                                                                 \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m  \u001b[1;32mTask Completed\u001b[0m                                                                                                 \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m  \u001b[37mName: \u001b[0m\u001b[32m53df899d-9720-4c7f-911f-3b2fa479f5df\u001b[0m                                                                     \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m  \u001b[37mAgent: \u001b[0m\u001b[32mMarket Researcher\u001b[0m                                                                                       \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m                                                                                                                 \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m                                                                                                                 \u001b[32m│\u001b[0m\n", "\u001b[32m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000\">╭──────────────────────────────────────────────── Task Completion ────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│</span>                                                                                                                 <span style=\"color: #008000; text-decoration-color: #008000\">│</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│</span>  <span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">Task Completed</span>                                                                                                 <span style=\"color: #008000; text-decoration-color: #008000\">│</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│</span>  <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">Name: </span><span style=\"color: #008000; text-decoration-color: #008000\">53df899d-9720-4c7f-911f-3b2fa479f5df</span>                                                                     <span style=\"color: #008000; text-decoration-color: #008000\">│</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│</span>  <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">Agent: </span><span style=\"color: #008000; text-decoration-color: #008000\">Market Researcher</span>                                                                                       <span style=\"color: #008000; text-decoration-color: #008000\">│</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│</span>                                                                                                                 <span style=\"color: #008000; text-decoration-color: #008000\">│</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│</span>                                                                                                                 <span style=\"color: #008000; text-decoration-color: #008000\">│</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"]}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["\n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">\n", "</pre>\n"]}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["\u001b[32m╭─\u001b[0m\u001b[32m───────────────────────────────────────────────\u001b[0m\u001b[32m Crew Completion \u001b[0m\u001b[32m───────────────────────────────────────────────\u001b[0m\u001b[32m─╮\u001b[0m\n", "\u001b[32m│\u001b[0m                                                                                                                 \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m  \u001b[1;32mCrew Execution Completed\u001b[0m                                                                                       \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m  \u001b[37mName: \u001b[0m\u001b[32mcrew\u001b[0m                                                                                                     \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m  \u001b[37mID: \u001b[0m\u001b[32m03bc2b16-db94-4c32-8d0c-70c68ab86e51\u001b[0m                                                                       \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m                                                                                                                 \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m                                                                                                                 \u001b[32m│\u001b[0m\n", "\u001b[32m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000\">╭──────────────────────────────────────────────── Crew Completion ────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│</span>                                                                                                                 <span style=\"color: #008000; text-decoration-color: #008000\">│</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│</span>  <span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">Crew Execution Completed</span>                                                                                       <span style=\"color: #008000; text-decoration-color: #008000\">│</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│</span>  <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">Name: </span><span style=\"color: #008000; text-decoration-color: #008000\">crew</span>                                                                                                     <span style=\"color: #008000; text-decoration-color: #008000\">│</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│</span>  <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">ID: </span><span style=\"color: #008000; text-decoration-color: #008000\">03bc2b16-db94-4c32-8d0c-70c68ab86e51</span>                                                                       <span style=\"color: #008000; text-decoration-color: #008000\">│</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│</span>                                                                                                                 <span style=\"color: #008000; text-decoration-color: #008000\">│</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│</span>                                                                                                                 <span style=\"color: #008000; text-decoration-color: #008000\">│</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"]}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["\n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">\n", "</pre>\n"]}, "metadata": {}}, {"output_type": "stream", "name": "stderr", "text": ["OPIK: Started logging traces to the \"crewai-integration-demo\" project at https://www.comet.com/opik/api/v1/session/redirect/projects/?trace_id=01964d7d-57fa-7a71-b791-784fa05055a1&path=aHR0cHM6Ly93d3cuY29tZXQuY29tL29waWsvYXBpLw==.\n"]}, {"output_type": "stream", "name": "stdout", "text": ["The analysis of factors influencing market dynamics reveals a multifaceted landscape shaped by various economic, consumer, and geopolitical elements. \n", "\n", "**1. Economic Indicators**\n", "   - **Inflation and Interest Rates**: The stabilization of inflation at around 3.2% has allowed the Federal Reserve to maintain interest rates, contributing to enhanced consumer confidence. Stable inflation helps in reducing the cost of borrowing, thus fostering both consumer spending and business investments, which are vital for economic growth.\n", "   - **Employment Levels**: With unemployment rates at approximately 4.1%, the labor market appears robust. Strong job creation, particularly in the service sector, has the potential to increase disposable income, leading to higher consumer spending, particularly in discretionary sectors.\n", "\n", "**2. Sector Performance**\n", "   - **Technology Leadership**: The technology sector's growth of 8% in Q3, driven by earnings from major firms like Apple and Microsoft, underscores its pivotal role in the market. Demand for AI and cloud solutions is reshaping business operations, indicating an enduring reliance on technological advancements that propel growth and operational efficiency.\n", "   - **Healthcare Resilience**: The healthcare sector's recovery, rising by 4%, is attributed to ongoing innovation in biotechnology and continued demand for healthcare services. This sector's growth showcases the importance of health-related investments and the ongoing impact of pandemic-related shifts in consumer behavior.\n", "   - **Energy Sector Challenges**: In contrast, the energy sector's decline of 2% emphasizes vulnerability to external factors such as fluctuating global oil prices and regulatory challenges. The 10% drop in oil prices resonates with concerns about global economic slowdown, particularly in significant markets like China, making the energy sector susceptible to market volatility.\n", "   - **Consumer Discretionary Improvement**: The increase of 6% in the consumer discretionary sector signals a rebound in consumer expenditure, primarily due to falling inflation rates. This trend reflects a resurgence in retail, especially in e-commerce, which now constitutes 18% of total retail sales, highlighting significant shifts in consumer purchasing patterns.\n", "\n", "**3. Consumer Behavior Trends**\n", "   - **Sustainability Preferences**: A notable pivot towards sustainable goods arises from consumers' growing environmental consciousness. Enterprises integrating sustainable practices into their operations are seeing increased loyalty and sales, indicating that prioritizing eco-friendly options can enhance competitive advantage.\n", "   - **Rise of E-Commerce**: The pandemic has hastened the shift towards online shopping, a trend that continues to reshape how consumers engage with brands. Retailers that effectively leverage this trend are likely to significantly benefit from lasting changes in consumer purchasing habits.\n", "\n", "**4. Geopolitical Influences**\n", "   - **Trade Relations**: Ongoing tensions between the US and China introduce uncertainties that can affect investment decisions and market stability. Investors remain cautious due to potential supply chain disruptions that can adversely impact manufacturing, highlighting the need for strategic risk management in response to geopolitical developments.\n", "\n", "**5. Future Outlook and Strategic Implications**\n", "   - **Predicted Sector Growth**: Looking ahead, analysts forecast that sectors such as technology and consumer discretionary will remain at the forefront of market expansion as businesses invest in transformative technologies. Stakeholders should proactively position themselves to take advantage of these opportunities, recognizing that technological innovation and shifts in consumer preferences will play crucial roles.\n", "   - **Monitoring Economic and Geopolitical Risks**: However, vigilance is necessary concerning potential geopolitical uncertainties and economic fluctuations in crucial markets like Europe and Asia, as these could significantly affect market dynamics. Strategies should be devised to mitigate risks while capitalizing on emerging trends.\n", "\n", "In conclusion, the market dynamics of Q3 2023 illustrate resilience despite various challenges. A strong focus on technology and changing consumer behavior, particularly towards sustainability, will likely be driving forces leading into 2024. Stakeholders must remain adaptable and informed to navigate this evolving landscape effectively.\n"]}], "source": ["from opik.integrations.crewai import track_crewai\n", "\n", "track_crewai(project_name=\"crewai-integration-demo\")\n", "\n", "my_crew = YourCrewName().crew()\n", "result = my_crew.kickoff()\n", "\n", "print(result)"]}, {"cell_type": "markdown", "metadata": {"id": "iOkkYwFK4zZt"}, "source": ["You can now go to the Opik app to see the trace:\n", "\n", "![CrewAI trace in Opik](https://raw.githubusercontent.com/comet-ml/opik/main/apps/opik-documentation/documentation/fern/img/cookbook/crewai_trace_cookbook.png)"]}], "metadata": {"kernelspec": {"display_name": "py312_llm_eval", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}, "colab": {"provenance": [], "toc_visible": true}}, "nbformat": 4, "nbformat_minor": 0}