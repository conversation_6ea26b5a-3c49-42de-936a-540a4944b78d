{"cells": [{"cell_type": "code", "execution_count": null, "id": "2c76b148", "metadata": {}, "outputs": [], "source": ["import nest_asyncio\n", "\n", "nest_asyncio.apply()"]}, {"cell_type": "code", "execution_count": null, "id": "ff2c4875", "metadata": {}, "outputs": [], "source": ["from crewai import Crew, Agent, Task, Process\n", "\n", "# Define a simple agent (assuming a default LLM agent)\n", "assistant = Agent(role=\"Personal Assistant\",\n", "                  goal=\"\"\"You are a personal assistant that can\n", "                          help the user with their tasks.\"\"\",\n", "                  backstory=\"\"\"You are a personal assistant that\n", "                               can help the user with their tasks.\"\"\",\n", "                  verbose=True)\n", "\n", "task = Task(description=\"Handle this task: {user_task}\",\n", "            expected_output=\"A clear and concise answer to the question.\",\n", "            agent=assistant)\n", "\n", "# Create a crew with memory enabled\n", "crew = Crew(\n", "    agents=[assistant], \n", "    tasks=[task], \n", "    process=Process.sequential,\n", "    verbose=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "2c6c0251", "metadata": {}, "outputs": [], "source": ["user_input = \"\"\"My favorite color is #46778F and\n", "                my favorite Agent framework is CrewAI.\"\"\"\n", "\n", "result = crew.kickoff(inputs={\"user_task\": user_input})"]}, {"cell_type": "code", "execution_count": null, "id": "be50b014", "metadata": {}, "outputs": [], "source": ["user_input = \"What is my favorite color?\"\n", "\n", "result = crew.kickoff(inputs={\"user_task\": user_input})"]}, {"cell_type": "markdown", "id": "280489d7", "metadata": {}, "source": ["### Short Term Memory"]}, {"cell_type": "code", "execution_count": 32, "id": "b8b9c030", "metadata": {}, "outputs": [], "source": ["from crewai import Crew, Agent, Task, Process\n", "\n", "# Define a simple agent (assuming a default LLM agent)\n", "assistant = Agent(role=\"Personal Assistant\",\n", "                  goal=\"\"\"You are a personal assistant that can\n", "                          help the user with their tasks.\"\"\",\n", "                  backstory=\"\"\"You are a personal assistant that\n", "                               can help the user with their tasks.\"\"\",\n", "                  verbose=True)\n", "\n", "task = Task(description=\"Handle this task: {user_task}\",\n", "            expected_output=\"A clear and concise answer to the question.\",\n", "            agent=assistant)\n", "\n", "# Create a crew with memory enabled\n", "crew = Crew(\n", "    agents=[assistant], \n", "    tasks=[task], \n", "    process=Process.sequential,\n", "    memory=True,\n", "    verbose=True\n", ")"]}, {"cell_type": "code", "execution_count": 33, "id": "14690295", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080\">╭──────────────────────────────────────────── Crew Execution Started ─────────────────────────────────────────────╮</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080\">│</span>                                                                                                                 <span style=\"color: #008080; text-decoration-color: #008080\">│</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080\">│</span>  <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">Crew Execution Started</span>                                                                                         <span style=\"color: #008080; text-decoration-color: #008080\">│</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080\">│</span>  <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">Name: </span><span style=\"color: #008080; text-decoration-color: #008080\">crew</span>                                                                                                     <span style=\"color: #008080; text-decoration-color: #008080\">│</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080\">│</span>  <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">ID: </span><span style=\"color: #008080; text-decoration-color: #008080\">e0b9bdcd-8f00-4871-a4c7-81ec189b71aa</span>                                                                       <span style=\"color: #008080; text-decoration-color: #008080\">│</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080\">│</span>                                                                                                                 <span style=\"color: #008080; text-decoration-color: #008080\">│</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080\">│</span>                                                                                                                 <span style=\"color: #008080; text-decoration-color: #008080\">│</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[36m╭─\u001b[0m\u001b[36m───────────────────────────────────────────\u001b[0m\u001b[36m Crew Execution Started \u001b[0m\u001b[36m────────────────────────────────────────────\u001b[0m\u001b[36m─╮\u001b[0m\n", "\u001b[36m│\u001b[0m                                                                                                                 \u001b[36m│\u001b[0m\n", "\u001b[36m│\u001b[0m  \u001b[1;36mCrew Execution Started\u001b[0m                                                                                         \u001b[36m│\u001b[0m\n", "\u001b[36m│\u001b[0m  \u001b[37mName: \u001b[0m\u001b[36mcrew\u001b[0m                                                                                                     \u001b[36m│\u001b[0m\n", "\u001b[36m│\u001b[0m  \u001b[37mID: \u001b[0m\u001b[36me0b9bdcd-8f00-4871-a4c7-81ec189b71aa\u001b[0m                                                                       \u001b[36m│\u001b[0m\n", "\u001b[36m│\u001b[0m                                                                                                                 \u001b[36m│\u001b[0m\n", "\u001b[36m│\u001b[0m                                                                                                                 \u001b[36m│\u001b[0m\n", "\u001b[36m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">\n", "</pre>\n"], "text/plain": ["\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">🚀 Crew: crew</span>\n", "└── <span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">📋 Task: 449e3418-9ee5-4cb8-884e-8f2a2f506b0e</span>\n", "    <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">   Status: </span><span style=\"color: #bfbf7f; text-decoration-color: #bfbf7f\">Executing Task...</span>\n", "</pre>\n"], "text/plain": ["\u001b[1;36m🚀 Crew: \u001b[0m\u001b[1;36mcrew\u001b[0m\n", "└── \u001b[1;33m📋 Task: 449e3418-9ee5-4cb8-884e-8f2a2f506b0e\u001b[0m\n", "    \u001b[37m   Status: \u001b[0m\u001b[2;33mExecuting Task...\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">\n", "</pre>\n"], "text/plain": ["\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">🚀 Crew: crew</span>\n", "└── <span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">📋 Task: 449e3418-9ee5-4cb8-884e-8f2a2f506b0e</span>\n", "    <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">   Status: </span><span style=\"color: #bfbf7f; text-decoration-color: #bfbf7f\">Executing Task...</span>\n", "    └── <span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">🤖 Agent: </span><span style=\"color: #008000; text-decoration-color: #008000\">Personal Assistant</span>\n", "        <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">    Status: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">In Progress</span>\n", "</pre>\n"], "text/plain": ["\u001b[1;36m🚀 Crew: \u001b[0m\u001b[1;36mcrew\u001b[0m\n", "└── \u001b[1;33m📋 Task: 449e3418-9ee5-4cb8-884e-8f2a2f506b0e\u001b[0m\n", "    \u001b[37m   Status: \u001b[0m\u001b[2;33mExecuting Task...\u001b[0m\n", "    └── \u001b[1;32m🤖 Agent: \u001b[0m\u001b[32mPersonal Assistant\u001b[0m\n", "        \u001b[37m    Status: \u001b[0m\u001b[1;32mIn Progress\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">\n", "</pre>\n"], "text/plain": ["\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m\u001b[95m# Agent:\u001b[00m \u001b[1m\u001b[92mPersonal Assistant\u001b[00m\n", "\u001b[95m## Task:\u001b[00m \u001b[92mHandle this task: My favorite color is #46778F and\n", "                my favorite Agent framework is CrewAI.\u001b[00m\n", "\n", "\n", "\u001b[1m\u001b[95m# Agent:\u001b[00m \u001b[1m\u001b[92mPersonal Assistant\u001b[00m\n", "\u001b[95m## Final Answer:\u001b[00m \u001b[92m\n", "Your favorite color is #46778F and your favorite agent framework is CrewAI.\u001b[00m\n", "\n", "\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">🚀 Crew: crew</span>\n", "└── <span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">📋 Task: 449e3418-9ee5-4cb8-884e-8f2a2f506b0e</span>\n", "    <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">   Status: </span><span style=\"color: #bfbf7f; text-decoration-color: #bfbf7f\">Executing Task...</span>\n", "    └── <span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">🤖 Agent: </span><span style=\"color: #008000; text-decoration-color: #008000\">Personal Assistant</span>\n", "        <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">    Status: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">✅ Completed</span>\n", "</pre>\n"], "text/plain": ["\u001b[1;36m🚀 Crew: \u001b[0m\u001b[1;36mcrew\u001b[0m\n", "└── \u001b[1;33m📋 Task: 449e3418-9ee5-4cb8-884e-8f2a2f506b0e\u001b[0m\n", "    \u001b[37m   Status: \u001b[0m\u001b[2;33mExecuting Task...\u001b[0m\n", "    └── \u001b[1;32m🤖 Agent: \u001b[0m\u001b[32mPersonal Assistant\u001b[0m\n", "        \u001b[37m    Status: \u001b[0m\u001b[1;32m✅ Completed\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">\n", "</pre>\n"], "text/plain": ["\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">🚀 Crew: crew</span>\n", "└── <span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">📋 Task: 449e3418-9ee5-4cb8-884e-8f2a2f506b0e</span>\n", "    <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">   Assigned to: </span><span style=\"color: #008000; text-decoration-color: #008000\">Personal Assistant</span>\n", "    <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">   Status: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">✅ Completed</span>\n", "    └── <span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">🤖 Agent: </span><span style=\"color: #008000; text-decoration-color: #008000\">Personal Assistant</span>\n", "        <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">    Status: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">✅ Completed</span>\n", "</pre>\n"], "text/plain": ["\u001b[1;36m🚀 Crew: \u001b[0m\u001b[1;36mcrew\u001b[0m\n", "└── \u001b[1;32m📋 Task: 449e3418-9ee5-4cb8-884e-8f2a2f506b0e\u001b[0m\n", "    \u001b[37m   Assigned to: \u001b[0m\u001b[32mPersonal Assistant\u001b[0m\n", "    \u001b[37m   Status: \u001b[0m\u001b[1;32m✅ Completed\u001b[0m\n", "    └── \u001b[1;32m🤖 Agent: \u001b[0m\u001b[32mPersonal Assistant\u001b[0m\n", "        \u001b[37m    Status: \u001b[0m\u001b[1;32m✅ Completed\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000\">╭──────────────────────────────────────────────── Task Completion ────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│</span>                                                                                                                 <span style=\"color: #008000; text-decoration-color: #008000\">│</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│</span>  <span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">Task Completed</span>                                                                                                 <span style=\"color: #008000; text-decoration-color: #008000\">│</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│</span>  <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">Name: </span><span style=\"color: #008000; text-decoration-color: #008000\">449e3418-9ee5-4cb8-884e-8f2a2f506b0e</span>                                                                     <span style=\"color: #008000; text-decoration-color: #008000\">│</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│</span>  <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">Agent: </span><span style=\"color: #008000; text-decoration-color: #008000\">Personal Assistant</span>                                                                                      <span style=\"color: #008000; text-decoration-color: #008000\">│</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│</span>                                                                                                                 <span style=\"color: #008000; text-decoration-color: #008000\">│</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│</span>                                                                                                                 <span style=\"color: #008000; text-decoration-color: #008000\">│</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[32m╭─\u001b[0m\u001b[32m───────────────────────────────────────────────\u001b[0m\u001b[32m Task Completion \u001b[0m\u001b[32m───────────────────────────────────────────────\u001b[0m\u001b[32m─╮\u001b[0m\n", "\u001b[32m│\u001b[0m                                                                                                                 \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m  \u001b[1;32mTask Completed\u001b[0m                                                                                                 \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m  \u001b[37mName: \u001b[0m\u001b[32m449e3418-9ee5-4cb8-884e-8f2a2f506b0e\u001b[0m                                                                     \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m  \u001b[37mAgent: \u001b[0m\u001b[32mPersonal Assistant\u001b[0m                                                                                      \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m                                                                                                                 \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m                                                                                                                 \u001b[32m│\u001b[0m\n", "\u001b[32m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">\n", "</pre>\n"], "text/plain": ["\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000\">╭──────────────────────────────────────────────── Crew Completion ────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│</span>                                                                                                                 <span style=\"color: #008000; text-decoration-color: #008000\">│</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│</span>  <span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">Crew Execution Completed</span>                                                                                       <span style=\"color: #008000; text-decoration-color: #008000\">│</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│</span>  <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">Name: </span><span style=\"color: #008000; text-decoration-color: #008000\">crew</span>                                                                                                     <span style=\"color: #008000; text-decoration-color: #008000\">│</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│</span>  <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">ID: </span><span style=\"color: #008000; text-decoration-color: #008000\">e0b9bdcd-8f00-4871-a4c7-81ec189b71aa</span>                                                                       <span style=\"color: #008000; text-decoration-color: #008000\">│</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│</span>                                                                                                                 <span style=\"color: #008000; text-decoration-color: #008000\">│</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│</span>                                                                                                                 <span style=\"color: #008000; text-decoration-color: #008000\">│</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[32m╭─\u001b[0m\u001b[32m───────────────────────────────────────────────\u001b[0m\u001b[32m Crew Completion \u001b[0m\u001b[32m───────────────────────────────────────────────\u001b[0m\u001b[32m─╮\u001b[0m\n", "\u001b[32m│\u001b[0m                                                                                                                 \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m  \u001b[1;32mCrew Execution Completed\u001b[0m                                                                                       \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m  \u001b[37mName: \u001b[0m\u001b[32mcrew\u001b[0m                                                                                                     \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m  \u001b[37mID: \u001b[0m\u001b[32me0b9bdcd-8f00-4871-a4c7-81ec189b71aa\u001b[0m                                                                       \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m                                                                                                                 \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m                                                                                                                 \u001b[32m│\u001b[0m\n", "\u001b[32m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">\n", "</pre>\n"], "text/plain": ["\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["user_input = \"\"\"My favorite color is #46778F and\n", "                my favorite Agent framework is CrewAI.\"\"\"\n", "\n", "result = crew.kickoff(inputs={\"user_task\": user_input})"]}, {"cell_type": "code", "execution_count": 34, "id": "42ea070d", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080\">╭──────────────────────────────────────────── Crew Execution Started ─────────────────────────────────────────────╮</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080\">│</span>                                                                                                                 <span style=\"color: #008080; text-decoration-color: #008080\">│</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080\">│</span>  <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">Crew Execution Started</span>                                                                                         <span style=\"color: #008080; text-decoration-color: #008080\">│</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080\">│</span>  <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">Name: </span><span style=\"color: #008080; text-decoration-color: #008080\">crew</span>                                                                                                     <span style=\"color: #008080; text-decoration-color: #008080\">│</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080\">│</span>  <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">ID: </span><span style=\"color: #008080; text-decoration-color: #008080\">e0b9bdcd-8f00-4871-a4c7-81ec189b71aa</span>                                                                       <span style=\"color: #008080; text-decoration-color: #008080\">│</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080\">│</span>                                                                                                                 <span style=\"color: #008080; text-decoration-color: #008080\">│</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080\">│</span>                                                                                                                 <span style=\"color: #008080; text-decoration-color: #008080\">│</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[36m╭─\u001b[0m\u001b[36m───────────────────────────────────────────\u001b[0m\u001b[36m Crew Execution Started \u001b[0m\u001b[36m────────────────────────────────────────────\u001b[0m\u001b[36m─╮\u001b[0m\n", "\u001b[36m│\u001b[0m                                                                                                                 \u001b[36m│\u001b[0m\n", "\u001b[36m│\u001b[0m  \u001b[1;36mCrew Execution Started\u001b[0m                                                                                         \u001b[36m│\u001b[0m\n", "\u001b[36m│\u001b[0m  \u001b[37mName: \u001b[0m\u001b[36mcrew\u001b[0m                                                                                                     \u001b[36m│\u001b[0m\n", "\u001b[36m│\u001b[0m  \u001b[37mID: \u001b[0m\u001b[36me0b9bdcd-8f00-4871-a4c7-81ec189b71aa\u001b[0m                                                                       \u001b[36m│\u001b[0m\n", "\u001b[36m│\u001b[0m                                                                                                                 \u001b[36m│\u001b[0m\n", "\u001b[36m│\u001b[0m                                                                                                                 \u001b[36m│\u001b[0m\n", "\u001b[36m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">\n", "</pre>\n"], "text/plain": ["\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">🚀 Crew: crew</span>\n", "└── <span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">📋 Task: 449e3418-9ee5-4cb8-884e-8f2a2f506b0e</span>\n", "    <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">   Status: </span><span style=\"color: #bfbf7f; text-decoration-color: #bfbf7f\">Executing Task...</span>\n", "</pre>\n"], "text/plain": ["\u001b[1;36m🚀 Crew: \u001b[0m\u001b[1;36mcrew\u001b[0m\n", "└── \u001b[1;33m📋 Task: 449e3418-9ee5-4cb8-884e-8f2a2f506b0e\u001b[0m\n", "    \u001b[37m   Status: \u001b[0m\u001b[2;33mExecuting Task...\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">\n", "</pre>\n"], "text/plain": ["\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">🚀 Crew: crew</span>\n", "└── <span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">📋 Task: 449e3418-9ee5-4cb8-884e-8f2a2f506b0e</span>\n", "    <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">   Status: </span><span style=\"color: #bfbf7f; text-decoration-color: #bfbf7f\">Executing Task...</span>\n", "    └── <span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">🤖 Agent: </span><span style=\"color: #008000; text-decoration-color: #008000\">Personal Assistant</span>\n", "        <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">    Status: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">In Progress</span>\n", "</pre>\n"], "text/plain": ["\u001b[1;36m🚀 Crew: \u001b[0m\u001b[1;36mcrew\u001b[0m\n", "└── \u001b[1;33m📋 Task: 449e3418-9ee5-4cb8-884e-8f2a2f506b0e\u001b[0m\n", "    \u001b[37m   Status: \u001b[0m\u001b[2;33mExecuting Task...\u001b[0m\n", "    └── \u001b[1;32m🤖 Agent: \u001b[0m\u001b[32mPersonal Assistant\u001b[0m\n", "        \u001b[37m    Status: \u001b[0m\u001b[1;32mIn Progress\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">\n", "</pre>\n"], "text/plain": ["\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m\u001b[95m# Agent:\u001b[00m \u001b[1m\u001b[92mPersonal Assistant\u001b[00m\n", "\u001b[95m## Task:\u001b[00m \u001b[92mHandle this task: What is my favorite color?\u001b[00m\n", "\n", "\n", "\u001b[1m\u001b[95m# Agent:\u001b[00m \u001b[1m\u001b[92mPersonal Assistant\u001b[00m\n", "\u001b[95m## Final Answer:\u001b[00m \u001b[92m\n", "Your favorite color is #46778F and your favorite Agent framework is CrewAI.\u001b[00m\n", "\n", "\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">🚀 Crew: crew</span>\n", "└── <span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">📋 Task: 449e3418-9ee5-4cb8-884e-8f2a2f506b0e</span>\n", "    <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">   Status: </span><span style=\"color: #bfbf7f; text-decoration-color: #bfbf7f\">Executing Task...</span>\n", "    └── <span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">🤖 Agent: </span><span style=\"color: #008000; text-decoration-color: #008000\">Personal Assistant</span>\n", "        <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">    Status: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">✅ Completed</span>\n", "</pre>\n"], "text/plain": ["\u001b[1;36m🚀 Crew: \u001b[0m\u001b[1;36mcrew\u001b[0m\n", "└── \u001b[1;33m📋 Task: 449e3418-9ee5-4cb8-884e-8f2a2f506b0e\u001b[0m\n", "    \u001b[37m   Status: \u001b[0m\u001b[2;33mExecuting Task...\u001b[0m\n", "    └── \u001b[1;32m🤖 Agent: \u001b[0m\u001b[32mPersonal Assistant\u001b[0m\n", "        \u001b[37m    Status: \u001b[0m\u001b[1;32m✅ Completed\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">\n", "</pre>\n"], "text/plain": ["\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">🚀 Crew: crew</span>\n", "└── <span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">📋 Task: 449e3418-9ee5-4cb8-884e-8f2a2f506b0e</span>\n", "    <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">   Assigned to: </span><span style=\"color: #008000; text-decoration-color: #008000\">Personal Assistant</span>\n", "    <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">   Status: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">✅ Completed</span>\n", "    └── <span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">🤖 Agent: </span><span style=\"color: #008000; text-decoration-color: #008000\">Personal Assistant</span>\n", "        <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">    Status: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">✅ Completed</span>\n", "</pre>\n"], "text/plain": ["\u001b[1;36m🚀 Crew: \u001b[0m\u001b[1;36mcrew\u001b[0m\n", "└── \u001b[1;32m📋 Task: 449e3418-9ee5-4cb8-884e-8f2a2f506b0e\u001b[0m\n", "    \u001b[37m   Assigned to: \u001b[0m\u001b[32mPersonal Assistant\u001b[0m\n", "    \u001b[37m   Status: \u001b[0m\u001b[1;32m✅ Completed\u001b[0m\n", "    └── \u001b[1;32m🤖 Agent: \u001b[0m\u001b[32mPersonal Assistant\u001b[0m\n", "        \u001b[37m    Status: \u001b[0m\u001b[1;32m✅ Completed\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000\">╭──────────────────────────────────────────────── Task Completion ────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│</span>                                                                                                                 <span style=\"color: #008000; text-decoration-color: #008000\">│</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│</span>  <span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">Task Completed</span>                                                                                                 <span style=\"color: #008000; text-decoration-color: #008000\">│</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│</span>  <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">Name: </span><span style=\"color: #008000; text-decoration-color: #008000\">449e3418-9ee5-4cb8-884e-8f2a2f506b0e</span>                                                                     <span style=\"color: #008000; text-decoration-color: #008000\">│</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│</span>  <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">Agent: </span><span style=\"color: #008000; text-decoration-color: #008000\">Personal Assistant</span>                                                                                      <span style=\"color: #008000; text-decoration-color: #008000\">│</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│</span>                                                                                                                 <span style=\"color: #008000; text-decoration-color: #008000\">│</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│</span>                                                                                                                 <span style=\"color: #008000; text-decoration-color: #008000\">│</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[32m╭─\u001b[0m\u001b[32m───────────────────────────────────────────────\u001b[0m\u001b[32m Task Completion \u001b[0m\u001b[32m───────────────────────────────────────────────\u001b[0m\u001b[32m─╮\u001b[0m\n", "\u001b[32m│\u001b[0m                                                                                                                 \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m  \u001b[1;32mTask Completed\u001b[0m                                                                                                 \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m  \u001b[37mName: \u001b[0m\u001b[32m449e3418-9ee5-4cb8-884e-8f2a2f506b0e\u001b[0m                                                                     \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m  \u001b[37mAgent: \u001b[0m\u001b[32mPersonal Assistant\u001b[0m                                                                                      \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m                                                                                                                 \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m                                                                                                                 \u001b[32m│\u001b[0m\n", "\u001b[32m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">\n", "</pre>\n"], "text/plain": ["\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000\">╭──────────────────────────────────────────────── Crew Completion ────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│</span>                                                                                                                 <span style=\"color: #008000; text-decoration-color: #008000\">│</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│</span>  <span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">Crew Execution Completed</span>                                                                                       <span style=\"color: #008000; text-decoration-color: #008000\">│</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│</span>  <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">Name: </span><span style=\"color: #008000; text-decoration-color: #008000\">crew</span>                                                                                                     <span style=\"color: #008000; text-decoration-color: #008000\">│</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│</span>  <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">ID: </span><span style=\"color: #008000; text-decoration-color: #008000\">e0b9bdcd-8f00-4871-a4c7-81ec189b71aa</span>                                                                       <span style=\"color: #008000; text-decoration-color: #008000\">│</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│</span>                                                                                                                 <span style=\"color: #008000; text-decoration-color: #008000\">│</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">│</span>                                                                                                                 <span style=\"color: #008000; text-decoration-color: #008000\">│</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[32m╭─\u001b[0m\u001b[32m───────────────────────────────────────────────\u001b[0m\u001b[32m Crew Completion \u001b[0m\u001b[32m───────────────────────────────────────────────\u001b[0m\u001b[32m─╮\u001b[0m\n", "\u001b[32m│\u001b[0m                                                                                                                 \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m  \u001b[1;32mCrew Execution Completed\u001b[0m                                                                                       \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m  \u001b[37mName: \u001b[0m\u001b[32mcrew\u001b[0m                                                                                                     \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m  \u001b[37mID: \u001b[0m\u001b[32me0b9bdcd-8f00-4871-a4c7-81ec189b71aa\u001b[0m                                                                       \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m                                                                                                                 \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m                                                                                                                 \u001b[32m│\u001b[0m\n", "\u001b[32m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">\n", "</pre>\n"], "text/plain": ["\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["user_input = \"What is my favorite color?\"\n", "\n", "result = crew.kickoff(inputs={\"user_task\": user_input})"]}, {"cell_type": "code", "execution_count": 35, "id": "d7f933b7", "metadata": {}, "outputs": [{"data": {"text/plain": ["ShortTermMemory(embedder_config=None, crew=None, storage=<crewai.memory.storage.rag_storage.RAGStorage object at 0x125ecb5c0>)"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["crew._short_term_memory"]}, {"cell_type": "code", "execution_count": 36, "id": "bff29ba9", "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'id': '12276a9f-fd27-4c32-90a6-bb6a0c3ceffe',\n", "  'metadata': {'agent': 'Personal Assistant',\n", "   'observation': 'Handle this task: My favorite color is #46778F and\\n                my favorite Agent framework is CrewAI.'},\n", "  'context': 'I now can give a great answer  \\nFinal Answer: Your favorite color is #46778F and your favorite Agent framework is CrewAI.',\n", "  'score': 0.890435395010837},\n", " {'id': 'dc5f9303-a10c-405d-b66d-8dad334c9a86',\n", "  'metadata': {'agent': 'Personal Assistant',\n", "   'observation': 'Handle this task: What is my favorite color?'},\n", "  'context': 'I now can give a great answer  \\nFinal Answer: Your favorite color is #46778F and your favorite Agent framework is CrewAI.',\n", "  'score': 0.890435395010837},\n", " {'id': '5125962c-dd48-4d8a-aa25-9888cda511ba',\n", "  'metadata': {'agent': 'Personal Assistant',\n", "   'observation': 'Handle this task: What is my favorite color?'},\n", "  'context': 'I now can give a great answer  \\nFinal Answer: Your favorite color is #46778F and your favorite Agent framework is CrewAI.',\n", "  'score': 0.890435395010837}]"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["user_input = \"What is my favorite color?\"\n", "\n", "crew._short_term_memory.storage.search(user_input)"]}, {"cell_type": "code", "execution_count": 37, "id": "f78bf2ae", "metadata": {}, "outputs": [], "source": ["crew._long_term_memory.search(user_input)"]}, {"cell_type": "code", "execution_count": 38, "id": "4547217d", "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'id': 'ee229c69-b6da-4367-a010-6a968aa5f71a',\n", "  'metadata': {'relationships': '- Identified as favorite color based on the task request.'},\n", "  'context': 'Favorite Color(Color): #46778F, the color designated as the favorite.',\n", "  'score': 0.6658871150305598},\n", " {'id': 'a32be6fd-1d0d-40bf-9dfb-da9490068380',\n", "  'metadata': {'relationships': '- is related to personal preference'},\n", "  'context': 'favorite color(color): A specific color that is preferred by someone.',\n", "  'score': 0.7554879215777222},\n", " {'id': '89a74270-9af3-4c69-a93d-cfa81553160c',\n", "  'metadata': {'relationships': '- is favorite color of the user'},\n", "  'context': \"favorite color(Color Code): The hexadecimal representation of the user's favorite color.\",\n", "  'score': 0.7812394006629673}]"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["user_input = \"What is my favorite color?\"\n", "\n", "crew._entity_memory.search(user_input)"]}, {"cell_type": "markdown", "id": "e00f7158", "metadata": {}, "source": ["### More on Memory"]}, {"cell_type": "code", "execution_count": 39, "id": "baf0c561", "metadata": {}, "outputs": [], "source": ["from crewai import Crew, Agent, Task, Process\n", "from crewai.memory import ShortTermMemory\n", "from crewai.memory.storage.rag_storage import RAGStorage\n", "\n", "shared_db = \"./short_term_memory.db\"\n", "\n", "storage = RAGStorage(path=shared_db, type=\"short_term\")\n", "\n", "short_term_memory = ShortTermMemory(storage=storage)\n"]}, {"cell_type": "code", "execution_count": 40, "id": "2fcbd472", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[1m\u001b[95m# Agent:\u001b[00m \u001b[1m\u001b[92mA Personal Assistant\u001b[00m\n", "\u001b[95m## Task:\u001b[00m \u001b[92mHandle this task: My favorite color is #46778F.\u001b[00m\n", "\n", "\n", "\u001b[1m\u001b[95m# Agent:\u001b[00m \u001b[1m\u001b[92mA Personal Assistant\u001b[00m\n", "\u001b[95m## Final Answer:\u001b[00m \u001b[92m\n", "Your favorite color is #46778F, which is a beautiful teal shade. This color can evoke feelings of calmness and serenity. It is often associated with nature, tranquility, and sophistication. If you have any specific applications or desired uses for this color, feel free to share!\u001b[00m\n", "\n", "\n"]}, {"data": {"text/plain": ["CrewOutput(raw='Your favorite color is #46778F, which is a beautiful teal shade. This color can evoke feelings of calmness and serenity. It is often associated with nature, tranquility, and sophistication. If you have any specific applications or desired uses for this color, feel free to share!', pydantic=None, json_dict=None, tasks_output=[TaskOutput(description='Handle this task: My favorite color is #46778F.', name=None, expected_output='A clear and concise answer to the question.', summary='Handle this task: My favorite color is #46778F....', raw='Your favorite color is #46778F, which is a beautiful teal shade. This color can evoke feelings of calmness and serenity. It is often associated with nature, tranquility, and sophistication. If you have any specific applications or desired uses for this color, feel free to share!', pydantic=None, json_dict=None, agent='A Personal Assistant', output_format=<OutputFormat.RAW: 'raw'>)], token_usage=UsageMetrics(total_tokens=265, prompt_tokens=196, cached_prompt_tokens=0, completion_tokens=69, successful_requests=1))"]}, "execution_count": 40, "metadata": {}, "output_type": "execute_result"}], "source": ["agent = Agent(role=\"A Personal Assistant\",\n", "              goal=\"\"\"You are a personal assistant that can\n", "                      help the user with their tasks.\"\"\",\n", "              backstory=\"\"\"You are a personal assistant that\n", "                           can help the user with their tasks.\"\"\",\n", "              verbose=True)\n", "\n", "task = Task(description=\"Handle this task: {user_task}\",\n", "            expected_output=\"A clear and concise answer to the question.\",\n", "            agent=agent)\n", "\n", "\n", "crew = Crew(\n", "    agents=[agent], \n", "    tasks=[task], \n", "    process=Process.sequential,\n", "    memory=True,\n", "    short_term_memory=short_term_memory\n", ")\n", "crew.kickoff(inputs={\"user_task\": \"My favorite color is #46778F.\"})"]}, {"cell_type": "code", "execution_count": 41, "id": "1d335b6f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[1m\u001b[95m# Agent:\u001b[00m \u001b[1m\u001b[92mA Personal Assistant\u001b[00m\n", "\u001b[95m## Task:\u001b[00m \u001b[92mHandle this task: What is my favorite color?\u001b[00m\n", "\n", "\n", "\u001b[1m\u001b[95m# Agent:\u001b[00m \u001b[1m\u001b[92mA Personal Assistant\u001b[00m\n", "\u001b[95m## Final Answer:\u001b[00m \u001b[92m\n", "Your favorite color is #46778F, which is a beautiful teal shade. This color can evoke feelings of calmness and serenity. It is often associated with nature, tranquility, and sophistication. If you have any specific applications or desired uses for this color, feel free to share!\u001b[00m\n", "\n", "\n"]}, {"data": {"text/plain": ["CrewOutput(raw='Your favorite color is #46778F, which is a beautiful teal shade. This color can evoke feelings of calmness and serenity. It is often associated with nature, tranquility, and sophistication. If you have any specific applications or desired uses for this color, feel free to share!', pydantic=None, json_dict=None, tasks_output=[TaskOutput(description='Handle this task: What is my favorite color?', name=None, expected_output='A clear and concise answer to the question.', summary='Handle this task: What is my favorite color?...', raw='Your favorite color is #46778F, which is a beautiful teal shade. This color can evoke feelings of calmness and serenity. It is often associated with nature, tranquility, and sophistication. If you have any specific applications or desired uses for this color, feel free to share!', pydantic=None, json_dict=None, agent='A Personal Assistant', output_format=<OutputFormat.RAW: 'raw'>)], token_usage=UsageMetrics(total_tokens=724, prompt_tokens=586, cached_prompt_tokens=0, completion_tokens=138, successful_requests=2))"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["user_query = \"What is my favorite color?\"\n", "\n", "crew.kickoff(inputs={\"user_task\": user_query})"]}, {"cell_type": "markdown", "id": "b8ace0b4", "metadata": {}, "source": ["### Long Term Memory"]}, {"cell_type": "code", "execution_count": 42, "id": "60619dfb", "metadata": {}, "outputs": [], "source": ["from crewai import Crew, Agent, Task, Process\n", "from crewai.memory import LongTermMemory\n", "from crewai.memory.storage.ltm_sqlite_storage import LTMSQLiteStorage"]}, {"cell_type": "code", "execution_count": 43, "id": "2f8fd4c3", "metadata": {}, "outputs": [], "source": ["shared_db = \"./agent_memory.db\"\n", "\n", "storage = LTMSQLiteStorage(db_path=shared_db)\n", "\n", "long_memory = LongTermMemory(storage=storage)"]}, {"cell_type": "code", "execution_count": 44, "id": "8e35c105", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[1m\u001b[95m# Agent:\u001b[00m \u001b[1m\u001b[92m<PERSON><PERSON>t Personal Assistant\u001b[00m\n", "\u001b[95m## Task:\u001b[00m \u001b[92mHandle this task: My favorite color is #46778F.\u001b[00m\n", "\n", "\n", "\u001b[1m\u001b[95m# Agent:\u001b[00m \u001b[1m\u001b[92m<PERSON><PERSON>t Personal Assistant\u001b[00m\n", "\u001b[95m## Final Answer:\u001b[00m \u001b[92m\n", "Your favorite color is hex code #46778F, which represents a soothing and calming shade of blue-green. This particular color is known to evoke feelings of tranquility and peace, often associated with nature and serenity. Utilizing this color in your surroundings can create a harmonious atmosphere, enhancing relaxation and comfort in your space. Whether in fashion, interior design, or art, #46778F can be incorporated in various ways to bring a refreshing and calming aesthetic to any setting.\u001b[00m\n", "\n", "\n"]}, {"data": {"text/plain": ["CrewOutput(raw='Your favorite color is hex code #46778F, which represents a soothing and calming shade of blue-green. This particular color is known to evoke feelings of tranquility and peace, often associated with nature and serenity. Utilizing this color in your surroundings can create a harmonious atmosphere, enhancing relaxation and comfort in your space. Whether in fashion, interior design, or art, #46778F can be incorporated in various ways to bring a refreshing and calming aesthetic to any setting.', pydantic=None, json_dict=None, tasks_output=[TaskOutput(description='Handle this task: My favorite color is #46778F.', name=None, expected_output='A clear and concise answer to the question.', summary='Handle this task: My favorite color is #46778F....', raw='Your favorite color is hex code #46778F, which represents a soothing and calming shade of blue-green. This particular color is known to evoke feelings of tranquility and peace, often associated with nature and serenity. Utilizing this color in your surroundings can create a harmonious atmosphere, enhancing relaxation and comfort in your space. Whether in fashion, interior design, or art, #46778F can be incorporated in various ways to bring a refreshing and calming aesthetic to any setting.', pydantic=None, json_dict=None, agent='First Personal Assistant', output_format=<OutputFormat.RAW: 'raw'>)], token_usage=UsageMetrics(total_tokens=547, prompt_tokens=442, cached_prompt_tokens=0, completion_tokens=105, successful_requests=1))"]}, "execution_count": 44, "metadata": {}, "output_type": "execute_result"}], "source": ["agent1 = Agent(role=\"First Personal Assistant\",\n", "              goal=\"\"\"You are a personal assistant that handles\n", "                      user's tasks.\"\"\",\n", "              backstory=\"\"\"You are a personal assistant that\n", "                           can help the user with their tasks.\"\"\",\n", "              verbose=True)\n", "\n", "task1 = Task(description=\"Handle this task: {user_task}\",\n", "            expected_output=\"A clear and concise answer to the question.\",\n", "            agent=agent1)\n", "\n", "crew1 = Crew(\n", "    agents=[agent1],\n", "    tasks=[task1], \n", "    process=Process.sequential,\n", "    memory=True,\n", "    long_term_memory=long_memory\n", ")\n", "crew1.kickoff(inputs={\"user_task\": \"My favorite color is #46778F.\"})"]}, {"cell_type": "code", "execution_count": 45, "id": "9895fe86", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Tables in the database:\n", "long_term_memories\n", "sqlite_sequence\n", "(1, 'Handle this task: My favorite color is #46778F.', '{\"suggestions\": [\"Provide a more direct answer to the question\", \"Limit the response to the specific color description without added commentary\", \"Ensure to stick closely to the expected output format\", \"Avoid overly elaborate explanations that may detract from the core response\"], \"quality\": 6.0, \"agent\": \"First Personal Assistant\", \"expected_output\": \"A clear and concise answer to the question.\"}', '1744540942.912284', 6.0)\n"]}], "source": ["import sqlite3\n", "\n", "conn = sqlite3.connect(\"agent_memory.db\")\n", "cursor = conn.cursor()\n", "cursor.execute(\"SELECT name FROM sqlite_master WHERE type='table';\")\n", "\n", "tables = cursor.fetchall()\n", "\n", "print(\"Tables in the database:\")\n", "for table in tables:\n", "    print(table[0])\n", "\n", "cursor.execute(\"SELECT * FROM long_term_memories;\")\n", "rows = cursor.fetchall()\n", "\n", "for row in rows:\n", "    print(row)\n"]}, {"cell_type": "code", "execution_count": 46, "id": "7b46d184", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[1m\u001b[95m# Agent:\u001b[00m \u001b[1m\u001b[92m<PERSON><PERSON>t Personal Assistant\u001b[00m\n", "\u001b[95m## Task:\u001b[00m \u001b[92mHandle this task: What is my favorite color?\u001b[00m\n", "\n", "\n", "\u001b[1m\u001b[95m# Agent:\u001b[00m \u001b[1m\u001b[92m<PERSON><PERSON>t Personal Assistant\u001b[00m\n", "\u001b[95m## Final Answer:\u001b[00m \u001b[92m\n", "Your favorite color is hex code #46778F, which is a soothing and calming shade of blue-green. This specific color often evokes feelings of tranquility and is commonly associated with nature and serenity, making it a perfect choice for creating a peaceful atmosphere in any environment.\u001b[00m\n", "\n", "\n"]}, {"data": {"text/plain": ["CrewOutput(raw='Your favorite color is hex code #46778F, which is a soothing and calming shade of blue-green. This specific color often evokes feelings of tranquility and is commonly associated with nature and serenity, making it a perfect choice for creating a peaceful atmosphere in any environment.', pydantic=None, json_dict=None, tasks_output=[TaskOutput(description='Handle this task: What is my favorite color?', name=None, expected_output='A clear and concise answer to the question.', summary='Handle this task: What is my favorite color?...', raw='Your favorite color is hex code #46778F, which is a soothing and calming shade of blue-green. This specific color often evokes feelings of tranquility and is commonly associated with nature and serenity, making it a perfect choice for creating a peaceful atmosphere in any environment.', pydantic=None, json_dict=None, agent='First Personal Assistant', output_format=<OutputFormat.RAW: 'raw'>)], token_usage=UsageMetrics(total_tokens=1025, prompt_tokens=855, cached_prompt_tokens=0, completion_tokens=170, successful_requests=2))"]}, "execution_count": 46, "metadata": {}, "output_type": "execute_result"}], "source": ["task2 = Task(description=\"Handle this task: {user_task}\",\n", "            expected_output=\"A clear and concise answer to the question.\",\n", "            agent=agent1)\n", "\n", "crew2 = Crew(\n", "    agents=[agent1], \n", "    tasks=[task2], \n", "    process=Process.sequential,\n", "    memory=True,\n", "    long_term_memory=long_memory\n", ")\n", "\n", "crew2.kickoff(inputs={\"user_task\": \"What is my favorite color?\"})\n"]}, {"cell_type": "markdown", "id": "d95c87f9", "metadata": {}, "source": ["### Entity Memory"]}, {"cell_type": "code", "execution_count": 47, "id": "44931fbe", "metadata": {}, "outputs": [], "source": ["from crewai import Crew, Agent, Task, Process\n", "from crewai.memory import EntityMemory\n", "from crewai.memory.storage.rag_storage import RAGStorage\n", "\n", "shared_db = \"./agent1_entity_memory.db\"\n", "\n", "storage = RAGStorage(path=shared_db, type=\"short_term\")\n", "\n", "entity_memory = EntityMemory(storage=storage)\n"]}, {"cell_type": "code", "execution_count": 48, "id": "59b5e1d7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[1m\u001b[95m# Agent:\u001b[00m \u001b[1m\u001b[92mRemembering Agent\u001b[00m\n", "\u001b[95m## Task:\u001b[00m \u001b[92mHandle this task: My favorite color is #46778F.\u001b[00m\n", "\n", "\n", "\u001b[1m\u001b[95m# Agent:\u001b[00m \u001b[1m\u001b[92mRemembering Agent\u001b[00m\n", "\u001b[95m## Final Answer:\u001b[00m \u001b[92m\n", "Your favorite color is #46778F.\u001b[00m\n", "\n", "\n"]}, {"data": {"text/plain": ["CrewOutput(raw='Your favorite color is #46778F.', pydantic=None, json_dict=None, tasks_output=[TaskOutput(description='Handle this task: My favorite color is #46778F.', name=None, expected_output='A clear and concise answer to the question.', summary='Handle this task: My favorite color is #46778F....', raw='Your favorite color is #46778F.', pydantic=None, json_dict=None, agent='Remembering Agent', output_format=<OutputFormat.RAW: 'raw'>)], token_usage=UsageMetrics(total_tokens=263, prompt_tokens=242, cached_prompt_tokens=0, completion_tokens=21, successful_requests=1))"]}, "execution_count": 48, "metadata": {}, "output_type": "execute_result"}], "source": ["remembering_agent = Agent(role=\"Remembering Agent\",\n", "                          goal=\"\"\"You are a assistant that can\n", "                                  remember the user's information.\"\"\",\n", "                          backstory=\"\"\"You are a assistant that can remember\n", "                                       the user's information.\"\"\",\n", "                          verbose=True)\n", "            \n", "task = Task(description=\"Handle this task: {user_task}\",\n", "            expected_output=\"A clear and concise answer to the question.\",\n", "            agent=remembering_agent)\n", "\n", "crew = Crew(\n", "    agents=[remembering_agent], \n", "    tasks=[task], \n", "    process=Process.sequential,\n", "    memory=True,\n", "    entity_memory=entity_memory\n", ")\n", "\n", "crew.kickoff(inputs={\"user_task\": \"My favorite color is #46778F.\"})"]}, {"cell_type": "code", "execution_count": 49, "id": "4aa235fd", "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'id': 'b5e36392-36ff-41ba-bc17-83fab07482c3',\n", "  'metadata': {'relationships': '- It is identified by the color code #46778F.'},\n", "  'context': 'favorite color(color): The color that the person prefers, mentioned in the task.',\n", "  'score': 0.9134150239774512}]"]}, "execution_count": 49, "metadata": {}, "output_type": "execute_result"}], "source": ["crew._entity_memory.search(\"What is my favorite color?\")"]}, {"cell_type": "markdown", "id": "5759eb66", "metadata": {}, "source": ["### Resetting memory"]}, {"cell_type": "code", "execution_count": 50, "id": "5d19d55d", "metadata": {}, "outputs": [{"ename": "RuntimeError", "evalue": "Failed to reset all memory: Failed to reset entity memory", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mFileNotFoundError\u001b[39m                         <PERSON><PERSON> (most recent call last)", "\u001b[36mFile \u001b[39m\u001b[32m~/opt/anaconda3/envs/trippy/lib/python3.12/site-packages/crewai/memory/storage/rag_storage.py:155\u001b[39m, in \u001b[36mRAGStorage.reset\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    154\u001b[39m \u001b[38;5;28mself\u001b[39m.app.reset()\n\u001b[32m--> \u001b[39m\u001b[32m155\u001b[39m \u001b[43mshutil\u001b[49m\u001b[43m.\u001b[49m\u001b[43mrmtree\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43mf\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[38;5;132;43;01m{\u001b[39;49;00m\u001b[43mdb_storage_path\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;132;43;01m}\u001b[39;49;00m\u001b[33;43m/\u001b[39;49m\u001b[38;5;132;43;01m{\u001b[39;49;00m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mtype\u001b[49m\u001b[38;5;132;43;01m}\u001b[39;49;00m\u001b[33;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[32m    156\u001b[39m \u001b[38;5;28mself\u001b[39m.app = \u001b[38;5;28;01mNone\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/opt/anaconda3/envs/trippy/lib/python3.12/shutil.py:759\u001b[39m, in \u001b[36mrmtree\u001b[39m\u001b[34m(path, ignore_errors, onerror, onexc, dir_fd)\u001b[39m\n\u001b[32m    758\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m err:\n\u001b[32m--> \u001b[39m\u001b[32m759\u001b[39m     \u001b[43monexc\u001b[49m\u001b[43m(\u001b[49m\u001b[43mos\u001b[49m\u001b[43m.\u001b[49m\u001b[43mlstat\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mpath\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43merr\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    760\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/opt/anaconda3/envs/trippy/lib/python3.12/shutil.py:757\u001b[39m, in \u001b[36mrmtree\u001b[39m\u001b[34m(path, ignore_errors, onerror, onexc, dir_fd)\u001b[39m\n\u001b[32m    756\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m757\u001b[39m     orig_st = \u001b[43mos\u001b[49m\u001b[43m.\u001b[49m\u001b[43mlstat\u001b[49m\u001b[43m(\u001b[49m\u001b[43mpath\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdir_fd\u001b[49m\u001b[43m=\u001b[49m\u001b[43mdir_fd\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    758\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m err:\n", "\u001b[31mFileNotFoundError\u001b[39m: [Errno 2] No such file or directory: '/Users/<USER>/Library/Application Support/crewai-advanced/short_term'", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[31mException\u001b[39m                                 <PERSON><PERSON> (most recent call last)", "\u001b[36mFile \u001b[39m\u001b[32m~/opt/anaconda3/envs/trippy/lib/python3.12/site-packages/crewai/memory/entity/entity_memory.py:64\u001b[39m, in \u001b[36mEntityMemory.reset\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m     63\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m---> \u001b[39m\u001b[32m64\u001b[39m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mstorage\u001b[49m\u001b[43m.\u001b[49m\u001b[43mreset\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     65\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/opt/anaconda3/envs/trippy/lib/python3.12/site-packages/crewai/memory/storage/rag_storage.py:163\u001b[39m, in \u001b[36mRAGStorage.reset\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    162\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m163\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m(\n\u001b[32m    164\u001b[39m         \u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mAn error occurred while resetting the \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mself\u001b[39m.type\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m memory: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00me\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m\n\u001b[32m    165\u001b[39m     )\n", "\u001b[31mException\u001b[39m: An error occurred while resetting the short_term memory: [Errno 2] No such file or directory: '/Users/<USER>/Library/Application Support/crewai-advanced/short_term'", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[31mException\u001b[39m                                 <PERSON><PERSON> (most recent call last)", "\u001b[36mFile \u001b[39m\u001b[32m~/opt/anaconda3/envs/trippy/lib/python3.12/site-packages/crewai/crew.py:1372\u001b[39m, in \u001b[36mCrew._reset_all_memories\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m   1371\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m-> \u001b[39m\u001b[32m1372\u001b[39m     \u001b[43msystem\u001b[49m\u001b[43m.\u001b[49m\u001b[43mreset\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1373\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/opt/anaconda3/envs/trippy/lib/python3.12/site-packages/crewai/memory/entity/entity_memory.py:66\u001b[39m, in \u001b[36mEntityMemory.reset\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m     65\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[32m---> \u001b[39m\u001b[32m66\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mAn error occurred while resetting the entity memory: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00me\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m)\n", "\u001b[31mException\u001b[39m: An error occurred while resetting the entity memory: An error occurred while resetting the short_term memory: [Errno 2] No such file or directory: '/Users/<USER>/Library/Application Support/crewai-advanced/short_term'", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[31m<PERSON>unt<PERSON>E<PERSON><PERSON>\u001b[39m                              <PERSON><PERSON> (most recent call last)", "\u001b[36mFile \u001b[39m\u001b[32m~/opt/anaconda3/envs/trippy/lib/python3.12/site-packages/crewai/crew.py:1347\u001b[39m, in \u001b[36mCrew.reset_memories\u001b[39m\u001b[34m(self, command_type)\u001b[39m\n\u001b[32m   1346\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m command_type == \u001b[33m\"\u001b[39m\u001b[33mall\u001b[39m\u001b[33m\"\u001b[39m:\n\u001b[32m-> \u001b[39m\u001b[32m1347\u001b[39m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_reset_all_memories\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1348\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/opt/anaconda3/envs/trippy/lib/python3.12/site-packages/crewai/crew.py:1374\u001b[39m, in \u001b[36mCrew._reset_all_memories\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m   1373\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[32m-> \u001b[39m\u001b[32m1374\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mRuntimeError\u001b[39;00m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mFailed to reset \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mname\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m memory\u001b[39m\u001b[33m\"\u001b[39m) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01me\u001b[39;00m\n", "\u001b[31mRuntimeError\u001b[39m: Failed to reset entity memory", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[31m<PERSON>unt<PERSON>E<PERSON><PERSON>\u001b[39m                              <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[50]\u001b[39m\u001b[32m, line 1\u001b[39m\n\u001b[32m----> \u001b[39m\u001b[32m1\u001b[39m \u001b[43mcrew\u001b[49m\u001b[43m.\u001b[49m\u001b[43mreset_memories\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcommand_type\u001b[49m\u001b[43m \u001b[49m\u001b[43m=\u001b[49m\u001b[43m \u001b[49m\u001b[33;43m'\u001b[39;49m\u001b[33;43mall\u001b[39;49m\u001b[33;43m'\u001b[39;49m\u001b[43m)\u001b[49m \n", "\u001b[36mFile \u001b[39m\u001b[32m~/opt/anaconda3/envs/trippy/lib/python3.12/site-packages/crewai/crew.py:1356\u001b[39m, in \u001b[36mCrew.reset_memories\u001b[39m\u001b[34m(self, command_type)\u001b[39m\n\u001b[32m   1354\u001b[39m error_msg = \u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mFailed to reset \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mcommand_type\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m memory: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mstr\u001b[39m(e)\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m\n\u001b[32m   1355\u001b[39m \u001b[38;5;28mself\u001b[39m._logger.log(\u001b[33m\"\u001b[39m\u001b[33merror\u001b[39m\u001b[33m\"\u001b[39m, error_msg)\n\u001b[32m-> \u001b[39m\u001b[32m1356\u001b[39m \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mRuntimeError\u001b[39;00m(error_msg) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01me\u001b[39;00m\n", "\u001b[31mRuntimeError\u001b[39m: Failed to reset all memory: Failed to reset entity memory"]}], "source": ["crew.reset_memories(command_type = 'all') "]}, {"cell_type": "code", "execution_count": 51, "id": "2920c376", "metadata": {}, "outputs": [], "source": ["crew.reset_memories(command_type='short')"]}, {"cell_type": "code", "execution_count": 52, "id": "b53a26fb", "metadata": {}, "outputs": [], "source": ["crew.reset_memories(command_type='long')"]}, {"cell_type": "code", "execution_count": null, "id": "47f844d4", "metadata": {}, "outputs": [], "source": ["crew.reset_memories(command_type='entities')"]}], "metadata": {"kernelspec": {"display_name": "trippy", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.0"}}, "nbformat": 4, "nbformat_minor": 5}