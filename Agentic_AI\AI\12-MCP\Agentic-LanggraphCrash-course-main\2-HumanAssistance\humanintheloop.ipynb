{"cells": [{"cell_type": "markdown", "id": "cfa36789", "metadata": {}, "source": ["## Human In the Loop"]}, {"cell_type": "code", "execution_count": 1, "id": "a026b4dc", "metadata": {}, "outputs": [{"data": {"text/plain": ["ChatGroq(client=<groq.resources.chat.completions.Completions object at 0x000001F8E603AE40>, async_client=<groq.resources.chat.completions.AsyncCompletions object at 0x000001F8E603BA10>, model_name='llama3-8b-8192', model_kwargs={}, groq_api_key=SecretStr('**********'))"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import os\n", "from langchain.chat_models import init_chat_model\n", "llm=init_chat_model(\"groq:llama3-8b-8192\")\n", "llm"]}, {"cell_type": "code", "execution_count": 2, "id": "8249cbdf", "metadata": {}, "outputs": [{"data": {"text/plain": ["<langgraph.graph.state.StateGraph at 0x1f8e79030e0>"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["from typing import Annotated\n", "\n", "from langchain_tavily import TavilySearch\n", "from langchain_core.tools import tool\n", "from typing_extensions import TypedDict\n", "\n", "from langgraph.checkpoint.memory import MemorySaver\n", "from langgraph.graph import StateGraph, START, END\n", "from langgraph.graph.message import add_messages\n", "from langgraph.prebuilt import ToolNode, tools_condition\n", "\n", "from langgraph.types import Command, interrupt\n", "\n", "class State(TypedDict):\n", "    messages: Annotated[list, add_messages]\n", "\n", "graph_builder = StateGraph(State)\n", "\n", "@tool\n", "def human_assistance(query: str) -> str:\n", "    \"\"\"Request assistance from a human.\"\"\"\n", "    human_response = interrupt({\"query\": query})\n", "    return human_response[\"data\"]\n", "\n", "tool = TavilySearch(max_results=2)\n", "tools = [tool, human_assistance]\n", "llm_with_tools = llm.bind_tools(tools)\n", "\n", "def chatbot(state: State):\n", "    message = llm_with_tools.invoke(state[\"messages\"])\n", "    # Because we will be interrupting during tool execution,\n", "    # we disable parallel tool calling to avoid repeating any\n", "    # tool invocations when we resume.\n", "    \n", "    return {\"messages\": [message]}\n", "\n", "graph_builder.add_node(\"chatbot\", chatbot)\n", "\n", "tool_node = ToolNode(tools=tools)\n", "graph_builder.add_node(\"tools\", tool_node)\n", "\n", "graph_builder.add_conditional_edges(\n", "    \"chatbot\",\n", "    tools_condition,\n", ")\n", "graph_builder.add_edge(\"tools\", \"chatbot\")\n", "graph_builder.add_edge(START, \"chatbot\")\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 3, "id": "ec16e698", "metadata": {}, "outputs": [], "source": ["memory = MemorySaver()\n", "\n", "graph = graph_builder.compile(checkpointer=memory)"]}, {"cell_type": "code", "execution_count": 4, "id": "fb33c8d4", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.display import Image, display\n", "\n", "try:\n", "    display(Image(graph.get_graph().draw_mermaid_png()))\n", "except Exception:\n", "    # This requires some extra dependencies and is optional\n", "    pass"]}, {"cell_type": "code", "execution_count": 6, "id": "c63f9f63", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "I need some expert guidance and assistance for building an AI agent. Could you request assistance for me?\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Tool Calls:\n", "  human_assistance (8jwn397aj)\n", " Call ID: 8jwn397aj\n", "  Args:\n", "    query: expert guidance and assistance for building an AI agent\n"]}], "source": ["user_input = \"I need some expert guidance and assistance for building an AI agent. Could you request assistance for me?\"\n", "config = {\"configurable\": {\"thread_id\": \"1\"}}\n", "\n", "events = graph.stream(\n", "    {\"messages\": user_input},\n", "    config,\n", "    stream_mode=\"values\",\n", ")\n", "for event in events:\n", "    if \"messages\" in event:\n", "        event[\"messages\"][-1].pretty_print()"]}, {"cell_type": "code", "execution_count": 7, "id": "661660a3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Tool Calls:\n", "  human_assistance (8jwn397aj)\n", " Call ID: 8jwn397aj\n", "  Args:\n", "    query: expert guidance and assistance for building an AI agent\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: human_assistance\n", "\n", "We, the experts are here to help! We'd recommend you check out LangGraph to build your agent. It's much more reliable and extensible than simple autonomous agents.\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "Thank you for the recommendation! LangGraph seems like a great tool for building AI agents. I'll make sure to keep that in mind.\n", "\n", "To further assist you, I'd like to ask a few follow-up questions:\n", "\n", "1. What specific aspects of building an AI agent are you struggling with or would like guidance on?\n", "2. What is your background in AI and machine learning, and what is your goal for building this agent?\n", "3. Have you considered any specific architectures or frameworks for building your agent, or would you like some recommendations?\n", "\n", "Please let me know, and I'll do my best to provide more tailored guidance and assistance.\n"]}], "source": ["human_response = (\n", "    \"We, the experts are here to help! We'd recommend you check out LangGraph to build your agent.\"\n", "    \" It's much more reliable and extensible than simple autonomous agents.\"\n", ")\n", "\n", "human_command = Command(resume={\"data\": human_response})\n", "\n", "events = graph.stream(human_command, config, stream_mode=\"values\")\n", "for event in events:\n", "    if \"messages\" in event:\n", "        event[\"messages\"][-1].pretty_print()"]}, {"cell_type": "code", "execution_count": null, "id": "83c25f9d", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "AgenticLanggraph", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}