# Configuration file for Advanced AI Voice-Over Streamlit Application
import os
from pathlib import Path

# Import Azure configuration from existing system
import sys
parent_dir = Path(__file__).parent.parent
sys.path.append(str(parent_dir / "AI_Voice_Over_Azure"))

try:
    from config import (
        A<PERSON><PERSON><PERSON>_SPEECH_KEY, AZURE_SPEECH_REGION, AZURE_STORAGE_CONNECTION_STRING,
        AZURE_CONTAINER_VIDEOS, AZURE_CONTAINER_AUDIO, AZURE_CONTAINER_OUTPUT,
        AZURE_VOICE_OPTIONS, AZURE_SPEECH_STT_CONFIG, AZURE_SPEECH_TTS_CONFIG
    )
except ImportError:
    # Fallback configuration if Azure config is not available
    AZURE_SPEECH_KEY = "your_azure_speech_key_here"
    AZURE_SPEECH_REGION = "eastus"
    AZURE_STORAGE_CONNECTION_STRING = "your_connection_string_here"
    AZURE_CONTAINER_VIDEOS = "videos"
    AZURE_CONTAINER_AUDIO = "audio"
    AZURE_CONTAINER_OUTPUT = "output"
    AZURE_VOICE_OPTIONS = {
        "aria": "en-US-AriaNeural",
        "jenny": "en-US-JennyNeural"
    }
    AZURE_SPEECH_STT_CONFIG = {"language": "en-US"}
    AZURE_SPEECH_TTS_CONFIG = {"voice_name": "en-US-AriaNeural"}

# Streamlit Application Settings
APP_TITLE = "Advanced AI Voice-Over System"
APP_ICON = "🎬"
APP_LAYOUT = "wide"

# File Processing Settings
SUPPORTED_VIDEO_FORMATS = ['.mp4', '.avi', '.mov', '.mkv', '.webm', '.wmv']
SUPPORTED_AUDIO_FORMATS = ['.mp3', '.wav', '.m4a', '.flac', '.aac']
MAX_FILE_SIZE_MB = 500  # Increased for Streamlit
CHUNK_SIZE = 8192

# Audio Processing Settings
AUDIO_SAMPLE_RATE = 16000  # Standard for speech processing
AUDIO_CHANNELS = 1  # Mono
AUDIO_BIT_DEPTH = 16

# Timeline Settings
MIN_SEGMENT_LENGTH = 1.0  # Minimum segment length in seconds
SILENCE_THRESHOLD = 0.01  # Threshold for silence detection
MERGE_GAP_THRESHOLD = 0.5  # Merge segments closer than this

# AI Enhancement Settings
DEFAULT_ENHANCEMENT_SETTINGS = {
    'noise_reduction': 50,  # 0-100
    'normalize_audio': True,
    'enhance_speech': True,
    'remove_silence': False,
    'apply_eq': True,
    'dynamic_range_compression': True
}

# Audio Quality Settings
NOISE_REDUCTION_LEVELS = {
    'light': 0.3,
    'medium': 0.5,
    'heavy': 0.7
}

COMPRESSION_SETTINGS = {
    'light': {'threshold': 0.5, 'ratio': 2.0},
    'medium': {'threshold': 0.3, 'ratio': 4.0},
    'heavy': {'threshold': 0.2, 'ratio': 6.0}
}

# Directory Settings
BASE_DIR = Path(__file__).parent
TEMP_FOLDER = BASE_DIR / "temp"
OUTPUT_FOLDER = BASE_DIR / "output"
PROJECTS_FOLDER = BASE_DIR / "projects"
CACHE_FOLDER = BASE_DIR / ".cache"

# Streamlit Session State Keys
SESSION_KEYS = {
    'project_data': 'project_data',
    'azure_converter': 'azure_converter',
    'audio_processor': 'audio_processor',
    'audio_polisher': 'audio_polisher',
    'current_step': 'current_step',
    'timeline_data': 'timeline_data',
    'enhancement_settings': 'enhancement_settings'
}

# UI Settings
TIMELINE_HEIGHT = 500
WAVEFORM_HEIGHT = 200
PLAYER_HEIGHT = 400

# Color Scheme
COLORS = {
    'primary': '#1f77b4',
    'secondary': '#ff7f0e',
    'success': '#2ca02c',
    'warning': '#ff7f0e',
    'error': '#d62728',
    'info': '#17a2b8',
    'segment_normal': 'lightblue',
    'segment_modified': 'lightcoral',
    'segment_selected': 'gold',
    'timeline_cursor': 'red'
}

# Export Settings
EXPORT_FORMATS = {
    'mp4': {
        'extension': '.mp4',
        'codec': 'libx264',
        'audio_codec': 'aac'
    },
    'avi': {
        'extension': '.avi',
        'codec': 'libx264',
        'audio_codec': 'mp3'
    },
    'mov': {
        'extension': '.mov',
        'codec': 'libx264',
        'audio_codec': 'aac'
    }
}

QUALITY_SETTINGS = {
    'high': {
        'video_bitrate': '5000k',
        'audio_bitrate': '192k',
        'resolution': 'original'
    },
    'medium': {
        'video_bitrate': '2500k',
        'audio_bitrate': '128k',
        'resolution': '1080p'
    },
    'low': {
        'video_bitrate': '1000k',
        'audio_bitrate': '96k',
        'resolution': '720p'
    }
}

# Logging Settings
LOG_LEVEL = "INFO"
LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
LOG_FILE = BASE_DIR / "app.log"

# Performance Settings
MAX_CONCURRENT_PROCESSES = 2
PROCESSING_TIMEOUT = 600  # 10 minutes
CACHE_EXPIRY_HOURS = 24

# Feature Flags
FEATURES = {
    'enable_real_time_preview': True,
    'enable_waveform_display': True,
    'enable_auto_save': True,
    'enable_project_templates': False,
    'enable_batch_processing': False,
    'enable_cloud_sync': True
}

# API Settings (for future extensions)
API_SETTINGS = {
    'enable_api': False,
    'api_port': 8000,
    'api_host': 'localhost'
}

def create_directories():
    """Create necessary directories if they don't exist"""
    directories = [
        TEMP_FOLDER,
        OUTPUT_FOLDER,
        PROJECTS_FOLDER,
        CACHE_FOLDER
    ]
    
    for directory in directories:
        directory.mkdir(parents=True, exist_ok=True)
        print(f"Ensured directory exists: {directory}")

def get_project_path(project_id: str) -> Path:
    """Get path for a specific project"""
    return PROJECTS_FOLDER / project_id

def get_temp_path(filename: str) -> Path:
    """Get path for temporary file"""
    return TEMP_FOLDER / filename

def get_output_path(filename: str) -> Path:
    """Get path for output file"""
    return OUTPUT_FOLDER / filename

# Initialize directories on import
create_directories()

if __name__ == "__main__":
    print("Configuration loaded successfully")
    print(f"Base directory: {BASE_DIR}")
    print(f"Temp folder: {TEMP_FOLDER}")
    print(f"Output folder: {OUTPUT_FOLDER}")
    create_directories()
