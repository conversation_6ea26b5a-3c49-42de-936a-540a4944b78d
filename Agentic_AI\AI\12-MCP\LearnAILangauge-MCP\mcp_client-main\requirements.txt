altair==5.5.0
annotated-types==0.7.0
anyio==4.8.0
attrs==25.3.0
blinker==1.9.0
cachetools==5.5.2
certifi==2025.1.31
charset-normalizer==3.4.1
click==8.1.8
filetype==1.2.0
gitdb==4.0.12
GitPython==3.1.44
google-ai-generativelanguage==0.6.15
google-api-core==2.24.2
google-api-python-client==2.165.0
google-auth==2.38.0
google-auth-httplib2==0.2.0
google-genai==1.5.0
google-generativeai==0.8.4
googleapis-common-protos==1.69.2
grpcio==1.71.0
grpcio-status==1.71.0
h11==0.14.0
httpcore==1.0.7
httplib2==0.22.0
httpx==0.28.1
httpx-sse==0.4.0
idna==3.10
Jinja2==3.1.6
jsonpatch==1.33
jsonpointer==3.0.0
jsonschema==4.23.0
jsonschema-specifications==2024.10.1
langchain==0.3.21
langchain-core==0.3.46
langchain-google-genai==2.0.10
langchain-mcp-adapters==0.0.5
langchain-text-splitters==0.3.7
langgraph==0.3.18
langgraph-checkpoint==2.0.21
langgraph-prebuilt==0.1.3
langgraph-sdk==0.1.58
langsmith==0.3.18
MarkupSafe==3.0.2
mcp==1.4.1
mcp-client @ file:///Users/<USER>/mcp/clients/mcp-client
msgpack==1.1.0
narwhals==1.34.1
nest-asyncio==1.6.0
numpy==2.2.4
orjson==3.10.15
ormsgpack==1.9.1
packaging==24.2
pandas==2.2.3
pillow==11.1.0
proto-plus==1.26.1
protobuf==5.29.4
pyarrow==19.0.1
pyasn1==0.6.1
pyasn1_modules==0.4.1
pydantic==2.10.6
pydantic-settings==2.8.1
pydantic_core==2.27.2
pydeck==0.9.1
pyparsing==3.2.1
python-dateutil==2.9.0.post0
python-dotenv==1.0.1
pytz==2025.2
PyYAML==6.0.2
referencing==0.36.2
requests==2.32.3
requests-toolbelt==1.0.0
rpds-py==0.24.0
rsa==4.9
six==1.17.0
smmap==5.0.2
sniffio==1.3.1
SQLAlchemy==2.0.39
sse-starlette==2.2.1
starlette==0.46.1
streamlit==1.44.1
tenacity==9.0.0
toml==0.10.2
tornado==6.4.2
tqdm==4.67.1
typing-inspection==0.4.0
typing_extensions==4.12.2
tzdata==2025.2
uritemplate==4.1.1
urllib3==2.3.0
uvicorn==0.34.0
websockets==14.2
xxhash==3.5.0
zstandard==0.23.0
