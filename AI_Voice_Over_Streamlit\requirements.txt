# Advanced AI Voice-Over Streamlit Application Requirements

# Streamlit and UI components
streamlit==1.29.0
streamlit-player==0.1.5
streamlit-audio-recorder==0.0.8
streamlit-timeline==0.0.2
plotly==5.17.0
matplotlib==3.8.2

# Azure SDK packages (reuse from existing system)
azure-cognitiveservices-speech==1.34.0
azure-storage-blob==12.19.0
azure-identity==1.15.0
azure-mgmt-storage==21.1.0

# Enhanced Audio/Video processing
moviepy==1.0.3
pydub==0.25.1
librosa==0.10.1
scipy==1.11.4
numpy==1.24.3
soundfile==0.12.1
ffmpeg-python==0.2.0

# Audio enhancement and AI processing
noisereduce==3.0.0
webrtcvad==2.0.10
pyaudio==0.2.11

# HTTP requests and utilities
requests==2.31.0
tqdm==4.66.1

# Data handling and visualization
pandas==2.1.4
pillow==10.1.0

# Environment and configuration
python-dotenv==1.0.0

# Logging and utilities
colorlog==6.8.0

# File handling and compression
zipfile36==0.1.3

# Timeline and synchronization
python-dateutil==2.8.2

# Additional Streamlit components
streamlit-aggrid==0.3.4
streamlit-option-menu==0.3.6
