#!/usr/bin/env python3
"""
AI Audio Polisher
Advanced audio enhancement using AI and signal processing techniques
"""

import os
import logging
import numpy as np
import librosa
import soundfile as sf
from typing import Dict, Optional, Tuple
import noisereduce as nr
from scipy import signal
from scipy.signal import butter, filtfilt
import json

class AudioPolisher:
    """AI-powered audio enhancement and polishing"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.sample_rate = 16000
        
    def polish_audio(self, audio_path: str, output_path: str, 
                    enhancement_settings: Dict = None) -> Dict:
        """
        Apply AI-powered audio polishing
        
        Args:
            audio_path: Path to input audio file
            output_path: Path for enhanced audio output
            enhancement_settings: Dictionary of enhancement parameters
            
        Returns:
            Dictionary with enhancement results and metrics
        """
        try:
            self.logger.info(f"Starting AI audio polishing: {audio_path}")
            
            # Default enhancement settings
            if enhancement_settings is None:
                enhancement_settings = {
                    'noise_reduction': 50,
                    'normalize_audio': True,
                    'enhance_speech': True,
                    'remove_silence': False,
                    'apply_eq': True,
                    'dynamic_range_compression': True
                }
            
            # Load audio
            audio_data, sr = librosa.load(audio_path, sr=self.sample_rate)
            original_audio = audio_data.copy()
            
            # Apply enhancement pipeline
            enhanced_audio = audio_data
            enhancement_log = []
            
            # 1. Noise Reduction
            if enhancement_settings.get('noise_reduction', 0) > 0:
                enhanced_audio, noise_metrics = self.apply_noise_reduction(
                    enhanced_audio, sr, 
                    reduction_strength=enhancement_settings['noise_reduction'] / 100.0
                )
                enhancement_log.append(f"Noise reduction applied: {noise_metrics}")
            
            # 2. Speech Enhancement
            if enhancement_settings.get('enhance_speech', False):
                enhanced_audio = self.enhance_speech_clarity(enhanced_audio, sr)
                enhancement_log.append("Speech clarity enhanced")
            
            # 3. Audio Normalization
            if enhancement_settings.get('normalize_audio', False):
                enhanced_audio = self.normalize_audio(enhanced_audio)
                enhancement_log.append("Audio normalized")
            
            # 4. Equalization
            if enhancement_settings.get('apply_eq', False):
                enhanced_audio = self.apply_speech_eq(enhanced_audio, sr)
                enhancement_log.append("Speech EQ applied")
            
            # 5. Dynamic Range Compression
            if enhancement_settings.get('dynamic_range_compression', False):
                enhanced_audio = self.apply_compression(enhanced_audio)
                enhancement_log.append("Dynamic range compression applied")
            
            # 6. Remove excessive silence
            if enhancement_settings.get('remove_silence', False):
                enhanced_audio = self.trim_silence(enhanced_audio, sr)
                enhancement_log.append("Excessive silence removed")
            
            # Save enhanced audio
            sf.write(output_path, enhanced_audio, sr)
            
            # Calculate enhancement metrics
            metrics = self.calculate_enhancement_metrics(original_audio, enhanced_audio, sr)
            
            result = {
                'enhanced_audio_path': output_path,
                'enhancement_log': enhancement_log,
                'metrics': metrics,
                'settings_used': enhancement_settings
            }
            
            self.logger.info("Audio polishing completed successfully")
            return result
            
        except Exception as e:
            self.logger.error(f"Error in audio polishing: {str(e)}")
            raise
    
    def apply_noise_reduction(self, audio_data: np.ndarray, sample_rate: int, 
                            reduction_strength: float = 0.5) -> Tuple[np.ndarray, Dict]:
        """
        Apply AI-powered noise reduction
        
        Args:
            audio_data: Input audio signal
            sample_rate: Sample rate
            reduction_strength: Strength of noise reduction (0.0 to 1.0)
            
        Returns:
            Tuple of (enhanced_audio, metrics)
        """
        try:
            # Use noisereduce library for spectral gating
            reduced_noise = nr.reduce_noise(
                y=audio_data, 
                sr=sample_rate,
                stationary=False,  # Non-stationary noise reduction
                prop_decrease=reduction_strength
            )
            
            # Calculate noise reduction metrics
            original_noise_level = np.std(audio_data)
            reduced_noise_level = np.std(reduced_noise)
            noise_reduction_db = 20 * np.log10(original_noise_level / (reduced_noise_level + 1e-10))
            
            metrics = {
                'noise_reduction_db': float(noise_reduction_db),
                'original_noise_level': float(original_noise_level),
                'reduced_noise_level': float(reduced_noise_level)
            }
            
            return reduced_noise, metrics
            
        except Exception as e:
            self.logger.error(f"Error in noise reduction: {str(e)}")
            return audio_data, {}
    
    def enhance_speech_clarity(self, audio_data: np.ndarray, sample_rate: int) -> np.ndarray:
        """
        Enhance speech clarity using spectral processing
        
        Args:
            audio_data: Input audio signal
            sample_rate: Sample rate
            
        Returns:
            Enhanced audio signal
        """
        try:
            # Apply spectral subtraction for speech enhancement
            stft = librosa.stft(audio_data, n_fft=2048, hop_length=512)
            magnitude = np.abs(stft)
            phase = np.angle(stft)
            
            # Enhance speech frequencies (300Hz - 3400Hz)
            freqs = librosa.fft_frequencies(sr=sample_rate, n_fft=2048)
            speech_mask = (freqs >= 300) & (freqs <= 3400)
            
            # Apply gentle enhancement to speech frequencies
            enhanced_magnitude = magnitude.copy()
            enhanced_magnitude[speech_mask] *= 1.2  # 20% boost
            
            # Reconstruct audio
            enhanced_stft = enhanced_magnitude * np.exp(1j * phase)
            enhanced_audio = librosa.istft(enhanced_stft, hop_length=512)
            
            return enhanced_audio
            
        except Exception as e:
            self.logger.error(f"Error in speech enhancement: {str(e)}")
            return audio_data
    
    def normalize_audio(self, audio_data: np.ndarray, target_level: float = -20.0) -> np.ndarray:
        """
        Normalize audio to target level in dB
        
        Args:
            audio_data: Input audio signal
            target_level: Target level in dB
            
        Returns:
            Normalized audio signal
        """
        try:
            # Calculate current RMS level
            rms = np.sqrt(np.mean(audio_data ** 2))
            current_level_db = 20 * np.log10(rms + 1e-10)
            
            # Calculate gain needed
            gain_db = target_level - current_level_db
            gain_linear = 10 ** (gain_db / 20)
            
            # Apply gain with limiting to prevent clipping
            normalized_audio = audio_data * gain_linear
            normalized_audio = np.clip(normalized_audio, -0.95, 0.95)
            
            return normalized_audio
            
        except Exception as e:
            self.logger.error(f"Error in audio normalization: {str(e)}")
            return audio_data
    
    def apply_speech_eq(self, audio_data: np.ndarray, sample_rate: int) -> np.ndarray:
        """
        Apply speech-optimized equalization
        
        Args:
            audio_data: Input audio signal
            sample_rate: Sample rate
            
        Returns:
            Equalized audio signal
        """
        try:
            # Design filters for speech enhancement
            nyquist = sample_rate / 2
            
            # High-pass filter to remove low-frequency noise
            high_cutoff = 80 / nyquist
            b_high, a_high = butter(4, high_cutoff, btype='high')
            
            # Low-pass filter to remove high-frequency noise
            low_cutoff = 8000 / nyquist
            b_low, a_low = butter(4, low_cutoff, btype='low')
            
            # Apply filters
            filtered_audio = filtfilt(b_high, a_high, audio_data)
            filtered_audio = filtfilt(b_low, a_low, filtered_audio)
            
            return filtered_audio
            
        except Exception as e:
            self.logger.error(f"Error in EQ application: {str(e)}")
            return audio_data
    
    def apply_compression(self, audio_data: np.ndarray, 
                         threshold: float = 0.3, ratio: float = 4.0) -> np.ndarray:
        """
        Apply dynamic range compression
        
        Args:
            audio_data: Input audio signal
            threshold: Compression threshold (0.0 to 1.0)
            ratio: Compression ratio
            
        Returns:
            Compressed audio signal
        """
        try:
            # Simple dynamic range compression
            compressed_audio = audio_data.copy()
            
            # Find samples above threshold
            above_threshold = np.abs(compressed_audio) > threshold
            
            # Apply compression to samples above threshold
            compressed_audio[above_threshold] = (
                np.sign(compressed_audio[above_threshold]) * 
                (threshold + (np.abs(compressed_audio[above_threshold]) - threshold) / ratio)
            )
            
            return compressed_audio
            
        except Exception as e:
            self.logger.error(f"Error in compression: {str(e)}")
            return audio_data
    
    def trim_silence(self, audio_data: np.ndarray, sample_rate: int, 
                    silence_threshold: float = 0.01) -> np.ndarray:
        """
        Remove excessive silence from audio
        
        Args:
            audio_data: Input audio signal
            sample_rate: Sample rate
            silence_threshold: Threshold for silence detection
            
        Returns:
            Trimmed audio signal
        """
        try:
            # Use librosa to trim silence
            trimmed_audio, _ = librosa.effects.trim(
                audio_data, 
                top_db=20 * np.log10(silence_threshold)
            )
            
            return trimmed_audio
            
        except Exception as e:
            self.logger.error(f"Error in silence trimming: {str(e)}")
            return audio_data
    
    def calculate_enhancement_metrics(self, original: np.ndarray, enhanced: np.ndarray, 
                                    sample_rate: int) -> Dict:
        """
        Calculate metrics to quantify enhancement quality
        
        Args:
            original: Original audio signal
            enhanced: Enhanced audio signal
            sample_rate: Sample rate
            
        Returns:
            Dictionary of enhancement metrics
        """
        try:
            # Signal-to-noise ratio improvement
            original_snr = self.calculate_snr(original)
            enhanced_snr = self.calculate_snr(enhanced)
            snr_improvement = enhanced_snr - original_snr
            
            # Dynamic range
            original_dynamic_range = np.max(original) - np.min(original)
            enhanced_dynamic_range = np.max(enhanced) - np.min(enhanced)
            
            # Spectral centroid (brightness)
            original_centroid = np.mean(librosa.feature.spectral_centroid(y=original, sr=sample_rate))
            enhanced_centroid = np.mean(librosa.feature.spectral_centroid(y=enhanced, sr=sample_rate))
            
            metrics = {
                'snr_improvement_db': float(snr_improvement),
                'original_snr_db': float(original_snr),
                'enhanced_snr_db': float(enhanced_snr),
                'original_dynamic_range': float(original_dynamic_range),
                'enhanced_dynamic_range': float(enhanced_dynamic_range),
                'original_spectral_centroid': float(original_centroid),
                'enhanced_spectral_centroid': float(enhanced_centroid)
            }
            
            return metrics
            
        except Exception as e:
            self.logger.error(f"Error calculating metrics: {str(e)}")
            return {}
    
    def calculate_snr(self, audio_data: np.ndarray) -> float:
        """Calculate signal-to-noise ratio"""
        try:
            # Simple SNR calculation
            signal_power = np.mean(audio_data ** 2)
            noise_power = np.var(audio_data)  # Approximation
            snr_db = 10 * np.log10(signal_power / (noise_power + 1e-10))
            return snr_db
        except:
            return 0.0
